package io.terminus.trantor2.datasource.connection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 业务数据源上下文
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceContext {
    /**
     * 数据源
     */
    private DataSource dataSource;

    /**
     * JdbcTemplate
     *
     * @return JdbcTemplate
     */
    public JdbcTemplate getJdbcTemplate() {
        return new JdbcTemplate(this.dataSource);
    }

    /**
     * equals
     *
     * @param obj
     * @return
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (!(obj instanceof DataSourceContext)) {
            return false;
        }

        // 当dataSource为同一对象时，表示两个DataSourceContext相同
        DataSourceContext other = (DataSourceContext) obj;
        return dataSource == other.dataSource;
    }

    /**
     * hashcode方法
     *
     * @return
     */
    @Override
    public int hashCode() {
        return dataSource.hashCode();
    }

}
