package io.terminus.trantor2.datasource.connection;

import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.datasource.dialect.MultiDbDataSourceProcessorTemplate;
import io.terminus.trantor2.datasource.exception.BusinessDataSourceException;
import io.terminus.trantor2.datasource.model.DataSourceConfigDTO;
import io.terminus.trantor2.datasource.model.SupportDialectTypeEnum;
import io.terminus.trantor2.datasource.model.sharding.TableShardingConfig;
import io.terminus.trantor2.datasource.util.DataSourceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.algorithm.core.config.AlgorithmConfiguration;
import org.apache.shardingsphere.sharding.api.config.ShardingRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.rule.ShardingTableRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.sharding.ComplexShardingStrategyConfiguration;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;

import static io.terminus.trantor2.datasource.constants.DataSourceConst.BUSINESS_DML_DATA_SOURCE_NAME;

/**
 * 数据源上下文工具类
 */
@Slf4j
public final class DataSourceContextHelper {

    private DataSourceContextHelper() {

    }

    /**
     * 创建数据源上下文
     *
     * @param sourceUniqueKey          sourceUniqueKey
     * @param dataSourceConfig         数据源配置
     * @param dataSourcePoolName       自定义连接池名称
     * @param multiDbProcessorTemplate 多数据库类型支持
     * @return DataSourceContext
     */
    public static DataSourceContext createDataSourceContext(String sourceUniqueKey,
                                                            DataSourceConfigDTO dataSourceConfig,
                                                            String dataSourcePoolName,
                                                            MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate,
                                                            List<TableShardingConfig> tableShardingConfigs,
                                                            boolean shardingSphereSqlShow) {
        //创建垂直分库DataSourceContext
        if (SupportDialectTypeEnum.ORACLE.name().equalsIgnoreCase(dataSourceConfig.getDbType().name())) {
            return createDataSourceContext(sourceUniqueKey, dataSourceConfig.getDbUsername(), dataSourceConfig,
                dataSourcePoolName, multiDbProcessorTemplate, tableShardingConfigs, shardingSphereSqlShow);
        } else {
            return createDataSourceContext(sourceUniqueKey, dataSourceConfig.getDbName(), dataSourceConfig,
                dataSourcePoolName, multiDbProcessorTemplate, tableShardingConfigs, shardingSphereSqlShow);
        }
    }

    private static DataSourceContext createDataSourceContext(String sourceUniqueKey,
                                                             String oracleUserNameOrMySqlSchema,
                                                             DataSourceConfigDTO dataSourceConfig,
                                                             String dataSourcePoolName,
                                                             MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate,
                                                             List<TableShardingConfig> tableShardingConfigs,
                                                             boolean shardingSphereSqlShow) {
        DataSource dataSource;
        DataSourceContext context;
        try {
            dataSource = DataSourceHelper.createDataSource(oracleUserNameOrMySqlSchema,
                dataSourceConfig, dataSourcePoolName,
                SupportDialectTypeEnum.isDRDS(dataSourceConfig.getDbType().name()),
                multiDbProcessorTemplate);

            // 创建数据源上下文，同时如果数据源开启了分库，将使用sharding数据源代理
            context = _createDataSourceContext(sourceUniqueKey, dataSource, dataSourceConfig,
                shardingSphereSqlShow, tableShardingConfigs, multiDbProcessorTemplate);
        } catch (Exception e) {
            String errMsg = "fail to create datasource:" + sourceUniqueKey + ",dbConfig:" + dataSourceConfig;
            log.error(errMsg, e);
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_UNKNOWN_ERROR, errMsg, new Object[]{errMsg});
        }
        return context;
    }

    /**
     * 数据源上下文封装
     *
     * @param sourceUniqueKey
     * @param dataSource
     * @param dataSourceConfigDTO
     * @param shardingSqlShow
     * @param tableShardingConfigs
     * @param multiDbProcessorTemplate
     * @return
     */
    private static DataSourceContext _createDataSourceContext(String sourceUniqueKey, DataSource dataSource,
                                                              DataSourceConfigDTO dataSourceConfigDTO,
                                                              Boolean shardingSqlShow,
                                                              List<TableShardingConfig> tableShardingConfigs,
                                                              MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate) throws SQLException {
        DataSourceContext context = new DataSourceContext();
        if (noPartitionDb(tableShardingConfigs)) {
            log.info("{} has no table partition will use hikari dataSource...", dataSourceConfigDTO.getDataSourceName());
            context.setDataSource(dataSource);
        } else {
            log.info("{} has table partition will use sharding dataSource...", dataSourceConfigDTO.getDataSourceName());
            // 这里创建sharding数据源
            Properties properties = new Properties();
            if (Boolean.TRUE.equals(shardingSqlShow)) {
                properties.setProperty("sql-show", "true");
            }
            // 所有被sharding代理的数据源
            Map<String, DataSource> datasourceMap = createShardingDataSourceMap(dataSource, dataSourceConfigDTO,
                multiDbProcessorTemplate);
            // sharding分库分表规则组装
            ShardingRuleConfiguration shardingRuleConfiguration = createShardingRuleConfiguration(tableShardingConfigs,
                dataSourceConfigDTO, dataSource, multiDbProcessorTemplate);

            DataSource shardingDataSource = ShardingSphereDataSourceFactory.createDataSource(sourceUniqueKey,
                datasourceMap, Collections.singletonList(shardingRuleConfiguration), properties);

            context.setDataSource(shardingDataSource);
        }
        return context;
    }

    public static ShardingRuleConfiguration createShardingRuleConfiguration(List<TableShardingConfig> tableShardingConfigs,
                                                                            DataSourceConfigDTO dataSourceConfigDTO,
                                                                            DataSource dataSource,
                                                                            MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate) {
        ShardingRuleConfiguration result = new ShardingRuleConfiguration();

        // 分库分表规则
        List<ShardingTableRuleConfiguration> tableRuleConfigurations = generateTableRuleConfiguration(tableShardingConfigs,
            dataSourceConfigDTO);
        result.getTables().addAll(tableRuleConfigurations);

        // 分库分表算法策略
        Map<String, AlgorithmConfiguration> shardingAlgorithms = generateAlgorithmConfiguration(dataSourceConfigDTO, tableShardingConfigs);
        result.getShardingAlgorithms().putAll(shardingAlgorithms);

        // 默认数据源下所有的物理表需要使用默认分库分表规则
        List<ShardingTableRuleConfiguration> defaultTableRuleConfigurations =
            getDefaultShardingTableRuleConfiguration(dataSourceConfigDTO, dataSource, multiDbProcessorTemplate, tableShardingConfigs);
        result.getTables().addAll(defaultTableRuleConfigurations);

        return result;
    }

    private static List<ShardingTableRuleConfiguration> getDefaultShardingTableRuleConfiguration(DataSourceConfigDTO dataSourceConfigDTO,
                                                                                                 DataSource dataSource,
                                                                                                 MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate,
                                                                                                 List<TableShardingConfig> tableShardingConfigs) {
        List<ShardingTableRuleConfiguration> shardingTableRuleConfigurationList = new ArrayList<>();
        List<String> useDefaultSharingRuleTables = getUseDefaultSharingRuleTables(dataSourceConfigDTO, multiDbProcessorTemplate,
            dataSource, tableShardingConfigs);
        log.info("datasource:{} find tables:{} will use default sharding rule...", dataSourceConfigDTO.getDataSourceName(), useDefaultSharingRuleTables);
        useDefaultSharingRuleTables.forEach(tableName -> {
            ShardingTableRuleConfiguration defaultTableRule = new ShardingTableRuleConfiguration(tableName,
                dataSourceConfigDTO.getDataSourceName() + "_0." + tableName);
            shardingTableRuleConfigurationList.add(defaultTableRule);
        });
        return shardingTableRuleConfigurationList;
    }

    private static Map<String, AlgorithmConfiguration> generateAlgorithmConfiguration(DataSourceConfigDTO dataSourceConfigDTO,
                                                                                      List<TableShardingConfig> tableShardingConfigs) {

        Map<String, AlgorithmConfiguration> algorithmConfigurationMap = new HashMap<>();
        for (TableShardingConfig tableShardingConfig : tableShardingConfigs) {
            String logicTable = tableShardingConfig.getLogicTable();
            String tableStrategyName = logicTable + "_table";
            String dbStrategyName = logicTable + "_db";

            // 规则算法计算
            Properties tableProperties = new Properties();
            tableProperties.setProperty("strategy", "COMPLEX");
            tableProperties.setProperty("algorithmClassName", "io.terminus.trantor2.datasource.algorithm.TrantorComplexShardingAlgorithm");
            tableProperties.put("tableShardingConfig", JsonUtil.toJson(tableShardingConfig));
            algorithmConfigurationMap.put(tableStrategyName,
                new AlgorithmConfiguration("CLASS_BASED", tableProperties));
        }
        return algorithmConfigurationMap;
    }

    private static List<ShardingTableRuleConfiguration> generateTableRuleConfiguration(List<TableShardingConfig> tableShardingConfigs,
                                                                                       DataSourceConfigDTO dataSourceConfigDTO) {
        List<ShardingTableRuleConfiguration> shardingTableRuleConfigurationList = new ArrayList<>();

        List<String> dbNames = dataSourceConfigDTO.generateShardingDbNames();
        for (TableShardingConfig tableShardingConfig : tableShardingConfigs) {
            // 分表规则，如CESHI_0.test_a_p_${0..1},CESHI_1.test_a_p_${2..3},CESHI_2.test_a_p_${4}
            String actualDataNodes = tableShardingConfig.generateActualDataNodes(dbNames);
            log.info("tableConfig:{} in datasource:{} generate actualDataNodes:{}",
                tableShardingConfig.getLogicTable(), dataSourceConfigDTO.getDataSourceName(), actualDataNodes);

            String logicTable = tableShardingConfig.getLogicTable();
            ShardingTableRuleConfiguration tableSharding = new ShardingTableRuleConfiguration(logicTable, actualDataNodes);

            // 分表字段 id,code
            String shardingColumns = tableShardingConfig.generateShardingColumns();
            log.info("tableConfig:{} in datasource:{} generate shardingColumns:{}",
                tableShardingConfig.getLogicTable(), dataSourceConfigDTO.getDataSourceName(), shardingColumns);
            String tableStrategyName = tableShardingConfig.getLogicTable() + "_table";
            // 指定分表使用的策略
            tableSharding.setTableShardingStrategy(new ComplexShardingStrategyConfiguration(shardingColumns,
                tableStrategyName));
            shardingTableRuleConfigurationList.add(tableSharding);
        }
        return shardingTableRuleConfigurationList;
    }

    private static List<String> getUseDefaultSharingRuleTables(DataSourceConfigDTO dataSourceConfigDTO,
                                                               MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate,
                                                               DataSource dataSource,
                                                               List<TableShardingConfig> tableShardingConfigs) {
        try {
            // 加载库中所有的物理表
            List<String> wholeTables = multiDbProcessorTemplate.getAllTableNameBySchema(dataSourceConfigDTO, dataSource);
            // 排除掉需要分表的模型对应的物理表
            Set<String> partitionTable = new HashSet<>();
            tableShardingConfigs.forEach(config -> {
                partitionTable.addAll(config.generateAllPhysicalTableName(true));
            });
            wholeTables.removeAll(partitionTable);
            return wholeTables;
        } catch (SQLException e) {
            String errorMsg = "fail to getUseDefaultSharingRuleTables error:" + e.getMessage() + ",dbConfig:" + dataSourceConfigDTO;
            log.error(errorMsg, e);
            throw new BusinessDataSourceException(ErrorType.DATASOURCE_UNKNOWN_ERROR, errorMsg, new Object[]{errorMsg});
        }
    }

    private static Map<String, DataSource> createShardingDataSourceMap(DataSource dataSource,
                                                                       DataSourceConfigDTO dataSourceConfigDTO,
                                                                       MultiDbDataSourceProcessorTemplate multiDbProcessorTemplate) {
        List<String> shardingDatasourceNames = dataSourceConfigDTO.generateShardingDbNames();

        Map<String, DataSource> wholePhysicalDatasourceMap = new LinkedHashMap<>();
        wholePhysicalDatasourceMap.put(shardingDatasourceNames.get(0), dataSource);
        if (Boolean.TRUE.equals(dataSourceConfigDTO.getNeedDbPartition())
            && !CollectionUtils.isEmpty(dataSourceConfigDTO.getSubDataSourceDtos())) {
            for (int i = 0; i < dataSourceConfigDTO.getSubDataSourceDtos().size(); i++) {
                DataSourceConfigDTO subDataSourceDto = dataSourceConfigDTO.getSubDataSourceDtos().get(i);
                wholePhysicalDatasourceMap.put(shardingDatasourceNames.get(i + 1),
                    DataSourceHelper.createDataSource(subDataSourceDto.getDbName(),
                        subDataSourceDto, BUSINESS_DML_DATA_SOURCE_NAME,
                        SupportDialectTypeEnum.isDRDS(subDataSourceDto.getDbType().name()),
                        multiDbProcessorTemplate));
            }
        }
        return wholePhysicalDatasourceMap;
    }

    private static boolean noPartitionDb(List<TableShardingConfig> tableShardingConfigs) {
        return CollectionUtils.isEmpty(tableShardingConfigs);
    }

    /**
     * 关闭数据源
     *
     * @param dataSourceContext dataSourceContext
     */
    public static void closeDataSourceContext(DataSourceContext dataSourceContext) {
        if (dataSourceContext == null) {
            return;
        }
        DataSourceHelper.closeDataSource(dataSourceContext.getDataSource());

    }

}
