package io.terminus.trantor2.datasource.connection;

import io.terminus.trantor2.datasource.manager.BusinessDDLDataSourceHolderManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 用于DDL获取JdbcTemplate
 */
@Data
@Slf4j
public class ModuleBusinessDDLJdbcTemplateHolder implements AutoCloseable {
    /**
     * 模块JdbcTemplate
     */
    private Map<String, JdbcTemplate> moduleBusinessDDLJdbcTemplate;

    /**
     * 连接池引用
     */
    private CloseableResourceCacheMap<DataSourceHolder> moduleDataSourceHolderMap;

    public ModuleBusinessDDLJdbcTemplateHolder(CloseableResourceCacheMap<DataSourceHolder> dataSourceHolderMap) {
        this.moduleDataSourceHolderMap = dataSourceHolderMap;
        init(moduleDataSourceHolderMap);
    }

    @Override
    public void close() {
        if (moduleDataSourceHolderMap != null) {
            moduleDataSourceHolderMap.clearCache();
        }
    }

    public void init(CloseableResourceCacheMap<DataSourceHolder> moduleDataSourceHolderMap) {
        if (moduleDataSourceHolderMap == null) {
            return;
        }
        Map<String, JdbcTemplate> jdbcTemplateMap = new HashMap<>();
        moduleDataSourceHolderMap.forEach((key, datasourceHolder) -> {
            DataSourceContext teamDataSourceContext = datasourceHolder.getDataSourceContext();
            jdbcTemplateMap.put(key, new JdbcTemplate(teamDataSourceContext.getDataSource()));
        });

        this.setModuleBusinessDDLJdbcTemplate(jdbcTemplateMap);
    }

    /**
     * ddl执行，保证ddl执行日志风格统一
     */
    public void execute(final String module, final long teamId, final long appId, final String tableName, String sql) {
        execute(module, teamId, appId, tableName, sql, false);
    }

    public void execute(final String module, final long teamId, final long appId, final String tableName, String sql, boolean ignoreException) {
        long start = System.currentTimeMillis();
        long cost;
        try {
            log.info("start execute DDL, hint=DDL, sql={}", sql);
            getJdbctemplate(module).execute(sql);
            cost = System.currentTimeMillis() - start;
            log.info("end execute DDL, cost={}ms, hint=DDL, sql={}", cost, sql);
        } catch (Exception e) {
            cost = System.currentTimeMillis() - start;
            log.error("fail execute DDL, cost={}ms, hint=DDL, err={}, sql={}", cost, e.getMessage(), sql);
            if (!ignoreException) {
                throw e;
            }
        }
    }

    private JdbcTemplate getJdbctemplate(String module) {
        JdbcTemplate jdbcTemplate = moduleBusinessDDLJdbcTemplate.get(module);
        if (null == jdbcTemplate) {
            jdbcTemplate = moduleBusinessDDLJdbcTemplate.get(BusinessDDLDataSourceHolderManager.DDL_DEFAULT_DATA_SOURCE_KEY);
        }
        return jdbcTemplate;
    }
}
