package io.terminus.trantor2.datasource.connection;


import io.terminus.trantor2.datasource.enums.CacheFunctionType;
import io.terminus.trantor2.datasource.log.PrintableTree;
import io.terminus.trantor2.datasource.log.TreeLoggerContext;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 可关闭资源 缓存 ConcurrentHashMap
 * - 线程安全
 *
 * @param <R> 可关闭资源类型
 * <AUTHOR>
 * @since 2020-10-16
 */
@Slf4j
public class CloseableResourceCacheMap<R extends CloseableResource> extends ConcurrentHashMap<String, R> {

    /**
     * 添加缓存
     *
     * @param key      资源Key
     * @param resource 缓存资源
     */
    public synchronized void addCache(@NonNull String key, @NonNull R resource) {
        log.info("addCache key:{}, resource:{}", key, resource);
        super.put(key, resource);
    }

    /**
     * 删除缓存
     *
     * @param key 资源Key
     */
    public synchronized void removeCache(@NonNull String key) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        PrintableTree currentTree = TreeLoggerContext.getPrintableTree(stackTrace);
        try {
            log.info("removeCache key:{}", key);
            R resource = super.remove(key);
            if (resource != null) {
                resource.released();
            }
        } finally {
            currentTree.buildValue(CacheFunctionType.CLEAR, "data source cache", key);
        }

    }

    /**
     * 批量删除缓存
     *
     * @param keys 资源Keys
     */
    public synchronized void removeCaches(@NonNull Set<String> keys) {
        log.info("removeCaches, keys:{}", keys);
        keys.forEach(this::removeCache);
    }

    /**
     * 删除所有缓存
     */
    public synchronized void clearCache() {
        log.info("clearCache");
        keySet().forEach(this::removeCache);
    }

    /**
     * put数据
     *
     * @param key   key
     * @param value value
     * @return the previous value associated with key, or null if there was no mapping for key
     */
    @Override
    public R put(@NonNull String key, @NonNull R value) {
        throw new UnsupportedOperationException("Use addCache method instead");
    }

    /**
     * remove数据
     *
     * @param key key
     * @return
     */
    @Override
    public R remove(@NonNull Object key) {
        throw new UnsupportedOperationException("Use removeCache method instead");
    }

    /**
     * 清空数据
     */
    @Override
    public void clear() {
        throw new UnsupportedOperationException("Use clearCache method instead");
    }

}
