<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor2</groupId>
        <artifactId>trantor-module-engine-runtime</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>trantor-module-engine-runtime-impl</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-module-engine-runtime-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-permission-runtime-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-service-report</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.i18n</groupId>
            <artifactId>terminus-i18n-api</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.apidocs</groupId>
                    <artifactId>swagger2-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-code-engine-runtime-api</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- All In One IDE Test -->
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-ide-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
