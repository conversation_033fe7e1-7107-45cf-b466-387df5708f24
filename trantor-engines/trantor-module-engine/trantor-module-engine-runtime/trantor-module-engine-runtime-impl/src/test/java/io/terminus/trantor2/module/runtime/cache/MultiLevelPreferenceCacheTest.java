package io.terminus.trantor2.module.runtime.cache;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.BookmarkPreferenceData;
import io.terminus.trantor2.module.runtime.entity.NullPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class MultiLevelPreferenceCacheTest extends PreferenceBaseTest {
    @Autowired
    private MultiLevelPreferenceCache cache;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CaffeinePreferenceCache caffeinePreferenceCache;
    private final String redisKeyPrefix = "PREFERENCE:";
    private final PreferenceCacheKey cacheKey = PreferenceCacheKey.builder()
            .teamCode("team")
            .moduleKey("module")
            .type(Preference.PreferenceType.BOOKMARK)
            .userId(1L).build();

    @Test
    @Order(1)
    @SneakyThrows
    void get() {
        PreferenceData data = cache.get(cacheKey);
        assertNotNull(data);
        assertInstanceOf(BookmarkPreferenceData.class, data);
        assertEquals(1, ((BookmarkPreferenceData) data).getBookmarks().size());
        assertEquals("kifI8maR0nbcCUpE1a-2H", ((BookmarkPreferenceData) data).getBookmarks().get(0).getCode());

        TrantorContext.getCurrentUser().setId(123L);
        PreferenceCacheKey nullCacheKey = PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(123L).build();
        PreferenceData nullData = cache.get(nullCacheKey);
        assertNull(nullData);
        Thread.sleep(50);

        String redisKey = redisKeyPrefix + nullCacheKey;
        Object obj = stringRedisTemplate.opsForValue().get(redisKey);
        assertNotNull(obj);
        data = JsonUtil.fromJson(obj.toString(), PreferenceData.class);
        assertInstanceOf(NullPreferenceData.class, data);
    }

    @Test
    @Order(2)
    @SneakyThrows
    void put_notNull() {
        PreferenceData data = cache.get(cacheKey);
        TrantorContext.getCurrentUser().setId(111L);
        PreferenceCacheKey newCacheKey = PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(111L).build();

        cache.put(newCacheKey, data);

        String redisKey = redisKeyPrefix + newCacheKey;
        Object obj = stringRedisTemplate.opsForValue().get(redisKey);
        assertNotNull(obj);
        data = JsonUtil.fromJson(obj.toString(), PreferenceData.class);
        assertInstanceOf(BookmarkPreferenceData.class, data);

        data = caffeinePreferenceCache.get(newCacheKey);
        assertNotNull(data);
    }

    @Test
    @Order(3)
    @SneakyThrows
    void put_null() {
        TrantorContext.getCurrentUser().setId(321L);
        PreferenceCacheKey newCacheKey = PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(321L).build();

        cache.put(newCacheKey, null);

        String redisKey = "PREFERENCE:" + newCacheKey;
        Object obj = stringRedisTemplate.opsForValue().get(redisKey);
        assertNotNull(obj);
        PreferenceData data = JsonUtil.fromJson(obj.toString(), PreferenceData.class);
        assertInstanceOf(NullPreferenceData.class, data);

        PreferenceData ifPresent = caffeinePreferenceCache.getIfPresent(newCacheKey);
        assertNull(ifPresent);
    }

    @Test
    @Order(4)
    void invalidate() {
        cache.invalidate(cacheKey);
        PreferenceData cacheData = caffeinePreferenceCache.getIfPresent(cacheKey);
        assertNull(cacheData);

        String redisKey = "PREFERENCE:" + cacheKey;
        Object obj = stringRedisTemplate.opsForValue().get(redisKey);
        assertNull(obj);
    }

    @Test
    @Order(5)
    void invalidateAll() {
        Set<String> keys = stringRedisTemplate.keys("*");
        assertFalse(CollectionUtils.isEmpty(keys));

        cache.invalidateAll();

        long l = caffeinePreferenceCache.size();
        assertEquals(0, l);
        keys = stringRedisTemplate.keys("*");
        assertTrue(CollectionUtils.isEmpty(keys));
    }
}