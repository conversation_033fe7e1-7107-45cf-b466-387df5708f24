package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.module.runtime.entity.IndicatorPreferenceData;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import io.terminus.trantor2.module.runtime.request.IndicatorPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceQueryRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class IndicatorPreferenceServiceTest extends AbstractPreferenceServiceTest {
    private final String scope = "ERP_GEN$GEN_MAT_MD_VIEW::ERP_GEN$GEN_MAT_MD_VIEW:TV-NOmPABSdZ3TgbXOLHo::material$indicator";

    @Autowired
    private IndicatorPreferenceService service;

    @Test
    void query() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        PreferenceData data = service.query(request);
        assertInstanceOf(IndicatorPreferenceData.class, data);
        assertEquals(2, ((IndicatorPreferenceData) data).getIndicators().size());
        assertEquals("V31KS4YvhNYz2OfDh_rQC", ((IndicatorPreferenceData) data).getIndicators().get(0).getCode());
    }

    @Test
    void create() {
        IndicatorPreferenceSaveRequest request = new IndicatorPreferenceSaveRequest();
        request.setScope("test$abc");
        List<IndicatorPreferenceData.Indicator> indicators = new ArrayList<>();
        IndicatorPreferenceData.Indicator indicator = new IndicatorPreferenceData.Indicator();

        indicator.setCode("abc");
        indicator.setName("abc");
        indicators.add(indicator);
        request.setScope("test$abc::");
        request.setIndicators(indicators);
        service.savePreference(request);

        PreferenceQueryRequest qReq = new PreferenceQueryRequest();
        qReq.setScope("test$abc::");
        PreferenceData data = service.query(qReq);
        assertInstanceOf(IndicatorPreferenceData.class, data);
        assertEquals(1, ((IndicatorPreferenceData) data).getIndicators().size());
        assertEquals("abc", ((IndicatorPreferenceData) data).getIndicators().get(0).getCode());
    }

    @Test
    void update() {
        PreferenceQueryRequest qReq = new PreferenceQueryRequest();
        qReq.setScope(scope);
        PreferenceData data = service.query(qReq);
        assertInstanceOf(IndicatorPreferenceData.class, data);
        assertEquals(2, ((IndicatorPreferenceData) data).getIndicators().size());
        assertEquals("V31KS4YvhNYz2OfDh_rQC", ((IndicatorPreferenceData) data).getIndicators().get(0).getCode());
        IndicatorPreferenceData.Indicator indicator = ((IndicatorPreferenceData) data).getIndicators().get(0);
        indicator.setName("update");

        IndicatorPreferenceSaveRequest request = new IndicatorPreferenceSaveRequest();
        request.setScope(scope);
        request.setIndicators(((IndicatorPreferenceData) data).getIndicators());
        service.savePreference(request);

        assertEquals(2, ((IndicatorPreferenceData) data).getIndicators().size());
        assertEquals("V31KS4YvhNYz2OfDh_rQC", ((IndicatorPreferenceData) data).getIndicators().get(0).getCode());
        assertEquals("update", ((IndicatorPreferenceData) data).getIndicators().get(0).getName());
    }

    @Test
    void delete() {
        assertThrows(UnsupportedOperationException.class, () -> service.delete(null));
    }

    @Test
    @Order(1)
    @DisplayName("场景删除时连带删除引用了场景的指标卡配置")
    void testOnSceneDelete() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        service.onSceneDeleted(teamCode, "ERP_GEN$GEN_MAT_MD_VIEW");
        PreferenceData data = service.query(request);
        assertNull(data);
    }
}