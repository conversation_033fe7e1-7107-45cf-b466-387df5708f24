package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.NavigationPreference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class NavigationPreferenceRepoTest extends PreferenceBaseTest {
    @Autowired
    private NavigationPreferenceRepo repo;
    private final String scope = "TERP_MIGRATE$test_Navigation::list-TERP_MIGRATE$test_Navigation-deeeeeeee";

    @Test
    void testFindByTeamCodeAndModuleKeyAndUserIdAndScope() {
        NavigationPreference preference = repo.findByTeamCodeAndModuleKeyAndUserIdAndScope(
                teamCode,
                moduleKey,
                userId,
                scope);
        assertNotNull(preference);
        assertEquals(14, preference.getId());
    }

    @Test
    void testFindByScopeAndCurUser() {
        NavigationPreference preference = repo.findByScopeAndCurUser(scope);
        assertNotNull(preference);
        assertEquals(14, preference.getId());
        assertEquals(1, preference.getUserId());
    }

    @Test
    void testDeleteAllByTeamCodeAndModuleKeyAndScopeStartingWith() {
        List<NavigationPreference> all = repo.findAll();
        assertEquals(2, all.size());

        repo.deleteAllByTeamCodeAndScopeStartingWith(
                teamCode,
                "TERP_MIGRATE$test_Navigation"
        );

        all = repo.findAll();
        assertEquals(0, all.size());
    }
}