package io.terminus.trantor2.module;

import io.terminus.i18n.api.facade.I18nResourceReadFacade;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.PortalI18nConfig;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.oss.OssAdapter;
import io.terminus.trantor2.module.runtime.adapter.I18nRuntimeAdapter;
import io.terminus.trantor2.module.runtime.cache.PortalI18nCache;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.test.tool.minio.MinioSpringTest;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Arrays;

import static io.terminus.trantor2.module.util.I18nLanguagePackUtils.uploadLanguagePack2OSS;

/**
 * <AUTHOR>
 */
@DataJpaTest(
        includeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.runtime.adapter.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.runtime.aspect.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.oss.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.service.*.*OSS.*"),
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = PortalI18nCache.class),
        },
        properties = "spring.flyway.enabled=false"
)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class I18nTestBase implements MysqlSpringTest, MinioSpringTest {
    @Autowired
    protected I18nRuntimeTarget i18nRuntimeTarget;
    @Autowired
    private OSSService ossService;
    @Autowired
    private OssAdapter ossAdapter;
    @Autowired
    private PortalI18nCache portalI18nCache;
    @MockBean
    private I18nResourceReadFacade i18nResourceReadFacade;

    protected static final String team = "team";
    protected static final String portalCode = "portal";
    protected static final Long userId = 1L;
    protected static final String enUS = "en_US";
    protected static boolean isUploaded = false;
    protected String downloadUrl;

    @BeforeEach
    void init() throws IOException {
        TrantorContext.init();
        TrantorContext.setPortalCode(portalCode);
        TrantorContext.setTeamCode(team);
        TrantorContext.setCurrentPortal(Portal.builder()
                .code(portalCode)
                .i18nConfig(new PortalI18nConfig(true, "zh_CN", Arrays.asList(
                        new PortalI18nConfig.PortalI18nLanguage("zh_CN", "中文"),
                        new PortalI18nConfig.PortalI18nLanguage(enUS, "美国英语")
                )))
                .build()
        );
        User user = new User();
        user.setId(userId);
        TrantorContext.setCurrentUser(user);

        ModuleMeta moduleMeta = new ModuleMeta();
        moduleMeta.setTeamCode(team);
        moduleMeta.setKey(portalCode);

        if (!isUploaded) {
            File file = ResourceUtils.getFile("classpath:i18n/messages_en_US.properties");
            InputStream inputStream = Files.newInputStream(file.toPath());
            MockMultipartFile multipartFile = new MockMultipartFile("messages_en_US", "messages_en_US.properties", "text/x-java-properties", inputStream);
            downloadUrl = uploadLanguagePack2OSS(ossService, multipartFile, enUS, moduleMeta);
            isUploaded = true;
        }
        Portal portal = TrantorContext.getCurrentPortal();
        portal.getI18nConfig().getLanguage(enUS).get().setDownloadUrl(downloadUrl);
        TrantorContext.setLang(enUS);
    }

    @AfterEach
    void tearDown() {
        TrantorContext.clear();
    }
}
