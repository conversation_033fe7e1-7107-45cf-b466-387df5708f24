package io.terminus.trantor2.module;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.utils.BusinessSnowFlake;
import io.terminus.trantor2.console.service.ConsoleService;
import io.terminus.trantor2.datasource.management.controller.ModelDataSourceController;
import io.terminus.trantor2.datasource.management.factory.BusinessDDLDataSourceFactory;
import io.terminus.trantor2.datasource.repository.DataSourceConfigRepository;
import io.terminus.trantor2.iam.mapper.TrantorUserConverter;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.ide.test.IDEHelperBean;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.dto.Manifest;
import io.terminus.trantor2.meta.api.dto.MetaEditAndQueryContext;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.MoveTarget;
import io.terminus.trantor2.meta.api.dto.MoveTargetType;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.blob.MetaBlobRepo;
import io.terminus.trantor2.meta.management.service.EditorMetaEditService;
import io.terminus.trantor2.meta.platform.PlatformConfigHolder;
import io.terminus.trantor2.meta.platform.PlatformVersion;
import io.terminus.trantor2.meta.platform.TrantorVersionService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.model.management.meta.dialect.MultiDbProcessorTemplate;
import io.terminus.trantor2.model.management.meta.dialect.mysql.MySQLDbProcessor;
import io.terminus.trantor2.module.runtime.service.impl.ConfigurationRuntimeServiceImpl;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.service.impl.OSSServiceImpl;
import io.terminus.trantor2.module.service.impl.TeamServiceImpl;
import io.terminus.trantor2.module.util.OSSConstant;
import io.terminus.trantor2.nexus.service.NexusApiClientFactory;
import io.terminus.trantor2.properties.TrantorTaskProperties;
import io.terminus.trantor2.properties.management.ModelManagementProperties;
import io.terminus.trantor2.properties.runtime.ModelRuntimeProperties;
import io.terminus.trantor2.test.tool.ResourceHelper;
import io.terminus.trantor2.test.tool.minio.MinioSpringTest;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import org.apache.logging.log4j.util.Strings;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.function.Executable;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
@DataJpaTest(includeFilters = {
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.properties.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.ide.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.meta.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.oss.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.environment.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.datasource.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.model.management.meta.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.model.management.ddl.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.doc.service.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.service.management.service.impl.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.service.management.validator.*"),
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.service.management.repo.*"),
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
        OSSServiceImpl.class,
        // others end
        // DDL start
        BusinessDDLDataSourceFactory.class,
        MultiDbProcessorTemplate.class,
        MySQLDbProcessor.class,
        // DDL end
        // config start
        ConfigurationRuntimeServiceImpl.class,
        TeamServiceImpl.class,
        TeamServiceImpl.class,
    })
})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.datasource.*"})
@EntityScan(basePackages = {"io.terminus.trantor2.datasource.*"})
@Transactional(propagation = Propagation.NOT_SUPPORTED)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ActiveProfiles("test")
@EnableAsync
@EnableConfigurationProperties({ModelRuntimeProperties.class, ModelManagementProperties.class, TrantorTaskProperties.class})
@Import(MetaBaseIntegrationTests.Config.class)
public class MetaBaseIntegrationTests implements MysqlSpringTest, MinioSpringTest {

    static class Config {

        @Bean
        public MetaCache metaCache(MetaQueryService metaQueryService) {
            return new MetaCache() {
                @Override
                public MetaTreeNodeExt get(String key) {
                    return metaQueryService.queryInTeam(TrantorContext.getTeamCode())
                        .findOne(Field.key().equal(key))
                        .orElse(null);
                }

                @Override
                public List<MetaTreeNodeExt> getAll(Collection<String> keys) {
                    return metaQueryService.queryInTeam(TrantorContext.getTeamCode())
                        .findAll(Field.key().in(keys));
                }

                @Override
                public void clear() {

                }
            };
        }
    }

    @MockBean
    private TrantorIAMUserService trantorIAMUserService;

    @MockBean
    private TrantorUserConverter userConverter;

    @MockBean
    private ModelDataSourceController modelDataSourceController;

    @MockBean
    private IdGenerator idGenerator;

    @MockBean
    private BusinessSnowFlake snowFlake;

    @MockBean
    private RedissonClient redissonClient;

    @MockBean
    private ConsoleService consoleService;

    @MockBean
    private NexusApiClientFactory nexusApiClientFactory;

    @MockBean
    private TrantorVersionService trantorVersionService;

    @MockBean
    private ModuleQueryService moduleQueryService;

    @Autowired
    protected DataSourceConfigRepository dataSourceConfigRepository;

    @Autowired
    protected EditorMetaEditService metaEditService;

    @Autowired
    protected MetaQueryService metaQueryService;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected MetaBlobRepo metaBlobRepo;

    @Autowired
    protected IDEHelperBean ideHelperBean;

    protected LocalDateTime firstTime = LocalDateTime.of(2022, 7, 27, 0, 0, 0);

    protected Long teamId = 1L;
    protected String teamCode = "firstTeam";
    protected Long appId = 2L;
    protected String moduleKey = "module1";
    protected Long userId = 11L;
    protected MetaEditAndQueryContext ctx = new MetaEditAndQueryContext("firstTeam", 1L, 11L);

    @BeforeEach
    public void baseInit(TestInfo info) throws IOException {
        System.out.println(info.getDisplayName());

        // before branch create
        Map<String, Object> modInfo = new HashMap<>(PlatformConfigHolder.getInfoAll());
        modInfo.put(
            PlatformConfigHolder.CONFIG_PLATFORM_VERSION,
            PlatformVersion.of(
                "0.0.0.0",
                "0000000000000000000000000000000000000000000000000000000000000000"
            )
        );
        PlatformConfigHolder.setInfo(modInfo);

        // load behaviors from disk (for test)
        ideHelperBean.loadBehaviors();

        ctx = ideHelperBean.initRepo("meta_test_state/base1__module1", userId);
        teamId = ctx.getTeamId();
        teamCode = ctx.getTeamCode();
        moduleKey = ctx.getModuleKey();
        userId = ctx.getUserId();

        // init context
        ideHelperBean.initCtx(ctx);
    }

    protected void createNewTeam(Long teamId, String teamCode) {
        MetaEditAndQueryContext ctx = new MetaEditAndQueryContext(teamCode, teamId, userId);
        metaEditService.initRepo(ctx);
    }

    protected Long createModule(MetaEditAndQueryContext ctx, String rootKey, String moduleKey) throws IOException {
        return metaEditService.submitOp(ctx,
            EditUtil.createNodeOp(
                easyNodeO("Module", moduleKey, moduleKey + "-name", null, rootKey, null),
                new MoveTarget(rootKey, MoveTargetType.ChildFirst)
            )
        ).get(moduleKey).getId();
    }

    @AfterEach
    public void baseClean() {
        ideHelperBean.clearAll();
    }

    protected MetaTreeNode easyNodeO(String type, String key, String name, String description, String parentKey, ObjectNode props) throws IOException {
        MetaTreeNode node = new MetaTreeNode();
        {
            node.setType(type);
            node.setKey(key);
            node.setName(name);
            node.setDescription(description);
            node.setProps(props);
            node.setParentKey(parentKey);
        }
        return node;
    }

    protected MetaTreeNode easyNode(String type, String key, String name, String description, String parentKey, String props) throws IOException {
        ObjectNode propsNode = null;
        if (Strings.isNotBlank(props)) {
            propsNode = (ObjectNode) ObjectJsonUtil.MAPPER.readTree(props);
        }
        return easyNodeO(type, key, name, description, parentKey, propsNode);
    }

    protected MetaTreeNode easyNode(String type, String key, String name, String parentKey, String props) throws IOException {
        return easyNode(type, key, name, null, parentKey, props);
    }

    protected void assertMetaState(Long teamId, String expectStatePath) {
        ideHelperBean.assertMetaState(teamId, expectStatePath);
    }

    protected Manifest assertArtifactState(String downloadUrl, String expectStatePath) {
        String pfx = "bad state at: " + expectStatePath + ":\n";
        String rootFolder = ResourceHelper.getResourceAsString(this.getClass(), expectStatePath);
        Map<String, String> expectData = new HashMap<>();
        for (String line : rootFolder.split("\n")) {
            String path = expectStatePath + "/" + line;
            if (line.equals("manifest.json")) {
                String content = ResourceHelper.getResourceAsString(getClass(), path);
                expectData.put(line, content);
            } else if (line.equals("objects")) {
                String objectsFolder = ResourceHelper.getResourceAsString(getClass(), path);
                for (String objectFile : objectsFolder.split("\n")) {
                    String objectPath = path + "/" + objectFile;
                    String content = ResourceHelper.getResourceAsString(getClass(), objectPath);
                    expectData.put("objects/" + objectFile, content);
                }
            } else if (line.equals("library")) {
                String libFileName = "library/" + OSSConstant.CONSOLE_FILE_PREFIX;
                path = expectStatePath + "/" + libFileName;
                String libUuidFolder = ResourceHelper.getResourceAsString(getClass(), path);
                for (String libUuidFile : libUuidFolder.split("\n")) {
                    String libUuidPath = path + libUuidFile;
                    String libFiles = ResourceHelper.getResourceAsString(getClass(), libUuidPath);
                    for (String libFile : libFiles.split("\n")) {
                        String libPath = libUuidPath + "/" + libFile;
                        String content = ResourceHelper.getResourceAsString(getClass(), libPath);
                        expectData.put(libFileName + libUuidFile + "/" + libFile, content);
                    }
                }
            }
        }

        Map<String, String> actualData = new HashMap<>();

        HttpURLConnection connection = null;

        try {
            URL url = new URL(downloadUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);

            int responseCode = connection.getResponseCode();
            assertEquals(HttpURLConnection.HTTP_OK, responseCode);

            try (ZipInputStream zis = new ZipInputStream(connection.getInputStream())) {
                ZipEntry entry = zis.getNextEntry();
                while (entry != null) {
                    String fileName = entry.getName();
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }
                    byte[] entireContent = baos.toByteArray();
                    String content = new String(entireContent, StandardCharsets.UTF_8);
                    actualData.put(fileName, content);
                    entry = zis.getNextEntry();
                }
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        List<Executable> all = new ArrayList<>();
        actualData.forEach((key, actual) -> {
            all.add(
                () -> assertTrue(expectData.containsKey(key), pfx + key + " not found in expectData")
            );
        });
        expectData.forEach((key, expectRaw) -> {
            assertTrue(actualData.containsKey(key), pfx + key + " not found in actualData");
            String actual = actualData.get(key);
            String expect;
            if (key.startsWith("objects/") || key.equals("manifest.json")) {
                expect = ObjectJsonUtil.serialize(ObjectJsonUtil.deserialize(expectRaw));
            } else {
                expect = expectRaw;
            }
            all.add(
                () -> assertEquals(expect, actual, pfx + "content not match: " + key +
                    "\nexpect: \n" + expect + "\nactual: \n" + actual + "\n")
            );
        });
        assertAll(all);
        try {
            return ObjectJsonUtil.MAPPER.readValue(actualData.get("manifest.json"), Manifest.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
