package io.terminus.trantor2.module.runtime.aspect;

import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import io.terminus.trantor2.module.I18nTestBase;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.test.tool.ResourceHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */
class MenuRuntimeQueryAspectTest extends I18nTestBase {
    @Autowired
    private MenuRuntimeQueryAspect menuRuntimeQueryAspect;

    @Test
    void enhanceI18nMenu() {
        MenuTreeMeta menuTree = ResourceHelper.readValueFromResource(ObjectJsonUtil.MAPPER, getClass(), "json/menu_tree.json", MenuTreeMeta.class);
        Map<String, String> key2Label = menuTree.flatMenuAndChildrenRecursively().stream()
                .collect(Collectors.toMap(MenuMeta::getKey, MenuMeta::getLabel));

        assertEquals("文件夹", key2Label.get("portal$_RR6BR"));
        assertEquals("分组", key2Label.get("portal$B4_Ypc"));
        assertEquals("子菜单", key2Label.get("portal$Tn0I8Z"));
        assertEquals("场景", key2Label.get("portal$Z1wqU5"));
        assertEquals("子文件夹", key2Label.get("portal$0QBTL-"));

        menuRuntimeQueryAspect.enhanceI18nMenu(menuTree);

        key2Label = menuTree.flatMenuAndChildrenRecursively().stream()
                .collect(Collectors.toMap(MenuMeta::getKey, MenuMeta::getLabel));

        assertEquals("folder", key2Label.get("portal$_RR6BR"));
        assertEquals("group", key2Label.get("portal$B4_Ypc"));
        assertEquals("sub menu", key2Label.get("portal$Tn0I8Z"));
        assertEquals("scene", key2Label.get("portal$Z1wqU5"));
        assertEquals("sub folders", key2Label.get("portal$0QBTL-"));
    }
}