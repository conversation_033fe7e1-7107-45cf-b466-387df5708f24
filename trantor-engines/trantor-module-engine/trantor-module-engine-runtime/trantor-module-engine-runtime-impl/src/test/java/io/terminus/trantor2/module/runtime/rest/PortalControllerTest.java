package io.terminus.trantor2.module.runtime.rest;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.dto.PortalDTO;
import io.terminus.trantor2.module.oss.OssAdapter;
import io.terminus.trantor2.module.oss.TrantorOssConfig;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.module.runtime.service.v2.PortalServiceV2;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.service.impl.OSSServiceImpl;
import io.terminus.trantor2.properties.TrantorTaskProperties;
import io.terminus.trantor2.properties.management.ModelManagementProperties;
import io.terminus.trantor2.properties.runtime.ModelRuntimeProperties;
import io.terminus.trantor2.test.tool.minio.MinioSpringTest;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 **/
@DataJpaTest(includeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
                PortalController.class,
                TrantorOssConfig.class,
                OSSServiceImpl.class,
                OssAdapter.class,
        })
})
@Transactional(propagation = Propagation.NOT_SUPPORTED)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ActiveProfiles("test")
@EnableAsync
@EnableConfigurationProperties({ModelRuntimeProperties.class, ModelManagementProperties.class, TrantorTaskProperties.class})
public class PortalControllerTest implements MysqlSpringTest, MinioSpringTest {
    @Autowired
    private PortalController portalController;
    @MockBean
    PortalService portalService;
    @MockBean
    PortalServiceV2 portalServiceV2;
    @MockBean
    private ModuleQueryService moduleService;
    @MockBean
    private ConfigurationService configurationService;

    @Test
    public void test() {
        TrantorContext.init();
        Portal portal = new Portal();
        portal.setTeamId(1L);
        portal.setTeamCode("teamCode");
        portal.setCode("test");
        TrantorContext.setCurrentPortal(portal);
        Response<PortalDTO> portalDTOResponse = portalController.currentPortal();
        Assertions.assertNotNull(portalDTOResponse);
        Assertions.assertNotNull(portalDTOResponse.getData());

        PortalDTO data = portalDTOResponse.getData();
        Assertions.assertNotNull(data.getOssPrivateEndpoints());
        Assertions.assertEquals(2, data.getOssPrivateEndpoints().size());
        Assertions.assertEquals(minioContainer.getS3URL() + "/test-pri1", data.getOssPrivateEndpoints().get(0));
        Assertions.assertEquals("http://test.com/Bucket1", data.getOssPrivateEndpoints().get(1));
    }
}
