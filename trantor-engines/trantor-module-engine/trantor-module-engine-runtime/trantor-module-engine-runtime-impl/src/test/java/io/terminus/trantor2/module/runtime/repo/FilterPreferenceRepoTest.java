package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.FilterPreference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class FilterPreferenceRepoTest extends PreferenceBaseTest {
    @Autowired
    private FilterPreferenceRepo repo;

    @Test
    void testFindByTeamCodeAndModuleKeyAndUserIdAndScope() {
        FilterPreference filter = repo.findByTeamCodeAndModuleKeyAndUserIdAndScope(
                teamCode,
                moduleKey,
                userId,
                "TERP_MIGRATE$sls_ro_TERP_MIGRATE$sls_ro-list_TERP_MIGRATE$sls_so_head_tr_TERP_MIGRATE$SYS_PagingDataService"
        );
        assertNotNull(filter);
        assertEquals(10, filter.getId());
    }

    @Test
    void testFindByScopeAndCurUser() {
        FilterPreference filter = repo.findByScopeAndCurUser("TERP_MIGRATE$sls_ro_TERP_MIGRATE$sls_ro-list_TERP_MIGRATE$sls_so_head_tr_TERP_MIGRATE$SYS_PagingDataService");
        assertNotNull(filter);
        assertEquals(10, filter.getId());
        assertEquals(1, filter.getUserId());
    }

    @Test
    void testDeleteAllByTeamCodeAndModuleKeyAndScopeStartingWith() {
        List<FilterPreference> allByCurUser = repo.findAllByCurUser();
        assertEquals(2, allByCurUser.size());
        repo.deleteAllByTeamCodeAndScopeStartingWith(
                teamCode,
                "TERP_MIGRATE$sls_ro"
        );
        allByCurUser = repo.findAllByCurUser();
        assertEquals(0, allByCurUser.size());
    }
}