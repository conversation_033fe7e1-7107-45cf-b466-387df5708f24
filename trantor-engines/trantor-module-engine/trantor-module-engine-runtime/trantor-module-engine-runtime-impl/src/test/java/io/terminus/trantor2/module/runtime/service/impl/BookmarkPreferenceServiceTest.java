package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.module.runtime.entity.BookmarkPreferenceData;
import io.terminus.trantor2.module.runtime.request.BookmarkPreferenceDeleteRequest;
import io.terminus.trantor2.module.runtime.request.BookmarkPreferenceQueryRequest;
import io.terminus.trantor2.module.runtime.request.BookmarkPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceQueryRequest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class BookmarkPreferenceServiceTest extends AbstractPreferenceServiceTest {

    @Autowired
    private BookmarkPreferenceService service;

    @Test
    void testQuery_All() {
        BookmarkPreferenceData data = service.query(new PreferenceQueryRequest());
        assertNotNull(data);
        assertEquals(1, data.getBookmarks().size());
    }

    @Test
    void testQuery_ByRef() {
        BookmarkPreferenceQueryRequest request = new BookmarkPreferenceQueryRequest();
        BookmarkPreferenceData.ViewReference viewReference = new BookmarkPreferenceData.ViewReference();
        viewReference.setSceneKey("test$abc");
        viewReference.setViewKey("test$abc-list");
        request.setRef(viewReference);

        BookmarkPreferenceData data = service.query(request);
        assertEquals(0, data.getBookmarks().size());

        viewReference.setSceneKey("ERP_GEN$GEN_MAT_MD_VIEW");
        viewReference.setViewKey("ERP_GEN$GEN_MAT_MD_VIEW-list");
        data = service.query(request);
        assertNotNull(data);
        assertEquals(1, data.getBookmarks().size());
    }

    @Test
    @SneakyThrows
    void testCreate() {
        BookmarkPreferenceSaveRequest req = new BookmarkPreferenceSaveRequest();
        BookmarkPreferenceData.ViewReference viewReference = new BookmarkPreferenceData.ViewReference();
        viewReference.setSceneKey("test$abc");
        viewReference.setViewKey("test$abc-list");
        req.setRef(viewReference);
        req.setName("书签");
        req.setType(BookmarkPreferenceData.BookmarkType.VIEW);

        service.savePreference(req);
        Thread.sleep(100);

        BookmarkPreferenceQueryRequest request = new BookmarkPreferenceQueryRequest();
        request.setRef(viewReference);
        BookmarkPreferenceData data = service.query(request);
        assertEquals(1, data.getBookmarks().size());
        assertEquals("书签", data.getBookmarks().get(0).getName());
    }

    @Test
    @SneakyThrows
    void testUpdate() {
        BookmarkPreferenceData.ViewReference viewReference = new BookmarkPreferenceData.ViewReference();
        viewReference.setSceneKey("ERP_GEN$GEN_MAT_MD_VIEW");
        viewReference.setViewKey("ERP_GEN$GEN_MAT_MD_VIEW-list");
        BookmarkPreferenceQueryRequest request = new BookmarkPreferenceQueryRequest();
        request.setRef(viewReference);
        BookmarkPreferenceData data = service.query(request);
        assertEquals(1, data.getBookmarks().size());
        assertEquals("物料主数据管理", data.getBookmarks().get(0).getName());

        BookmarkPreferenceSaveRequest req = new BookmarkPreferenceSaveRequest();
        req.setBookmarkCode(data.getBookmarks().get(0).getCode());
        req.setName("书签");
        req.setRef(viewReference);
        req.setType(BookmarkPreferenceData.BookmarkType.VIEW);
        service.savePreference(req);
        Thread.sleep(100);

        data = service.query(request);
        assertEquals(1, data.getBookmarks().size());
        assertEquals("书签", data.getBookmarks().get(0).getName());
    }

    @Test
    @SneakyThrows
    void testDelete() {
        BookmarkPreferenceData data = service.query(new PreferenceQueryRequest());
        assertNotNull(data);
        assertEquals(1, data.getBookmarks().size());

        BookmarkPreferenceDeleteRequest request = new BookmarkPreferenceDeleteRequest();
        request.setBookmarkCode("kifI8maR0nbcCUpE1a-2H");
        service.delete(request);
        Thread.sleep(100);

        data = service.query(new PreferenceQueryRequest());
        assertNull(data);
    }
}