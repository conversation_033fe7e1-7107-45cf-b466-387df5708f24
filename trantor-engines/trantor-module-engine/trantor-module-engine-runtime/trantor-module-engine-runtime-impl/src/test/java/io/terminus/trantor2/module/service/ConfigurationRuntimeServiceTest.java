package io.terminus.trantor2.module.service;

import io.terminus.trantor2.module.MetaBaseIntegrationTests;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.meta.SystemConfig;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 **/
public class ConfigurationRuntimeServiceTest extends MetaBaseIntegrationTests {
    @Autowired
    private ConfigurationService configurationService;

    @Test
    public void test() {
        ModuleIamConfig iamConfig = new ModuleIamConfig();
        iamConfig.setApplicationKey("test");
        configurationService.save(iamConfig, 1L, "moduleKey", ConfigType.Module_IAM);

        SystemConfig systemConfig = new SystemConfig();
        systemConfig.setName("test");
        configurationService.save(systemConfig, 1L, "moduleKey", ConfigType.System_Config);

        ModuleIamConfig queryIamConfig = configurationService.query(1L, "moduleKey", ConfigType.Module_IAM);
        Assertions.assertNotNull(queryIamConfig);
        Assertions.assertEquals(queryIamConfig.getApplicationKey(), "test");

        SystemConfig querySystemConfig = configurationService.query(1L, "moduleKey", ConfigType.System_Config);
        Assertions.assertNotNull(querySystemConfig);
        Assertions.assertEquals(querySystemConfig.getName(), "test");
    }
}
