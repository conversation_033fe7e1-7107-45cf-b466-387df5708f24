package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.module.runtime.entity.FilterPreferenceData;
import io.terminus.trantor2.module.runtime.request.FilterPreferenceDeleteRequest;
import io.terminus.trantor2.module.runtime.request.FilterPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceQueryRequest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
public class FilterPreferenceServiceTest extends AbstractPreferenceServiceTest {
    private final String scope = "TERP_MIGRATE$sls_ro_TERP_MIGRATE$FIN_CM_PN_REC-list_TERP_MIGRATE$fin_cm_pn_head_tr_SYS_PagingDataService";

    @Autowired
    private FilterPreferenceService service;

    @Test
    @DisplayName("查询")
    public void testQuery() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        FilterPreferenceData data = service.query(request);

        assertNotNull(data);
        assertEquals(4, data.getFilters().size());
        assertEquals("da91f967-ba50-42b7-8ab6-7e4178e34c49", data.getFilters().get(0).getCode());
    }

    @Test
    @DisplayName("创建")
    @SneakyThrows
    public void testSave() {
        String scope = "TEST$scene::view" + System.currentTimeMillis();
        assertNull(service.getCurUserPreferenceInScope(scope));

        FilterPreferenceSaveRequest filterPreferenceSaveRequest = new FilterPreferenceSaveRequest();
        filterPreferenceSaveRequest.setDefaultFilter(true);
        filterPreferenceSaveRequest.setName("abc");
        filterPreferenceSaveRequest.setScope(scope);
        service.savePreference(filterPreferenceSaveRequest);

        Thread.sleep(100);
        List<FilterPreferenceData.Filter> filters = service.getCurUserPreferenceInScope(scope);
        assertNotNull(filters);
        assertEquals(1, filters.size());
        assertEquals("abc", filters.get(0).getName());
    }

    @Test
    @DisplayName("更新")
    @SneakyThrows
    public void testUpdate() {
        List<FilterPreferenceData.Filter> filters = service.getCurUserPreferenceInScope(scope);
        assertNotNull(filters);
        assertEquals("初始化方案", filters.get(0).getName());
        assertEquals(4, filters.size());

        FilterPreferenceSaveRequest filterPreferenceSaveRequest = new FilterPreferenceSaveRequest();
        filterPreferenceSaveRequest.setDefaultFilter(true);
        filterPreferenceSaveRequest.setName("xyz");
        filterPreferenceSaveRequest.setCode("da91f967-ba50-42b7-8ab6-7e4178e34c49");
        filterPreferenceSaveRequest.setScope(scope);
        service.savePreference(filterPreferenceSaveRequest);

        Thread.sleep(100);
        filters = service.getCurUserPreferenceInScope(scope);
        assertNotNull(filters);
        assertEquals(4, filters.size());
        assertEquals("xyz", filters.get(0).getName());
    }

    @Test
    @DisplayName("删除")
    @SneakyThrows
    @Order(Integer.MAX_VALUE)
    void testDelete() {
        List<FilterPreferenceData.Filter> filters = service.getCurUserPreferenceInScope(scope);
        assertNotNull(filters);
        assertEquals(4, filters.size());

        FilterPreferenceDeleteRequest request = new FilterPreferenceDeleteRequest();
        request.setCode("da91f967-ba50-42b7-8ab6-7e4178e34c49");
        request.setScope(scope);
        service.delete(request);

        Thread.sleep(100);
        filters = service.getCurUserPreferenceInScope(scope);
        assertNotNull(filters);
        assertEquals(3, filters.size());
    }

    @Test
    @Order(Integer.MAX_VALUE)
    @DisplayName("场景删除时连带删除引用了场景的用户过滤器")
    void testOnSceneDelete() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        service.onSceneDeleted(teamCode, "TERP_MIGRATE$sls_ro");
        FilterPreferenceData data = service.query(request);
        assertNull(data);
    }
}