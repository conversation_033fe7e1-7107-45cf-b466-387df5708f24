package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.module.runtime.entity.NavigationPreferenceData;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import io.terminus.trantor2.module.runtime.request.NavigationPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceQueryRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class NavigationPreferenceServiceTest extends AbstractPreferenceServiceTest {
    private final String scope = "TERP_MIGRATE$test_Navigation::list-TERP_MIGRATE$test_Navigation-zrKcPDK_ZN4U5CXHzM1Rk";
    @Autowired
    private NavigationPreferenceService service;

    @Test
    void query() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        PreferenceData data = service.query(request);
        assertInstanceOf(NavigationPreferenceData.class, data);
        assertEquals(10, ((NavigationPreferenceData) data).getNavigations().size());
        assertEquals("_gwXDNFqne-NU8oI6zgLc", ((NavigationPreferenceData) data).getNavigations().get(0).getCode());
    }

    @Test
    void create() {
        NavigationPreferenceSaveRequest request = new NavigationPreferenceSaveRequest();
        request.setScope("test$abc");
        List<NavigationPreferenceData.Navigation> navigations = new ArrayList<>();
        NavigationPreferenceData.Navigation navigation = new NavigationPreferenceData.Navigation();

        navigation.setCode("abc");
        navigation.setName("abc");
        navigations.add(navigation);
        request.setScope("test$abc::");
        request.setNavigations(navigations);
        service.savePreference(request);

        PreferenceQueryRequest qReq = new PreferenceQueryRequest();
        qReq.setScope("test$abc::");
        PreferenceData data = service.query(qReq);
        assertInstanceOf(NavigationPreferenceData.class, data);
        assertEquals(1, ((NavigationPreferenceData) data).getNavigations().size());
        assertEquals("abc", ((NavigationPreferenceData) data).getNavigations().get(0).getCode());
    }

    @Test
    void update() {
        PreferenceQueryRequest qReq = new PreferenceQueryRequest();
        qReq.setScope(scope);
        PreferenceData data = service.query(qReq);
        assertInstanceOf(NavigationPreferenceData.class, data);
        assertEquals(10, ((NavigationPreferenceData) data).getNavigations().size());
        assertEquals("_gwXDNFqne-NU8oI6zgLc", ((NavigationPreferenceData) data).getNavigations().get(0).getCode());
        NavigationPreferenceData.Navigation navigation = ((NavigationPreferenceData) data).getNavigations().get(0);
        navigation.setName("update");

        NavigationPreferenceSaveRequest request = new NavigationPreferenceSaveRequest();
        request.setScope(scope);
        request.setNavigations(((NavigationPreferenceData) data).getNavigations());
        service.savePreference(request);

        assertEquals(10, ((NavigationPreferenceData) data).getNavigations().size());
        assertEquals("_gwXDNFqne-NU8oI6zgLc", ((NavigationPreferenceData) data).getNavigations().get(0).getCode());
        assertEquals("update", ((NavigationPreferenceData) data).getNavigations().get(0).getName());
    }

    @Test
    void delete() {
        assertThrows(UnsupportedOperationException.class, () -> service.delete(null));
    }

    @Test
    @Order(1)
    @DisplayName("场景删除时连带删除引用了场景的导航面板配置")
    void testOnSceneDelete() {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        service.onSceneDeleted(teamCode, "TERP_MIGRATE$test_Navigation");
        PreferenceData data = service.query(request);
        assertNull(data);
    }
}