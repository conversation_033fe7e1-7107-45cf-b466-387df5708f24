package io.terminus.trantor2.module.runtime;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.test.tool.mysql.MysqlSpringTest;
import io.terminus.trantor2.test.tool.redis.RedisSpringTest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJson;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;

/**
 * <AUTHOR>
 */
@DataJpaTest(includeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "io.terminus.trantor2.module.runtime.*.*Preference.*"),
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = RuntimeModuleAutoConfig.class)
})
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@AutoConfigureJson
@Import(PreferenceTestConfig.class)
@Sql("/sql/preferences.sql")
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public abstract class PreferenceBaseTest implements MysqlSpringTest, RedisSpringTest {
    public static final String teamCode = "terp_migrate";
    public static final String moduleKey = "TERP_MIGRATE_PORTAL";
    public static final Long userId = 1L;
    public static final User user = new User();

    static {
        user.setId(userId);
    }

    @BeforeEach
    public void setUp() {
        TrantorContext.init();
        TrantorContext.setPortalCode(moduleKey);
        TrantorContext.setTeamCode(teamCode);
        user.setId(userId);
        TrantorContext.setCurrentUser(user);
    }

    @AfterEach
    @SneakyThrows
    public void tearDown() {
        TrantorContext.clear();
    }

}