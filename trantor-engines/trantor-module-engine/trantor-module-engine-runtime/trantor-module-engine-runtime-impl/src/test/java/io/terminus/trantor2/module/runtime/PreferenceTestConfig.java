package io.terminus.trantor2.module.runtime;

import io.terminus.i18n.api.facade.I18nResourceReadFacade;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.module.service.OSSService;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */
@TestConfiguration
@EntityScan(basePackages = {"io.terminus.trantor2.module.runtime"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.module.runtime"})
@Import({RedisAutoConfiguration.class, RedissonAutoConfigurationV2.class, RuntimeModuleAutoConfig.class})
public class PreferenceTestConfig {
    @MockBean
    protected MetaCache metaCache;
    @MockBean
    protected I18nResourceReadFacade i18nResourceReadFacade;
    @MockBean
    protected OSSService ossService;
}
