package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.BookmarkPreference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 */
class BookmarkPreferenceRepoTest extends PreferenceBaseTest {
    @Autowired
    private BookmarkPreferenceRepo repo;

    @Test
    void testFindByTeamCodeAndModuleKeyAndUserIdAndScope() {
        BookmarkPreference preference = repo.findByTeamCodeAndModuleKeyAndUserIdAndScope(
                teamCode,
                moduleKey,
                userId,
                null);
        assertNotNull(preference);
        assertEquals(15, preference.getId());
        assertEquals(1, preference.getData().getBookmarks().size());
    }

    @Test
    void testFindByScopeAndCurUser() {
        BookmarkPreference preference = repo.findByCurUser();
        assertNotNull(preference);
        assertEquals(15, preference.getId());
        assertEquals(1, preference.getUserId());
    }
}