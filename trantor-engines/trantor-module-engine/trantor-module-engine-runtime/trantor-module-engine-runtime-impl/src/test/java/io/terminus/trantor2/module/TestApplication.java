package io.terminus.trantor2.module;

import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.editor.event.publish.RedisMetaEventPublisher;
import io.terminus.trantor2.module.adapter.IamManagerAdapter;
import org.mockito.Mockito;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * 2023/5/23 2:38 PM
 */
@SpringBootApplication(scanBasePackages = {"io.terminus.trantor2"})
@EntityScan(basePackages = {"io.terminus.trantor2.console", "io.terminus.trantor2.meta", "io.terminus.trantor2.module"})
@EnableJpaRepositories(basePackages = {"io.terminus.trantor2.console", "io.terminus.trantor2.meta", "io.terminus.trantor2.module"})
public class TestApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }

    @Bean
    public RedisMetaEventPublisher metaEventPublisher() {
        return Mockito.mock(RedisMetaEventPublisher.class);
    }

    @Bean
    public RedisTemplate redisTemplate() {
        return Mockito.mock(RedisTemplate.class);
    }

    @Bean
    public IamManagerAdapter iamManagerAdapter() {
        return Mockito.mock(IamManagerAdapter.class);
    }

    @Bean
    public IAMClient iamClient() {
        return Mockito.mock(IAMClient.class);
    }

    @Bean
    public TrantorIAMUserService userService() {
        return Mockito.mock(TrantorIAMUserService.class);
    }
}
