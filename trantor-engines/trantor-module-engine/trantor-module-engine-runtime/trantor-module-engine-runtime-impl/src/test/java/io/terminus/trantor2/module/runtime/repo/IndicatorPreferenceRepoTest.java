package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.IndicatorPreference;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * {@link IndicatorPreferenceRepo}
 *
 * <AUTHOR>
 */

class IndicatorPreferenceRepoTest extends PreferenceBaseTest {
    @Autowired
    private IndicatorPreferenceRepo repo;

    @Test
    void testFindByTeamCodeAndModuleKeyAndUserIdAndScope() {
        IndicatorPreference preference = repo.findByTeamCodeAndModuleKeyAndUserIdAndScope(
                teamCode,
                moduleKey,
                userId,
                "preview$scene_customer_01::preview$scene_customer_01-list-preview$crm_customer_01::indicatorKey");
        assertNotNull(preference);
        assertEquals(17, preference.getId());
    }

    @Test
    void testFindByScopeAndCurUser() {
        IndicatorPreference preference = repo.findByScopeAndCurUser("preview$scene_customer_01::preview$scene_customer_01-list-preview$crm_customer_01::indicatorKey");
        assertNotNull(preference);
        assertEquals(17, preference.getId());
        assertEquals(1, preference.getUserId());
    }


    @Test
    @Order(Integer.MAX_VALUE)
    void testDeleteAllByTeamCodeAndModuleKeyAndScopeStartingWith() {
        List<IndicatorPreference> all = repo.findAll();
        assertEquals(2, all.size());

        repo.deleteAllByTeamCodeAndScopeStartingWith(
                teamCode,
                "preview$scene_customer_01"
        );

        all = repo.findAll();
        assertEquals(1, all.size());
    }
}