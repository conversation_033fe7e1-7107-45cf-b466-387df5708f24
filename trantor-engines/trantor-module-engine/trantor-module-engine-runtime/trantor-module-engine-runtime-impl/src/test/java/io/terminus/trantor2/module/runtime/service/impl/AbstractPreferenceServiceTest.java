package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.cache.PreferenceCache;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class AbstractPreferenceServiceTest extends PreferenceBaseTest {
    @Autowired
    protected PreferenceCache preferenceCache;


    @AfterEach
    @SneakyThrows
    public void tearDown() {
        TrantorContext.clear();
        Thread.sleep(10);
        preferenceCache.invalidateAll();
    }
}