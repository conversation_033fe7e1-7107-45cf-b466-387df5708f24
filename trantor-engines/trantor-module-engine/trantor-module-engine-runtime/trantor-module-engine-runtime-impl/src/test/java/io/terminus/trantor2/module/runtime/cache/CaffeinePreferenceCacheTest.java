package io.terminus.trantor2.module.runtime.cache;

import io.terminus.trantor2.module.runtime.PreferenceBaseTest;
import io.terminus.trantor2.module.runtime.entity.BookmarkPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class CaffeinePreferenceCacheTest extends PreferenceBaseTest {
    @Autowired
    private CaffeinePreferenceCache cache;
    private final PreferenceCacheKey cacheKey = PreferenceCacheKey.builder()
            .teamCode("team")
            .moduleKey("module")
            .type(Preference.PreferenceType.BOOKMARK)
            .userId(1L).build();

    @Test
    @Order(1)
    void get() {
        PreferenceData data = cache.get(cacheKey);
        assertNotNull(data);
        assertInstanceOf(BookmarkPreferenceData.class, data);
        assertEquals(1, ((BookmarkPreferenceData) data).getBookmarks().size());
        assertEquals("kifI8maR0nbcCUpE1a-2H", ((BookmarkPreferenceData) data).getBookmarks().get(0).getCode());
    }

    @Test
    @Order(2)
    void getIfPresent() {
        PreferenceData data = cache.getIfPresent(cacheKey);
        assertNotNull(data);
        data = cache.getIfPresent(PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(123L).build());
        assertNull(data);
    }

    @Test
    @Order(3)
    void load() {
        PreferenceData data = cache.load(cacheKey);
        assertNotNull(data);
        assertInstanceOf(BookmarkPreferenceData.class, data);
        assertEquals(1, ((BookmarkPreferenceData) data).getBookmarks().size());
        assertEquals("kifI8maR0nbcCUpE1a-2H", ((BookmarkPreferenceData) data).getBookmarks().get(0).getCode());

        data = cache.getIfPresent(PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(123L).build());
        assertNull(data);
    }

    @Test
    @Order(4)
    void put() {
        PreferenceCacheKey newKey = PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(123L).build();

        PreferenceData cacheData = cache.getIfPresent(newKey);
        assertNull(cacheData);

        BookmarkPreferenceData data = new BookmarkPreferenceData();
        data.setBookmarks(new ArrayList<>());
        BookmarkPreferenceData.Bookmark bookmark = new BookmarkPreferenceData.Bookmark();
        bookmark.setCode("abc");
        bookmark.setName("abc");
        data.getBookmarks().add(bookmark);
        cache.put(newKey, data);

        cacheData = cache.getIfPresent(newKey);
        assertNotNull(cacheData);
    }

    @Test
    @Order(5)
    void invalidate() {
        PreferenceCacheKey newKey = PreferenceCacheKey.builder()
                .teamCode("team")
                .moduleKey("module")
                .type(Preference.PreferenceType.BOOKMARK)
                .userId(123L).build();
        PreferenceData cacheData = cache.getIfPresent(newKey);
        assertNotNull(cacheData);

        cache.invalidate(newKey);

        cacheData = cache.getIfPresent(newKey);
        assertNull(cacheData);
    }

    @Test
    @Order(6)
    void invalidateAll() {
        long l = cache.size();
        assertNotEquals(0, l);

        cache.invalidateAll();

        l = cache.size();
        assertEquals(0, l);
    }
}