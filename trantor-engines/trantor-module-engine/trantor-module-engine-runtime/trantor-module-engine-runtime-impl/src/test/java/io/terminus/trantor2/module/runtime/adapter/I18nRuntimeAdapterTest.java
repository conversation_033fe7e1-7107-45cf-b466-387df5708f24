package io.terminus.trantor2.module.runtime.adapter;

import io.terminus.trantor2.module.I18nTestBase;
import io.terminus.trantor2.module.util.I18nProperties;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */

class I18nRuntimeAdapterTest extends I18nTestBase {
    @Test
    void loadI18nProperties() {
        I18nProperties i18nProperties = i18nRuntimeTarget.loadI18nProperties();
        assertEquals(10, i18nProperties.size());
        assertTrue(i18nProperties.containsKey("模型"));
        assertTrue(i18nProperties.containsKey("场景"));
        assertTrue(i18nProperties.containsKey("创建"));
        assertTrue(i18nProperties.containsKey("menu.分组"));
        assertTrue(i18nProperties.containsKey("menu.场景"));
        assertTrue(i18nProperties.containsKey("menu.子文件夹"));
        assertTrue(i18nProperties.containsKey("menu.子菜单"));
        assertTrue(i18nProperties.containsKey("menu.文件夹"));
        assertTrue(i18nProperties.containsKey("model.斑马"));
        assertTrue(i18nProperties.containsKey("model.熊猫"));
    }

    @Test
    void findI18nResources() {
        Map<String, String> i18nResources = i18nRuntimeTarget.findI18nResources(Arrays.asList("menu.分组", "model.熊猫", "场景"));
        assertEquals(3, i18nResources.size());
        assertEquals("scene", i18nResources.get("场景"));
        assertEquals("group", i18nResources.get("menu.分组"));
        assertEquals("panda", i18nResources.get("model.熊猫"));
    }

    @Test
    void findI18nResourcesByRegex() {
        Map<String, String> i18nResourcesByRegex = i18nRuntimeTarget.findI18nResourcesByRegex(".*menu.*");
        assertEquals(5, i18nResourcesByRegex.size());
        assertTrue(i18nResourcesByRegex.keySet().stream().allMatch(value -> value.startsWith("menu")));
    }

    @Test
    void findI18nResourcesByPrefix() {
        Map<String, String> i18nResourcesByPrefix = i18nRuntimeTarget.findI18nResourcesByPrefix("menu.");
        assertEquals(5, i18nResourcesByPrefix.size());
        assertTrue(i18nResourcesByPrefix.keySet().stream().allMatch(value -> value.startsWith("menu")));
    }
}