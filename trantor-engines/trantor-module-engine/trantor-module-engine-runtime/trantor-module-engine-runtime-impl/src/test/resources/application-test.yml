spring:
  application:
    name: trantor2
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: false
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.MySQL57Dialect
    show-sql: true
  flyway:
    enabled: true
    table: trantor_flyway_schema_history
    locations: classpath:/db/migration
    validate-on-migrate: true
    baseline-on-migrate: true
    #baseline-version: 2.5.24.0330_001
trantor2:
  meta:
    event:
      type: test
  runtime:
    default-mode: true

cloud:
  storage:
    extend:
      - Bucket1:
          endpoint: http://test.com
          private-read: true
      - Bucket2:
          endpoint: http://test.com
          private-read: false
