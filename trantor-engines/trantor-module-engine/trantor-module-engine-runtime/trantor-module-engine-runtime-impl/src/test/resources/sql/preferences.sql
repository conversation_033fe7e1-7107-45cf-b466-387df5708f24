INSERT INTO `preference` (`id`, `created_at`, `created_by`, `updated_at`, `updated_by`, `version`, `data`, `module_key`,
                          `team_code`, `type`, `user_id`, `scope`)
VALUES (10, '2023-09-18 11:14:51.940000', 1, '2023-09-18 11:18:02.289000', 449907328366789, 4,
        '{\"filters\": [{\"code\": \"43cba080-67f2-4f8f-bfc6-78f2eb124752\", \"name\": \"初始化方案1\", \"content\": {\"filterType\": \"normal\", \"tableSettings\": {\"showNo\": false, \"columnsMap\": {\"phone\": {\"show\": true}, \"addrId\": {\"show\": true}, \"custId\": {\"show\": true}, \"soCode\": {\"show\": true}, \"taxAmt\": {\"show\": true}, \"slsDcId\": {\"show\": true}, \"slsTmId\": {\"show\": true}, \"delPhone\": {\"show\": true}, \"exchRate\": {\"show\": true}, \"slsOrgId\": {\"show\": true}, \"soTypeId\": {\"show\": true}, \"delAddrId\": {\"show\": true}, \"slsCurrId\": {\"show\": true}, \"soBilTime\": {\"show\": true}, \"soDelTime\": {\"show\": true}, \"soDocDate\": {\"show\": true}, \"soExtCode\": {\"show\": true}, \"soPayTime\": {\"show\": true}, \"soRefCode\": {\"show\": true}, \"baseCurrId\": {\"show\": true}, \"delPartyId\": {\"show\": true}, \"netBaseAmt\": {\"show\": true}, \"personName\": {\"show\": true}, \"soApplyTime\": {\"show\": true}, \"soBilStatus\": {\"show\": true}, \"soDelStatus\": {\"show\": true}, \"soPayStatus\": {\"show\": true}, \"exchRateDate\": {\"show\": true}, \"grossBaseAmt\": {\"show\": true}, \"soCreateType\": {\"show\": true}, \"delPersonName\": {\"show\": true}}, \"isTextWrap\": false, \"displayMode\": \"LIST\", \"showSummary\": true, \"lineHeightMode\": \"sm\", \"showZebraColor\": true, \"showVerticalDivider\": false}}, \"defaultFilter\": true}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'FILTER', 1,
        'TERP_MIGRATE$sls_ro_TERP_MIGRATE$sls_ro-list_TERP_MIGRATE$sls_so_head_tr_TERP_MIGRATE$SYS_PagingDataService'),
       (12, '2023-09-18 15:31:43.289000', 1, '2023-09-18 16:25:37.856000', 25, 11,
        '{\"filters\": [{\"code\": \"da91f967-ba50-42b7-8ab6-7e4178e34c49\", \"name\": \"初始化方案\", \"content\": {\"filterType\": \"pro\", \"tableSettings\": {\"showNo\": false, \"columnsMap\": {\"currId\": {\"show\": true}, \"pnDate\": {\"show\": true}, \"payerId\": {\"show\": true}, \"pnClass\": {\"show\": true}, \"comOrgId\": {\"show\": true}, \"exchRate\": {\"show\": true}, \"pnStatus\": {\"show\": true}, \"docTypeId\": {\"show\": true}, \"payerType\": {\"show\": true}, \"arApDocAmt\": {\"show\": true}, \"baseCurrId\": {\"show\": true}, \"createType\": {\"show\": true}, \"pnHeadCode\": {\"show\": true}, \"arApBaseAmt\": {\"show\": true}, \"payRecOrgId\": {\"show\": true}, \"purSlsOrgId\": {\"show\": true}, \"offsetDocAmt\": {\"show\": true}, \"clearedDocAmt\": {\"show\": true}, \"offsetBaseAmt\": {\"show\": true}, \"purSlsOrgName\": {\"show\": true}, \"clearedBaseAmt\": {\"show\": true}, \"clearingStatus\": {\"show\": true}, \"unoffsetDocAmt\": {\"show\": true}, \"unclearedDocAmt\": {\"show\": true}, \"unoffsetBaseAmt\": {\"show\": true}, \"headOffsetStatus\": {\"show\": true}, \"tradingPartnerId\": {\"show\": true}, \"unclearedBaseAmt\": {\"show\": true}, \"tradingPartnerName\": {\"show\": true}, \"tradingPartnerType\": {\"show\": true}, \"collectedPaidDocAmt\": {\"show\": true}, \"collectedPaidBaseAmt\": {\"show\": true}}, \"isTextWrap\": false, \"displayMode\": \"LIST\", \"showSummary\": true, \"lineHeightMode\": \"default\", \"showZebraColor\": true, \"showVerticalDivider\": false}}, \"defaultFilter\": true}, {\"code\": \"daf84bd7-3d7e-4316-b7fd-bbbfe2478f45\", \"name\": \"筛选方案02\", \"content\": {\"filterType\": \"pro\", \"filterSchema\": {\"title\": \"\", \"isShow\": true, \"configure\": {\"key\": \"9Wv91\", \"level\": 0, \"parentKey\": null, \"conditions\": [{\"key\": \"9Wv91_$$GvFKu\", \"name\": \"pnHeadCode\", \"level\": 1, \"operator\": \"EQ\", \"parentKey\": \"9Wv91\", \"fieldsType\": \"Text\"}, {\"key\": \"9Wv91_$$fwxAa\", \"name\": \"docTypeId\", \"level\": 1, \"operator\": \"EQ\", \"parentKey\": \"9Wv91\", \"fieldsType\": \"Model\"}, {\"key\": \"9Wv91_$$BhWyf\", \"name\": \"pnStatus\", \"level\": 1, \"operator\": \"EQ\", \"parentKey\": \"9Wv91\", \"fieldsType\": \"Select\"}, {\"key\": \"9Wv91_$$smFf9\", \"name\": \"comOrgId\", \"level\": 1, \"operator\": \"EQ\", \"parentKey\": \"9Wv91\", \"fieldsType\": \"Model\"}], \"logicOperator\": \"AND\"}}}, \"defaultFilter\": false}, {\"code\": \"d17eb69b-074e-4364-852d-33e2debe0059\", \"name\": \"筛选方案03\", \"content\": {\"filterType\": \"normal\", \"tableSettings\": {\"showNo\": false, \"columnsMap\": {\"currId\": {\"show\": false}, \"pnDate\": {\"show\": false, \"sortOrder\": \"descend\"}, \"payerId\": {\"show\": false}, \"pnClass\": {\"show\": true}, \"comOrgId\": {\"show\": true}, \"exchRate\": {\"show\": false}, \"pnStatus\": {\"show\": false}, \"docTypeId\": {\"show\": true}, \"payerType\": {\"show\": false}, \"arApDocAmt\": {\"show\": false}, \"baseCurrId\": {\"show\": false}, \"createType\": {\"show\": false}, \"pnHeadCode\": {\"show\": true}, \"arApBaseAmt\": {\"show\": false}, \"payRecOrgId\": {\"show\": false}, \"purSlsOrgId\": {\"show\": true}, \"offsetDocAmt\": {\"show\": false}, \"clearedDocAmt\": {\"show\": false}, \"offsetBaseAmt\": {\"show\": false}, \"purSlsOrgName\": {\"show\": true}, \"clearedBaseAmt\": {\"show\": false}, \"clearingStatus\": {\"show\": false}, \"unoffsetDocAmt\": {\"show\": false}, \"unclearedDocAmt\": {\"show\": false}, \"unoffsetBaseAmt\": {\"show\": false}, \"headOffsetStatus\": {\"show\": false}, \"tradingPartnerId\": {\"show\": false}, \"unclearedBaseAmt\": {\"show\": false}, \"tradingPartnerName\": {\"show\": false}, \"tradingPartnerType\": {\"show\": false}, \"collectedPaidDocAmt\": {\"show\": false}, \"collectedPaidBaseAmt\": {\"show\": false}}, \"isTextWrap\": true, \"displayMode\": \"LIST\", \"showSummary\": true, \"lineHeightMode\": \"default\", \"showZebraColor\": true, \"showVerticalDivider\": true}}, \"defaultFilter\": false}, {\"code\": \"d165d283-c38a-43d2-8a1a-713b252aa598\", \"name\": \"筛选方案04\", \"content\": {\"filterType\": \"normal\", \"tableSettings\": {\"showNo\": false, \"columnsMap\": {\"currId\": {\"show\": true}, \"pnDate\": {\"show\": true, \"sortOrder\": \"descend\"}, \"payerId\": {\"show\": true}, \"pnClass\": {\"show\": true}, \"comOrgId\": {\"show\": true}, \"exchRate\": {\"show\": true}, \"pnStatus\": {\"show\": true}, \"docTypeId\": {\"show\": true}, \"payerType\": {\"show\": true}, \"arApDocAmt\": {\"show\": true}, \"baseCurrId\": {\"show\": true}, \"createType\": {\"show\": true}, \"pnHeadCode\": {\"show\": true}, \"arApBaseAmt\": {\"show\": true}, \"payRecOrgId\": {\"show\": true}, \"purSlsOrgId\": {\"show\": true}, \"offsetDocAmt\": {\"show\": true}, \"clearedDocAmt\": {\"show\": true}, \"offsetBaseAmt\": {\"show\": true}, \"purSlsOrgName\": {\"show\": true}, \"clearedBaseAmt\": {\"show\": true}, \"clearingStatus\": {\"show\": true}, \"unoffsetDocAmt\": {\"show\": true}, \"unclearedDocAmt\": {\"show\": true}, \"unoffsetBaseAmt\": {\"show\": true}, \"headOffsetStatus\": {\"show\": true}, \"tradingPartnerId\": {\"show\": true}, \"unclearedBaseAmt\": {\"show\": true}, \"tradingPartnerName\": {\"show\": true}, \"tradingPartnerType\": {\"show\": true}, \"collectedPaidDocAmt\": {\"show\": true}, \"collectedPaidBaseAmt\": {\"show\": true}}, \"isTextWrap\": true, \"displayMode\": \"LIST\", \"showSummary\": true, \"lineHeightMode\": \"default\", \"showZebraColor\": true, \"showVerticalDivider\": true}}, \"defaultFilter\": false}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'FILTER', 1,
        'TERP_MIGRATE$sls_ro_TERP_MIGRATE$FIN_CM_PN_REC-list_TERP_MIGRATE$fin_cm_pn_head_tr_SYS_PagingDataService'),
       (13, '2024-04-02 16:19:07.029000', 1, '2024-04-07 14:06:40.943000', 473389403313477, 13,
        '{\"navigations\": [{\"code\": \"_gwXDNFqne-NU8oI6zgLc\", \"icon\": null, \"name\": \"33\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$cj_test_rencenrea\"}}, {\"code\": \"Veu5230rSUN1Iun73KLM3\", \"icon\": null, \"name\": \"4\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$test_cj_susadsk\"}}, {\"code\": \"jRSvGCeEW8xar-Bjw-dpP\", \"icon\": null, \"name\": \"44\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\"}}, {\"code\": \"_0rGgjlgHohKHGeJ8KGm9\", \"icon\": null, \"name\": \"2\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$chenjie_org_test\"}}, {\"code\": \"bFB3mLnyC92oppICOPBR5\", \"icon\": \"https://terminus-new-trantor.oss-cn-hangzhou.aliyuncs.com/trantor2/console/terp_migrate/5c760a70-20c7-4fe9-8ecb-b6e18969c19c/5A973FEB-4063-41AE-B3A0-0DD0321A7027.png\", \"name\": \"常用合同场景\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\"}}, {\"code\": \"GFaWGnoYQ0HjHUKAfLxZu\", \"icon\": null, \"name\": \"22\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$TERP_MIGRATE_qf08\"}}, {\"code\": \"cqUJEH2sHnS4h9NuCLV58\", \"icon\": null, \"name\": \"3\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$test_Navigation\"}}, {\"code\": \"8A4UYNTAe43kdOUAm6rGB\", \"icon\": null, \"name\": \"11\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$sup_invitation_scene\"}}, {\"code\": \"AQmMR0LWkHfMGdvHqNPCk\", \"icon\": null, \"name\": \"1232\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$SETT_ITEM_fr_test\"}}, {\"code\": \"dpiaC7TDzsPCrrP9gZ5tB\", \"icon\": null, \"name\": \"常用主数据场景\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$cj_test_rencenrea\"}}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'NAVIGATION', 1,
        'TERP_MIGRATE$test_Navigation::list-TERP_MIGRATE$test_Navigation-zrKcPDK_ZN4U5CXHzM1Rk'),
       (14, '2024-04-03 11:41:16.370000', 1, '2024-04-03 11:42:29.766000', 37, 1,
        '{\"navigations\": [{\"code\": \"i4DA3OWEfuqowVf3AMez1\", \"icon\": null, \"name\": \"单据场景\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$cj_test_scene\"}}, {\"code\": \"AQmMR0LWkHfMGdvHqNPCk\", \"icon\": null, \"name\": \"1232\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$SETT_ITEM_fr_test\"}}, {\"code\": \"_0rGgjlgHohKHGeJ8KGm9\", \"icon\": null, \"name\": \"2\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$chenjie_org_test\"}}, {\"code\": \"cqUJEH2sHnS4h9NuCLV58\", \"icon\": null, \"name\": \"3\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$test_Navigation\"}}, {\"code\": \"Veu5230rSUN1Iun73KLM3\", \"icon\": null, \"name\": \"4\", \"frontendConfig\": {\"sceneKey\": \"TERP_MIGRATE$test_cj_susadsk\"}}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'NAVIGATION', 1,
        'TERP_MIGRATE$test_Navigation::list-TERP_MIGRATE$test_Navigation-deeeeeeee'),
       (15, '2024-04-16 14:02:49.309000', 1, '2024-04-16 14:02:49.309000', 40, 0,
        '{\"bookmarks\": [{\"ref\": {\"viewKey\": \"ERP_GEN$GEN_MAT_MD_VIEW-list\", \"sceneKey\": \"ERP_GEN$GEN_MAT_MD_VIEW\"}, \"code\": \"kifI8maR0nbcCUpE1a-2H\", \"icon\": null, \"name\": \"物料主数据管理\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$59ac7d1e-3c48-4a55-a748-1b1c449209fb/page?sceneKey=ERP_GEN%24GEN_MAT_MD_VIEW\"}}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'BOOKMARK', 1, NULL),
       (16, '2024-04-16 16:12:51.794000', 1, '2024-05-06 10:08:46.864000', 473389403313477, 139,
        '{\"bookmarks\": [{\"ref\": {\"viewKey\": \"ERP_PLN$PLN_SLS_SUM_STATUS_VIEW-list\", \"sceneKey\": \"ERP_PLN$PLN_SLS_SUM_STATUS_VIEW\"}, \"code\": \"5Q5Pp0Xa9imvBYaRLZG9h\", \"icon\": null, \"name\": \"汇总状态查询\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$ed38f76e-8f2e-4254-9361-e60df008d451/page?sceneKey=ERP_PLN%24PLN_SLS_SUM_STATUS_VIEW\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$testcj_found_contract:list\", \"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\"}, \"code\": \"Z6IDFBa32PgKm-Eeespac\", \"icon\": null, \"name\": \"合同表列表123\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b9648084-b0a0-46b6-bc4f-31e237a04669/page?sceneKey=TERP_MIGRATE%24testcj_found_contract\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$test_cj_susadsk:list\", \"sceneKey\": \"TERP_MIGRATE$test_cj_susadsk\"}, \"code\": \"Q2WaBYAXK_A4OwCPhVuyw\", \"icon\": null, \"name\": \"ces1\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$42355fc1-23db-4a3b-ba60-e85b9f93b717/page?sceneKey=TERP_MIGRATE%24test_cj_susadsk\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$testcj_found_contract:list\", \"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\"}, \"code\": \"C5iMIKvSa40L5u83vQg9d\", \"icon\": null, \"name\": \"合同表列表312131\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b9648084-b0a0-46b6-bc4f-31e237a04669/page?sceneKey=TERP_MIGRATE%24testcj_found_contract\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$testcj_found_contract:list\", \"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\", \"filterCode\": \"bb713dd5-e129-4781-9175-70704e0f02b8\", \"filterScope\": \"TERP_MIGRATE$testcj_found_contract_TERP_MIGRATE$testcj_found_contract:list_TERP_MIGRATE$contract_table_TERP_MIGRATE$SYS_PagingDataService_TERP_MIGRATE$testcj_found_contract-table-container-TERP_MIGRATE$contract_table\"}, \"code\": \"lFaIUNLwmCKF4uWWnxAGH\", \"icon\": null, \"name\": \"合同表列表\", \"type\": \"FILTER\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b9648084-b0a0-46b6-bc4f-31e237a04669/page?sceneKey=TERP_MIGRATE%24testcj_found_contract&preferenceCode=bb713dd5-e129-4781-9175-70704e0f02b8\"}}, {\"ref\": null, \"code\": \"HFtTnrcjqg5S3g3S0pyXR\", \"icon\": null, \"name\": \"可我却恶趣味u我企鹅\", \"type\": \"URL\", \"frontendConfig\": {\"url\": \"https://aliyuque.antfin.com/trantor/siocpp/eryoqknvcswu9d3y\"}}, {\"ref\": {\"viewKey\": \"ERP_GEN$GEN_APPLY_TASK_CENTER_VIEW-list\", \"sceneKey\": \"ERP_GEN$GEN_APPLY_TASK_CENTER_VIEW\"}, \"code\": \"d3jIMujesLQA5rmSPqtmr\", \"icon\": null, \"name\": \"审批任务中心\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$7fbe8163-0f9b-4431-8904-d9aeccea5e50/page?sceneKey=ERP_GEN%24GEN_APPLY_TASK_CENTER_VIEW\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$testcj_found_contract:list\", \"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\", \"filterCode\": \"def91bb3-a935-4981-a900-4dfbcc4107a9\", \"filterScope\": \"TERP_MIGRATE$testcj_found_contract_TERP_MIGRATE$testcj_found_contract:list_TERP_MIGRATE$contract_table_TERP_MIGRATE$SYS_PagingDataService_TERP_MIGRATE$testcj_found_contract-table-container-TERP_MIGRATE$contract_table\"}, \"code\": \"i-BbKRotogBUIebcb99x1\", \"icon\": null, \"name\": \"合同表列表-系统初始方案\", \"type\": \"FILTER\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b9648084-b0a0-46b6-bc4f-31e237a04669/page?sceneKey=TERP_MIGRATE%24testcj_found_contract&preferenceCode=def91bb3-a935-4981-a900-4dfbcc4107a9\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$meter:list\", \"sceneKey\": \"TERP_MIGRATE$meter\"}, \"code\": \"1WXO5JBjhnmxrjDPxW9zm\", \"icon\": null, \"name\": \"表记\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$MLVTVu/page?sceneKey=TERP_MIGRATE%24meter\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$cj_test_rencenrea:list\", \"sceneKey\": \"TERP_MIGRATE$cj_test_rencenrea\"}, \"code\": \"WJSXGKz-_xX94b80Zg-2u\", \"icon\": null, \"name\": \"cj测试使用主数据场景\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$133916c7-05a2-41c2-a3e7-3298d286918b/page?sceneKey=TERP_MIGRATE%24cj_test_rencenrea\"}}, {\"ref\": {\"viewKey\": \"test$test3201:list\", \"sceneKey\": \"test$test3201\"}, \"code\": \"Utq9J-HotmGZJm6uA93th\", \"icon\": null, \"name\": \"主数据场景-状态\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$fe8770b1-7476-4157-a16f-be59da02b994/page?sceneKey=test%24test3201\"}}, {\"ref\": {\"viewKey\": \"ERP_GEN$GEN_MAT_MD_VIEW-list\", \"sceneKey\": \"ERP_GEN$GEN_MAT_MD_VIEW\"}, \"code\": \"SpdKo10O5XYgQpycMyCOd\", \"icon\": null, \"name\": \"物料主数据管理\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$59ac7d1e-3c48-4a55-a748-1b1c449209fb/page?sceneKey=ERP_GEN%24GEN_MAT_MD_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_GEN$GEN_SLS_CUST_INFO_VIEW:list\", \"sceneKey\": \"ERP_GEN$GEN_SLS_CUST_INFO_VIEW\"}, \"code\": \"2119p71f1qT9-AcVXo1bD\", \"icon\": null, \"name\": \"客户档案\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$983d4e7e-1e1f-472e-bb79-e27a83a53294/page?sceneKey=ERP_GEN%24GEN_SLS_CUST_INFO_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_PLN$PLN_VEND_SUPPLY_VIEW-list\", \"sceneKey\": \"ERP_PLN$PLN_VEND_SUPPLY_VIEW\"}, \"code\": \"rNyDa96cUk534NzkxUm53\", \"icon\": null, \"name\": \"供应商供应能力维护\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$22a9e4f2-45fd-496c-8fb5-d79c2e47ecb8/page?sceneKey=ERP_PLN%24PLN_VEND_SUPPLY_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_PLN$PLN_SLS_TARGET_VIEW-list\", \"sceneKey\": \"ERP_PLN$PLN_SLS_TARGET_VIEW\"}, \"code\": \"tL2SWlZqoA2aVAgvPhddr\", \"icon\": null, \"name\": \"销售目标维护\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$50a6a06e-a141-4ac9-8e3b-d5e19f12d978/page?sceneKey=ERP_PLN%24PLN_SLS_TARGET_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_PLN$PLN_SLS_SUM_ANALYSIS_VIEW-list\", \"sceneKey\": \"ERP_PLN$PLN_SLS_SUM_ANALYSIS_VIEW\"}, \"code\": \"D-eJXlEAs1Yi8cbZUxNRj\", \"icon\": null, \"name\": \"销售计划汇总分析\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$2396623c-3572-493d-b739-12093df24d72/page?sceneKey=ERP_PLN%24PLN_SLS_SUM_ANALYSIS_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_PLN$PLN_SLS_BALANCE_OUTPUT_VIEW-list\", \"sceneKey\": \"ERP_PLN$PLN_SLS_BALANCE_OUTPUT_VIEW\"}, \"code\": \"McVUm_ttoq51RfY1OTgYw\", \"icon\": null, \"name\": \"输出单据处理\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b0f48e6f-3e8a-4032-8c43-60d20bd8f8e3/page?sceneKey=ERP_PLN%24PLN_SLS_BALANCE_OUTPUT_VIEW\"}}, {\"ref\": {\"viewKey\": \"ERP_GTR$payment_task_for_copy:list\", \"sceneKey\": \"ERP_GTR$payment_task_for_copy\"}, \"code\": \"lXGbc5srqQWa9l_Ttslcw\", \"icon\": null, \"name\": \"付款任务复制\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$776b3995-0754-4a5d-aa95-97d1ff9ab31b/page?sceneKey=ERP_GTR%24payment_task_for_copy\"}}, {\"ref\": {\"viewKey\": \"ERP_GTR$business_type_config:list\", \"sceneKey\": \"ERP_GTR$business_type_config\"}, \"code\": \"G-wb10B5b6VD9lbqm9RAp\", \"icon\": null, \"name\": \"业务类型配置\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$9d2f3a09-8431-48a7-8679-************/page?sceneKey=ERP_GTR%24business_type_config\"}}, {\"ref\": {\"viewKey\": \"ERP_GTR$GTR_BANK_ACCOUUNT_CLOSE_TR_VIEW:list\", \"sceneKey\": \"ERP_GTR$GTR_BANK_ACCOUUNT_CLOSE_TR_VIEW\"}, \"code\": \"-U9f4q84B-ayFtDMChFF1\", \"icon\": null, \"name\": \"账户销户申请\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$106148d8-c945-4748-8054-497023ee2e67/page?sceneKey=ERP_GTR%24GTR_BANK_ACCOUUNT_CLOSE_TR_VIEW\"}}, {\"ref\": {\"viewKey\": \"TERP_MIGRATE$testcj_found_contract:detail\", \"sceneKey\": \"TERP_MIGRATE$testcj_found_contract\"}, \"code\": \"nc3FszseXGDWaOX_R-SJ0\", \"icon\": null, \"name\": \"合同表详情\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$b9648084-b0a0-46b6-bc4f-31e237a04669/page?action=show&recordId=2002002&sceneKey=TERP_MIGRATE%24testcj_found_contract\"}}, {\"ref\": {\"viewKey\": \"ERP_GTR$transfer_note_rule_list_new_2:list\", \"sceneKey\": \"ERP_GTR$transfer_note_rule_list_new_2\"}, \"code\": \"LS-9pGIypRf65OHzMd_2b\", \"icon\": null, \"name\": \"转账附言规则新2\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$e26971a7-04ef-44d4-9a82-06ae512ef2f0/page?sceneKey=ERP_GTR%24transfer_note_rule_list_new_2\"}}, {\"ref\": {\"viewKey\": \"ERP_GTR$payment_task_list_for_edit:list\", \"sceneKey\": \"ERP_GTR$payment_task_list_for_edit\"}, \"code\": \"VgEifMaBaZBai2Qir89Dw\", \"icon\": null, \"name\": \"付款任务编辑用\", \"type\": \"VIEW\", \"frontendConfig\": {\"path\": \"/TERP_MIGRATE_PORTAL/TERP_MIGRATE_PORTAL$9a4fb738-14df-4e92-8a1f-905a6e3a8675/page?sceneKey=ERP_GTR%24payment_task_list_for_edit\"}}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'BOOKMARK', 2, NULL),
       (17, '2024-04-11 10:18:31.504000', 1, '2024-04-11 10:18:31.597000', 435397539820229, 2,
        '{\"indicators\": [{\"code\": \"hhh\", \"icon\": \"icon\", \"name\": \"hhh\", \"frontendConfig\": {\"前端配置\": \"想存啥存啥\", \"前端配置2\": []}}, {\"code\": \"abc\", \"icon\": null, \"name\": \"abc\", \"frontendConfig\": null}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'INDICATOR', 1,
        'preview$scene_customer_01::preview$scene_customer_01-list-preview$crm_customer_01::indicatorKey'),
       (18, '2024-04-11 14:59:19.124000', 1, '2024-04-11 14:59:19.124000', 435429519844037, 0,
        '{\"indicators\": [{\"code\": \"V31KS4YvhNYz2OfDh_rQC\", \"icon\": \"sdfdsfsdfs\", \"name\": \"新的指标\", \"frontendConfig\": {}}, {\"code\": \"ph768wrJI6F6skase8cqm\", \"icon\": \"sdfdsfsdfs\", \"name\": \"新的指标\", \"frontendConfig\": {}}]}',
        'TERP_MIGRATE_PORTAL', 'terp_migrate', 'INDICATOR', 1,
        'ERP_GEN$GEN_MAT_MD_VIEW::ERP_GEN$GEN_MAT_MD_VIEW:TV-NOmPABSdZ3TgbXOLHo::material$indicator');