package io.terminus.trantor2.module.runtime.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.api.response.Response;
import io.terminus.i18n.api.enums.I18nLanguage;
import io.terminus.i18n.api.facade.I18nResourceReadFacade;
import io.terminus.i18n.api.model.request.QueryMessageSourceRequest;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.PortalI18nConfig;
import io.terminus.trantor2.module.meta.I18nLanguagePack;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.I18nLanguagePackUtils;
import io.terminus.trantor2.module.util.I18nProperties;
import jakarta.annotation.Nonnull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PortalI18nCaffeine implements PortalI18nCache {
    private final I18nResourceReadFacade i18nResourceReader;
    private final OSSService ossService;
    /**
     * 语言包缓存，从中间件加载
     */
    private final LoadingCache<PortalI18nCacheKey, Map<String, String>> portalIi18nResourcesCache =
            Caffeine.newBuilder().maximumSize(5).expireAfterAccess(1, TimeUnit.HOURS).build(this::load);
    /**
     * 语言包缓存，从OSS下载
     */
    private final LoadingCache<String, I18nProperties> i18nPropertiesOssCache =
            Caffeine.newBuilder().maximumSize(5).expireAfterAccess(1, TimeUnit.HOURS).build(this::loadFromOss);

    /**
     * 国际化查询结果缓存
     * 缓存key为: portalCode:lang:normalKey
     * 缓存value为对应的翻译结果
     */
    private static final Cache<String, String> i18nResultCache = Caffeine.newBuilder()
            .maximumSize(20000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    @Override
    public I18nProperties getI18nPropertiesFromOSS(@Nonnull Portal portal, @Nonnull String lang) {
        Optional<PortalI18nConfig.PortalI18nLanguage> languageOpt = portal.getI18nConfig().getLanguage(lang);
        if (!languageOpt.isPresent()) {
            return new I18nProperties();
        }
        String downloadUrl = languageOpt.get().getDownloadUrl();
        // 未上传过语言包，尝试从中间件加载
        if (downloadUrl == null) {
            Map<String, String> resources = portalIi18nResourcesCache.get(PortalI18nCacheKey.of(portal.getTeamCode(), portal.getCode(), lang));
            I18nProperties properties = new I18nProperties();
            if (MapUtils.isNotEmpty(resources)) {
                properties.putAll(resources);
            }
            return properties;
        }
        return i18nPropertiesOssCache.get(downloadUrl);
    }

    @Override
    public Map<String, String> getI18nResourcesFromMiddleware(@Nonnull Portal portal, @Nonnull String lang) {
        return portalIi18nResourcesCache.get(PortalI18nCacheKey.of(portal.getTeamCode(), portal.getCode(), lang));
    }

    @Override
    public void invalidate(@Nonnull String teamCode, @Nonnull String portalCode, @Nonnull I18nLanguagePack languagePack) {
        String downloadUrl = languagePack.getDownloadUrl();
        if (downloadUrl != null) {
            i18nPropertiesOssCache.invalidate(downloadUrl);
        }
        portalIi18nResourcesCache.invalidate(PortalI18nCacheKey.of(teamCode, portalCode, languagePack.getKey()));

        clearI18nResultCache(portalCode, languagePack.getKey());
        
        if (log.isDebugEnabled()) {
            log.debug("invalidate i18n properties cache, team: {}, portal: {}, lang: {}",
                    teamCode, portalCode, languagePack.getKey());
        }
    }

        /**
     * 获取缓存的国际化翻译结果
     */
    public String get(String portalCode, String lang, String key) {
        String cacheKey = buildCacheKey(portalCode, lang, key);
        return i18nResultCache.getIfPresent(cacheKey);
    }

    /**
     * 缓存国际化翻译结果
     */
    public void put(String portalCode, String lang, String key, String value) {
        String cacheKey = buildCacheKey(portalCode, lang, key);
        i18nResultCache.put(cacheKey, value);
        
        if (log.isDebugEnabled()) {
            log.debug("Cached i18n: portal={}, lang={}, key={}, value={}", 
                    portalCode, lang, key, value);
        }
    }

    /**
     * 清理指定门户和语言的查询结果缓存
     */
    public void clearI18nResultCache(String portalCode, String lang) {
        if (portalCode == null || lang == null) {
            return;
        }

        String prefix = buildCacheKey(portalCode, lang, "");
        i18nResultCache.asMap().keySet().removeIf(key -> key.startsWith(prefix));

        if (log.isDebugEnabled()) {
            log.debug("Cleared i18n result cache for portal: {}, lang: {}", portalCode, lang);
        }
    }

    /**
     * 清理所有查询结果缓存
     */
    public void clearAllI18nResultCache() {
        i18nResultCache.invalidateAll();
        if (log.isDebugEnabled()) {
            log.debug("Cleared all i18n result cache");
        }
    }

    /**
     * 构建缓存key
     */
    private String buildCacheKey(String portalCode, String lang, String normalKey) {
        return portalCode + ":" + lang + ":" + normalKey;
    }

    /**
     * 从OSS加载语言包
     */
    private I18nProperties loadFromOss(@Nonnull String downloadUrl) {
        I18nProperties i18nProperties = I18nLanguagePackUtils.downloadLanguagePackFromOSS(ossService, downloadUrl, false);
        i18nProperties.entrySet().removeIf(entry -> ObjectUtils.isEmpty(entry.getValue()));
        return i18nProperties;
    }

    /**
     * 从中间件加载语言包
     */
    private Map<String, String> load(PortalI18nCacheKey key) {
        String team = key.getTeamCode();
        String portal = key.getPortalCode();
        String lang = key.getLang();
        Response<Map<String, String>> response = i18nResourceReader.load(
                QueryMessageSourceRequest.of(appName(team, portal), I18nLanguage.getLanguageByLang(lang).getLocale()));
        if (response.getSuccess()) {
            return response.getData();
        } else {
            log.error("load i18n properties failure, team:{}，portal: {}, lang: {}",
                    team, portal, lang);
            return Collections.emptyMap();
        }
    }

    private String appName(String teamCode, String portalCode) {
        return teamCode + "@" + portalCode;
    }

    @Getter
    @AllArgsConstructor
    public static class PortalI18nCacheKey {
        private final String teamCode;
        private final String portalCode;
        private final String lang;

        public static PortalI18nCacheKey of(String teamCode, String portalCode, String lang) {
            return new PortalI18nCacheKey(teamCode, portalCode, lang);
        }
    }
}
