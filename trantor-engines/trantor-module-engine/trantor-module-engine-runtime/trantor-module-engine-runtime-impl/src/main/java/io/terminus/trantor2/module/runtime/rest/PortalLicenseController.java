package io.terminus.trantor2.module.runtime.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.LicenseConfig;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.platform.TrantorVersionService;
import io.terminus.trantor2.module.service.LicenseService;
import io.terminus.trantor2.module.util.LicenseThread;
import io.terminus.trantor2.module.util.LicenseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Tag(name = "portal证书接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/portal/license")
public class PortalLicenseController {

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private LicenseThread licenseThread;

    @Autowired
    private TrantorVersionService trantorVersionService;

    @Autowired
    private TrantorIAMUserService trantorIAMUserService;

    @Deprecated
    @GetMapping("/{teamCode}")
    @Operation(summary = "根据项目编码 获取证书信息")
    public Response<LicenseConfig> query(@PathVariable String teamCode) {
        LicenseConfig result = licenseThread.getLicenseConfig(teamCode);
        if (result == null) {
            return Response.ok(LicenseConfig.getNULL());
        }
        result.fillPlatform(licenseThread.getLicenseConfig(LicenseConfig.PLATFORM_TEAM_CODE));
        return Response.ok(result);
    }

    @PostMapping("/upload")
    @Operation(summary = "根据项目编码 获取证书信息")
    public Response<LicenseConfig> uploadLicense(@RequestParam("license") MultipartFile license,
                                                 @RequestParam(required = true) String privateKey,
                                                 @RequestParam(required = true) String teamCode) {
        if (!trantorIAMUserService.isAdmin(TrantorContext.getCurrentUserId())) {
            return Response.error("500", "非管理员无权操作");
        }
        String sign;
        try {
            sign = new String(license.getBytes());
        } catch (Exception e) {
            // 上传文件异常
            return Response.error("500", "License文件异常");
        }
        String result = LicenseUtils.decrypt(sign, privateKey);
        LicenseConfig licenseConfig = LicenseConfig.of(result, trantorVersionService.getTrantorVersion());
        if (!licenseConfig.isAuthorized()) {
            return Response.error("500", "无效License文件");
        }
        if (licenseConfig.isExpired()) {
            return Response.error("500", "无效License文件，证书已过期");
        }

        // 持久化数据
        licenseService.uploadLicense(sign, privateKey, teamCode);
        // 更新证书独立线程信息
        licenseThread.updateLicenseConfig(teamCode, licenseConfig);

        return Response.ok(licenseConfig);
    }

    @GetMapping("/flush")
    @Operation(summary = "刷新证书信息")
    public Response<Boolean> flush() {
        licenseThread.flush();
        return Response.ok(Boolean.TRUE);
    }

}
