package io.terminus.trantor2.module.runtime.service.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.meta.resource.ResourceBaseMeta;
import io.terminus.trantor2.module.meta.MenuIcon;
import io.terminus.trantor2.module.meta.MenuIconMeta;
import io.terminus.trantor2.module.meta.PortalIcon;
import io.terminus.trantor2.module.meta.PortalIconMeta;
import io.terminus.trantor2.module.runtime.repo.MenuIconRuntimeRepo;
import io.terminus.trantor2.module.runtime.repo.PortalIconRuntimeRepo;
import io.terminus.trantor2.module.service.IconService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 **/
@Service
@AllArgsConstructor
public class IconRuntimeService implements IconService {
    private final PortalIconRuntimeRepo portalIconRepo;
    private final MenuIconRuntimeRepo menuIconRepo;
    @Override
    public List<PortalIcon> queryPortalIcon(Long teamId, String portalKey) {
        return Optional.ofNullable(portalIconRepo.findOneByKey(generatePortalIconKey(portalKey)))
                .map(ResourceBaseMeta::getResourceProps)
                .map(PortalIconMeta.Props::getIcons)
                .orElse(Lists.newArrayList());
    }

    @Override
    public List<MenuIcon> queryMenuIcon(Long teamId, String portalKey, String packageKey) {
        MenuIconMeta icon = menuIconRepo.findOneByKey(generateMenuIconKey(portalKey, packageKey));
        if (Objects.isNull(icon)) {
            icon = menuIconRepo.findOneByKey(generateMenuIconKey(portalKey));
        }

        return Optional.ofNullable(icon)
                .map(ResourceBaseMeta::getResourceProps)
                .map(MenuIconMeta.Props::getIcons)
                .orElse(Lists.newArrayList());
    }
}
