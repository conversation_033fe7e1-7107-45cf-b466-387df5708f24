package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.IndicatorPreference;
import io.terminus.trantor2.module.runtime.entity.IndicatorPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import io.terminus.trantor2.module.runtime.repo.IndicatorPreferenceRepo;
import io.terminus.trantor2.module.runtime.request.IndicatorPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceSaveRequest;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@InternalComponent
public class IndicatorPreferenceService extends AbstractPreferenceService<IndicatorPreference> {
    private final IndicatorPreferenceRepo repo;

    @Override
    public Preference.PreferenceType getType() {
        return Preference.PreferenceType.INDICATOR;
    }

    @Override
    public AbstractPreferenceRepo<IndicatorPreference> getPreferenceRepo() {
        return repo;
    }

    @Override
    protected <R extends PreferenceSaveRequest> IndicatorPreference save(R req) {
        if (!(req instanceof IndicatorPreferenceSaveRequest)) {
            throw new ValidationException("bad request");
        }
        IndicatorPreferenceSaveRequest request = (IndicatorPreferenceSaveRequest) req;

        IndicatorPreference preference = Optional.ofNullable(repo.findByScopeAndCurUser(request.getScope()))
                .orElse(newPreference(IndicatorPreference.class));
        preference.setScope(request.getScope());
        if (preference.getData() == null) {
            preference.setData(new IndicatorPreferenceData());
        }
        IndicatorPreferenceData data = preference.getData();
        data.setIndicators(request.getIndicators());

        repo.saveAndFlush(preference);
        return preference;
    }


    @Override
    public void onSceneDeleted(String teamCode, String sceneKey) {
        String scope = sceneKey + "::";
        repo.deleteAllByTeamCodeAndScopeStartingWith(teamCode, scope);
    }
}
