package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.NavigationPreference;
import io.terminus.trantor2.module.runtime.entity.Preference;

/**
 * <AUTHOR>
 */
@InternalComponent
public interface NavigationPreferenceRepo extends AbstractPreferenceRepo<NavigationPreference> {
    @Override
    default Preference.PreferenceType getPreferenceType() {
        return Preference.PreferenceType.NAVIGATION;
    }
}
