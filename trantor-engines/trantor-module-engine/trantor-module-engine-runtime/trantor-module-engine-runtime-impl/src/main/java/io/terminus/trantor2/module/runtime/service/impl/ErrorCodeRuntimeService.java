package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.module.meta.ErrorCodeMeta;
import io.terminus.trantor2.module.runtime.repo.ErrorCodeRuntimeRepo;
import io.terminus.trantor2.module.service.ErrorCodeQueryService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ErrorCodeRuntimeService implements ErrorCodeQueryService {
    private final ErrorCodeRuntimeRepo errorCodeRuntimeRepo;

    @NotNull
    @Override
    public Optional<ErrorCodeMeta> findOneByKey(@NotNull String key) {
        return Optional.ofNullable(errorCodeRuntimeRepo.findOneBy<PERSON>ey(key));
    }

    @NotNull
    @Override
    public Optional<ErrorCodeMeta> findOneByKey(@NotNull String key, @Nullable ResourceContext ctx) {
        return findOneByKey(key);
    }
}
