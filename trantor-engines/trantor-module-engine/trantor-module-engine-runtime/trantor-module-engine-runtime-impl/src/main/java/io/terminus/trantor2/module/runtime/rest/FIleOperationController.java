package io.terminus.trantor2.module.runtime.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.dto.CreateFormUploadDTO;
import io.terminus.trantor2.module.dto.CreateUploadDTO;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.SystemConfig;
import io.terminus.trantor2.module.runtime.request.CompleteUploadRequest;
import io.terminus.trantor2.module.runtime.request.CreateUploadRequest;
import io.terminus.trantor2.module.runtime.request.UploadRequest;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.spi.model.OSSAdapterUploadRequest;
import io.terminus.trantor2.module.util.OSSConstant;
import io.terminus.trantor2.module.util.WatermarkUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author: yangyuqiang
 * @date: 2023/8/29 2:41 PM
 **/
@Tag(name = "文件管理")
@RestController
@RequestMapping(path = "/api/trantor/portal/file")
public class FIleOperationController {
    @Autowired
    private OSSService ossService;
    @Autowired
    private WatermarkUtil watermarkUtil;
    @Autowired
    private ConfigurationService configurationService;

    @PostMapping("/upload")
    @Operation(summary = "文件上传")
    public Response<String> upload(@RequestParam("file") MultipartFile file) {
        String prefix = OSSConstant.PORTAL_FILE_PREFIX;
        if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
            prefix = prefix + TrantorContext.getTeamCode() + "/";
        }
        return ossService.uploadAndGetUrl(file, prefix, false);
    }

    @GetMapping("/url")
    @Operation(summary = "获取可访问水印url")
    public Response<String> getFileUrl(@RequestParam String fileUrl, @RequestParam(required = false, defaultValue = "false") Boolean watermark, @RequestParam(required = false) Boolean privateAccess, @RequestParam(required = false, defaultValue = "false") Boolean encode) {
        String ossUrl = ossService.getFileUrl(fileUrl, privateAccess, encode);
        if (watermark) {
            ossUrl = getWatermark(privateAccess, encode, ossUrl);
        }
        return Response.ok(ossUrl);
    }

    @GetMapping("/url/{sceneKey}")
    @Operation(summary = "获取可访问url")
    public Response<String> getFileUrl(@RequestParam String fileUrl,
                                       @RequestParam(required = false) Boolean privateAccess,
                                       @RequestParam(required = false, defaultValue = "false") Boolean encode,
                                       @PathVariable String sceneKey,
                                       @RequestParam(required = false, defaultValue = "false") Boolean watermark) {
        String ossUrl = ossService.getFileUrl(fileUrl, privateAccess, encode, sceneKey);
        if (fileUrl.startsWith("/" + OSSConstant.CONSOLE_FILE_PREFIX)) {
            ossUrl = ossService.getFileUrl(fileUrl, privateAccess, encode);
        }
        if (watermark) {
            ossUrl = getWatermark(privateAccess, encode, ossUrl);
        }
        return Response.ok(ossUrl);
    }

    @PostMapping("/create-form-upload")
    @Operation(summary = "创建表单上传")
    public Response<CreateFormUploadDTO> createFormUpload(@RequestBody CreateUploadRequest request) {
        String prefix = OSSConstant.PORTAL_FILE_PREFIX;
        if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
            prefix = prefix + TrantorContext.getTeamCode() + "/";
        }
        if (StringUtils.isNotBlank(request.getPath())) {
            prefix = request.getPath();
        }
        return Response.ok(ossService.createFormUpload(prefix, request.getFileName(), request.getHeaders(), request.getPrivateAccess()));
    }

    @PostMapping("/complete-form-upload")
    @Operation(summary = "表单上传完成")
    public Response<String> completeFormUpload(@RequestBody CompleteUploadRequest request) {
        return Response.ok(ossService.completeFormUpload(request.getObjectName(), request.getHeaders(), request.getPrivateAccess(), request.getEncode()));
    }

    @PostMapping("/create-upload")
    @Operation(summary = "创建上传")
    public Response<CreateUploadDTO> createMultipartUpload(@RequestBody CreateUploadRequest request) {
        String prefix = OSSConstant.PORTAL_FILE_PREFIX;
        if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
            prefix = prefix + TrantorContext.getTeamCode() + "/";
        }
        if (StringUtils.isNotBlank(request.getPath())) {
            prefix = request.getPath();
        }
        return Response.ok(ossService.createMultipartUpload(prefix, request.getFileName(), request.getHeaders(), request.getPrivateAccess()));
    }

    @PostMapping("/create-upload/{sceneKey}")
    @Operation(summary = "创建上传")
    public Response<CreateUploadDTO> createMultipartUpload(@RequestBody CreateUploadRequest request, @PathVariable String sceneKey)  {
        String prefix = OSSConstant.PORTAL_FILE_PREFIX;
        if (StringUtils.isNotBlank(TrantorContext.getTeamCode())) {
            prefix = prefix + TrantorContext.getTeamCode() + "/";
        }
        if (StringUtils.isNotBlank(request.getPath())) {
            prefix = request.getPath();
        }
        OSSAdapterUploadRequest uploadRequest = new OSSAdapterUploadRequest();
        uploadRequest.setPortalCode(TrantorContext.getCurrentPortalOptional().map(Portal::getCode).orElse(null));
        uploadRequest.setSceneCode(sceneKey);
        uploadRequest.setViewCode(request.getViewCode());
        uploadRequest.setModelCode(request.getModelCode());
        uploadRequest.setCustomizedPath(request.getPath());
        uploadRequest.setUserId(TrantorContext.getCurrentUserId());
        return Response.ok(ossService.createMultipartUpload(uploadRequest, prefix, request.getFileName(), request.getHeaders(), request.getPrivateAccess(), sceneKey));
    }

    @PostMapping("/upload-url")
    @Operation(summary = "获取可上传url")
    public Response<String> getUploadUrl(@RequestBody UploadRequest request) {
        return Response.ok(ossService.getUploadPreSignedUrl(request.getObjectName(), request.getPartNum(), request.getUploadId(), request.getHeaders(), request.getPrivateAccess()));
    }

    @PostMapping("/upload-url/{sceneKey}")
    @Operation(summary = "获取可上传url")
    public Response<String> getUploadUrl(@RequestBody UploadRequest request, @PathVariable String sceneKey)  {
        return Response.ok(ossService.getUploadPreSignedUrl(request.getEndpoint(), request.getBucket(), request.getObjectName(), request.getPartNum(), request.getUploadId(), request.getHeaders(), sceneKey));
    }

    @PostMapping("/complete-upload")
    @Operation(summary = "上传完成")
    public Response<String> completeUpload(@RequestBody CompleteUploadRequest request) {
        return Response.ok(ossService.completeMultipartUpload(request.getObjectName(), request.getUploadId(), request.getHeaders(), request.getPrivateAccess(), request.getEncode()));
    }

    @PostMapping("/complete-upload/{sceneKey}")
    @Operation(summary = "上传完成")
    public Response<String> completeUpload(@RequestBody CompleteUploadRequest request, @PathVariable String sceneKey)  {
        return Response.ok(ossService.completeMultipartUpload(request.getBucket(), request.getObjectName(), request.getUploadId(), request.getHeaders(), request.getPrivateAccess(), request.getEncode(), sceneKey));
    }

    private String getWatermark(Boolean privateAccess, Boolean encode, String ossUrl) {
        SystemConfig systemConfig = configurationService.query(TrantorContext.getTeamId(), TrantorContext.getPortalCode(), ConfigType.System_Config);
        if (systemConfig == null || systemConfig.getPortalWatermark() == null || !systemConfig.getPortalWatermark().getEnable()) {
            return ossUrl;
        }
        return watermarkUtil.watermark(ossUrl, privateAccess, encode, systemConfig.getPortalWatermark());
    }
}
