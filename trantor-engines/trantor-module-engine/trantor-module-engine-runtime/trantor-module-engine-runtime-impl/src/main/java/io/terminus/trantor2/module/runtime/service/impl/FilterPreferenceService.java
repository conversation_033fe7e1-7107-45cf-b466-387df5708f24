package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.runtime.entity.FilterPreference;
import io.terminus.trantor2.module.runtime.entity.FilterPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.repo.FilterPreferenceRepo;
import io.terminus.trantor2.module.runtime.request.*;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Nonnull;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@InternalComponent
@RequiredArgsConstructor
public class FilterPreferenceService extends AbstractPreferenceService<FilterPreference> {
    private final FilterPreferenceRepo preferenceRepo;

    @Override
    public Preference.PreferenceType getType() {
        return Preference.PreferenceType.FILTER;
    }

    @Override
    public FilterPreferenceRepo getPreferenceRepo() {
        return preferenceRepo;
    }

    @Override
    protected <R extends PreferenceSaveRequest> FilterPreference save(R req) {
        if (!(req instanceof FilterPreferenceSaveRequest)) {
            throw new ValidationException("bad request");
        }
        FilterPreferenceSaveRequest request = (FilterPreferenceSaveRequest) req;
        FilterPreference preference = Optional.ofNullable(preferenceRepo.findByScopeAndCurUser(request.getScope()))
                .orElse(newPreference(FilterPreference.class));
        preference.setScope(request.getScope());
        if (preference.getData() == null) {
            preference.setData(new FilterPreferenceData());
        }
        FilterPreferenceData filterPreferenceData = preference.getData();
        if (CollectionUtils.isEmpty(filterPreferenceData.getFilters())) {
            filterPreferenceData.setFilters(new ArrayList<>());
        }
        AtomicBoolean create = new AtomicBoolean(false);
        FilterPreferenceData.Filter filter = Optional.ofNullable(request.getCode())
                .filter(StringUtils::hasText)
                .map(code -> filterPreferenceData.getFilters().stream().filter(f -> f.getCode().equals(code)).findFirst()
                        .orElseThrow(() -> new ValidationException("filter not exist, scope: " + request.getScope() + ", code: " + code)))
                .orElseGet(() -> {
                    FilterPreferenceData.Filter newFilter = new FilterPreferenceData.Filter();
                    newFilter.setCode(UUID.randomUUID().toString());
                    create.set(true);
                    return newFilter;
                });
        filter.setName(request.getName());
        filter.setContent(request.getContent());
        if (create.get()) {
            filterPreferenceData.getFilters().add(filter);
        }
        if (request.isDefaultFilter()) {
            filterPreferenceData.getFilters().forEach(it -> it.setDefaultFilter(false));
            filter.setDefaultFilter(true);
        }
        preference.setData(filterPreferenceData);
        preferenceRepo.saveAndFlush(preference);
        return preference;
    }

    @Override
    @Transactional
    public <R extends PreferenceDeleteRequest> void delete(R req) {
        if (!(req instanceof FilterPreferenceDeleteRequest)) {
            throw new ValidationException("bad request");
        }
        FilterPreferenceDeleteRequest request = (FilterPreferenceDeleteRequest) req;
        FilterPreference preference = preferenceRepo.findByScopeAndCurUser(request.getScope());
        if (preference == null) {
            return;
        }
        if (preference.getData() == null) {
            return;
        }
        FilterPreferenceData data = preference.getData();
        if (CollectionUtils.isEmpty(data.getFilters())) {
            return;
        }
        data.getFilters().removeIf(it -> it.getCode().equals(request.getCode()));
        if (CollectionUtils.isEmpty(data.getFilters())) {
            preferenceRepo.delete(preference);
        } else {
            preferenceRepo.saveAndFlush(preference);
        }
        invalidateData(preference);
    }

    @Override
    public <R extends PreferenceQueryRequest> FilterPreferenceData query(@Nonnull R request) {
        String scope = Optional.of(request)
                .map(PreferenceQueryRequest::getScope)
                .filter(StringUtils::hasText)
                .orElseThrow(() -> new ValidationException("scope is required"));
        FilterPreferenceData data = getData(scope);
        if (data == null) {
            return null;
        }
        List<FilterPreferenceData.Filter> sortedFilters = data.getFilters().stream()
                .sorted(Comparator.comparing(FilterPreferenceData.Filter::isDefaultFilter).reversed()
                        .thenComparing(FilterPreferenceData.Filter::getName))
                .collect(Collectors.toList());
        data.setFilters(sortedFilters);
        return data;
    }

    public List<FilterPreferenceData.Filter> getCurUserPreferenceInScope(String scope) {
        PreferenceQueryRequest request = new PreferenceQueryRequest();
        request.setScope(scope);
        return Optional.ofNullable(query(request)).map(FilterPreferenceData::getFilters).orElse(null);
    }

    @Override
    public void onSceneDeleted(String teamCode, String sceneKey) {
        String scope = sceneKey + "_" + KeyUtil.moduleKey(sceneKey) + "$";
        preferenceRepo.deleteAllByTeamCodeAndScopeStartingWith(teamCode, scope);
    }
}
