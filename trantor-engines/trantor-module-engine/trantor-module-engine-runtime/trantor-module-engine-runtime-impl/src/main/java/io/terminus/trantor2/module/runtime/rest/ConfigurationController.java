package io.terminus.trantor2.module.runtime.rest;

import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.api.ConfigurationApi;
import io.terminus.trantor2.module.dto.PortalConfigDTO;
import io.terminus.trantor2.module.runtime.service.ConfigurationRuntimeService;
import io.terminus.trantor2.module.service.ConfigurationService;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: yang<PERSON><PERSON><PERSON>
 * @date: 2023/8/22 10:52 AM
 **/
@RestController
@AllArgsConstructor
public class ConfigurationController implements ConfigurationApi {
    private final ConfigurationRuntimeService configurationService;
    @Override
    public Response<List<PortalConfigDTO>> queryAllPortalConfig() {
        return Response.ok(configurationService.queryAllPortalConfig());
    }
}
