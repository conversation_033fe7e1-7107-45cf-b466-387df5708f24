package io.terminus.trantor2.module.runtime.service.impl;

import cn.hutool.core.util.NumberUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.runtime.repo.MenuTreeRuntimeRepo;
import io.terminus.trantor2.module.service.MenuRuntimeQueryService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nonnull;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Primary
@Service
@RequiredArgsConstructor
public class MenuQueryRuntimeService implements MenuRuntimeQueryService {
    private final MenuTreeRuntimeRepo repo;
    private final ModuleRuntimeQueryService moduleQueryService;

    @Nonnull
    @Override
    public <T extends Serializable> MenuTreeMeta getMenuTreeMeta(T portalIdOrCode) {
        String portalCode = NumberUtil.isLong(portalIdOrCode.toString()) ?
                moduleQueryService.findKeyById(Long.parseLong(portalIdOrCode.toString())) : portalIdOrCode.toString();

        return Optional.ofNullable(repo.findOneByKey(KeyUtil.newKeyUnderModule(portalCode, MenuTreeMeta.MENU_TREE_SHORT_KEY)))
                .orElse(new MenuTreeMeta());
    }

    @Override
    public Collection<MenuMeta> findMenusByKeys(@Nonnull Collection<String> keys) {
        Map<String, MenuMeta> foundNodes = new HashMap<>();
        Set<String> menuTreeKeys = keys.stream().map(it -> KeyUtil.newKeyUnderModule(KeyUtil.moduleKey(it), MenuTreeMeta.MENU_TREE_SHORT_KEY))
                .collect(Collectors.toSet());

        List<MenuTreeMeta> menuTrees = repo.findAll(menuTreeKeys);
        menuTrees.forEach(tree -> tree.findMenusByKeys(keys).forEach(menu -> foundNodes.put(menu.getKey(), menu)));
        return foundNodes.values();
    }

    @Override
    public List<MenuMeta> findAllBySceneKeyAndTeam(@Nonnull String sceneKey, String teamCode) {
        throw new UnsupportedOperationException("Not implemented yet");
    }
}
