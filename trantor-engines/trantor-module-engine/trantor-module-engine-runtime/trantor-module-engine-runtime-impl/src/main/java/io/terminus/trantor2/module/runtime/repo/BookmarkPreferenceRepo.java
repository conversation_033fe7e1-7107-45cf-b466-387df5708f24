package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.BookmarkPreference;
import io.terminus.trantor2.module.runtime.entity.Preference;

/**
 * <AUTHOR>
 */
@InternalComponent
public interface BookmarkPreferenceRepo extends AbstractPreferenceRepo<BookmarkPreference> {
    @Override
    default Preference.PreferenceType getPreferenceType() {
        return Preference.PreferenceType.BOOKMARK;
    }
}
