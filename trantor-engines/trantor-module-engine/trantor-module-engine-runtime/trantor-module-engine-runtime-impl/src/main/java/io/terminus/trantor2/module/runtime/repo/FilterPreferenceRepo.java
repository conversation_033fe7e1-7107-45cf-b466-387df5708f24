package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.FilterPreference;
import io.terminus.trantor2.module.runtime.entity.Preference;

import java.util.List;

/**
 * <AUTHOR>
 */
@InternalComponent
public interface FilterPreferenceRepo extends AbstractPreferenceRepo<FilterPreference> {
    @Override
    default Preference.PreferenceType getPreferenceType() {
        return Preference.PreferenceType.FILTER;
    }

    List<FilterPreference> findAllByTeamCodeAndModuleKeyAndUserId(String teamCode, String moduleKey, Long userId);

    default List<FilterPreference> findAllByCurUser() {
        validationContext();
        return findAllByTeamCodeAndModuleKeyAndUserId(
                TrantorContext.getTeamCode(),
                TrantorContext.getPortalCode(),
                TrantorContext.getCurrentUserId()
        );
    }
}
