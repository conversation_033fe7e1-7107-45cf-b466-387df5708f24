package io.terminus.trantor2.module.runtime.listener;

import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.meta.event.MetaChangeEvent;
import io.terminus.trantor2.module.runtime.cache.PreferenceCache;
import io.terminus.trantor2.module.runtime.event.PreferenceCacheInvalidateEvent;
import io.terminus.trantor2.module.runtime.service.impl.AbstractPreferenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@InternalComponent
@RequiredArgsConstructor
public class PreferenceListener {
    private final List<AbstractPreferenceService<?>> preferenceServices;
    private final PreferenceCache preferenceCache;

    @EventListener(condition = "#event.editOpType == T(io.terminus.trantor2.meta.api.dto.EditOpType).DeleteNode " +
            "&& #event.oldMetas != null && !T(java.util.Arrays).asList(#event.oldMetas.?[type == 'Scene']).isEmpty()")
    public void onSceneDeleted(MetaChangeEvent event) {
        String sceneKey = event.getOldMetas().stream()
                .filter(it -> it.getType().equals("Scene")).findFirst()
                .map(MetaChangeEvent.MetaId::getKey).orElse(null);
        if (sceneKey == null) {
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug("SceneDeletedEvent: {}", sceneKey);
        }
        preferenceServices.forEach(service -> service.onSceneDeleted(event.getTeamCode(), sceneKey));
    }

    @Async
    @EventListener
    public void invalidatePreferenceCache(PreferenceCacheInvalidateEvent event) {
        preferenceCache.invalidate(event.getCacheKey());
    }
}
