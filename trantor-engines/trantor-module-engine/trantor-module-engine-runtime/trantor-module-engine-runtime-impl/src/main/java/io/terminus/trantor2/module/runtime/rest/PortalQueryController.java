package io.terminus.trantor2.module.runtime.rest;

import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.api.PortalApi;
import io.terminus.trantor2.module.runtime.service.PortalService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/8/22 4:23 PM
 **/
@RestController
@AllArgsConstructor
public class PortalQueryController implements PortalApi {
    private final PortalService portalService;
    @Override
    public Response<Portal> findPortalByDomainName(String domainName) {
        return Response.ok(portalService.findPortalByDomainName(domainName));
    }

    @Override
    public Response<Portal> findPortalByCode(String code) {
        return Response.ok(portalService.findPortalByCode(code));
    }
}
