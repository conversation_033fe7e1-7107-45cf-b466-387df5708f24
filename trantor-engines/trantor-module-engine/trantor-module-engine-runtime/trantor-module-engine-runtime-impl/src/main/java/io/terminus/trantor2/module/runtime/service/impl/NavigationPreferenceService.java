package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.runtime.entity.NavigationPreference;
import io.terminus.trantor2.module.runtime.entity.NavigationPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import io.terminus.trantor2.module.runtime.repo.NavigationPreferenceRepo;
import io.terminus.trantor2.module.runtime.request.NavigationPreferenceSaveRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceSaveRequest;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@InternalComponent
public class NavigationPreferenceService extends AbstractPreferenceService<NavigationPreference> {
    private final NavigationPreferenceRepo repo;

    @Override
    public Preference.PreferenceType getType() {
        return Preference.PreferenceType.NAVIGATION;
    }

    @Override
    public AbstractPreferenceRepo<NavigationPreference> getPreferenceRepo() {
        return repo;
    }

    @Override
    protected <R extends PreferenceSaveRequest> NavigationPreference save(R req) {
        if (!(req instanceof NavigationPreferenceSaveRequest)) {
            throw new ValidationException("bad request");
        }
        NavigationPreferenceSaveRequest request = (NavigationPreferenceSaveRequest) req;

        NavigationPreference preference = Optional.ofNullable(repo.findByScopeAndCurUser(request.getScope()))
                .orElse(newPreference(NavigationPreference.class));
        preference.setScope(request.getScope());
        if (preference.getData() == null) {
            preference.setData(new NavigationPreferenceData());
        }
        NavigationPreferenceData data = preference.getData();
        data.setNavigations(request.getNavigations());

        repo.saveAndFlush(preference);
        return preference;
    }

    @Override
    public void onSceneDeleted(String teamCode, String sceneKey) {
        String scope = sceneKey + "::";
        repo.deleteAllByTeamCodeAndScopeStartingWith(teamCode, scope);
    }
}
