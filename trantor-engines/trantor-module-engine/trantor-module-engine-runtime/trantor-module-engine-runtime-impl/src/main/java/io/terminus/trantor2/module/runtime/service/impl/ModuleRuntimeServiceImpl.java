package io.terminus.trantor2.module.runtime.service.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.meta.PortalRelationConfig;
import io.terminus.trantor2.module.model.dto.ModuleDTO;
import io.terminus.trantor2.module.runtime.repo.ModuleRuntimeRepo;
import io.terminus.trantor2.module.runtime.service.ConfigurationRuntimeService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor
public class ModuleRuntimeServiceImpl implements ModuleRuntimeQueryService {
    private final ModuleRuntimeRepo runtimeRepo;
    private final MetaQueryService queryService;
    private final ConfigurationRuntimeService configurationService;

    private final static Map<Long, String> ID2KEY_CACHE = new ConcurrentHashMap<>();

    @Override
    public ModuleMeta findByKey(String key) {
        return Optional.ofNullable(runtimeRepo.findOneByKey(key)).map(this::fillConfig).orElseThrow(() -> new TrantorRuntimeException("module not found, key is " + key));
    }

    @Override
    public Collection<ModuleMeta> findAllByKeysIn(Collection<String> keys) {
        return runtimeRepo.findAll(keys);
    }

    @Deprecated
    @Override
    public ModuleMeta findById(Long id) {
        return queryService.findById(id).map(ModuleMeta::convert).orElseThrow(() -> new TrantorRuntimeException("module not found, id:" + id));
    }

    @Override
    public String findKeyById(Long id) {
        return ID2KEY_CACHE.computeIfAbsent(id, (key) -> {
            try {
                return findById(id).getKey();
            } catch (Exception e) {
                log.error("module not found", e);
            }
            return null;
        });
    }

    @Override
    public ModuleDTO queryPortalRelation(String moduleKey) {
        PortalRelationConfig config = configurationService.query(TrantorContext.getTeamId(), moduleKey, ConfigType.Portal_Config);
        if (Objects.isNull(config) || CollectionUtils.isEmpty(config.getRelationKeys())) {
            return safeFindByKey(moduleKey)
                .map(app -> {
                    ModuleDTO moduleDTO = new ModuleDTO();
                    moduleDTO.setAppId(app.getId());
                    moduleDTO.setTeamId(app.getTeamId());
                    moduleDTO.setKey(app.getKey());
                    moduleDTO.setName(app.getName());
                    return moduleDTO;
                })
                .orElseThrow(() -> new TrantorRuntimeException("portal not found, key is " + moduleKey));
        }
        List<String> allPortalKey = config.getRelationKeys();
        allPortalKey.add(moduleKey);
        Map<String, ModuleDTO> map = runtimeRepo.findAll(allPortalKey).stream()
            .map(app -> {
                ModuleDTO moduleDTO = new ModuleDTO();
                moduleDTO.setAppId(app.getId());
                moduleDTO.setTeamId(app.getTeamId());
                moduleDTO.setKey(app.getKey());
                moduleDTO.setName(app.getName());
                return moduleDTO;
            })
            .collect(Collectors.toMap(ModuleDTO::getKey, Function.identity()));

        ModuleDTO moduleDTO = map.remove(moduleKey);
        moduleDTO.setRelatedModules(Lists.newArrayList(map.values()));

        return moduleDTO;
    }

    private Optional<ModuleMeta> safeFindByKey(String key) {
        return Optional.ofNullable(runtimeRepo.findOneByKey(key));
    }

    private ModuleMeta fillConfig(ModuleMeta meta) {
        if (meta.getResourceProps() != null) {
            meta.getResourceProps().setConfig(configurationService.query(meta.getTeamId(), meta.getKey(), ConfigType.Module_IAM));
        }
        return meta;
    }
}
