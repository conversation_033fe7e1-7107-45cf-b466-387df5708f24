package io.terminus.trantor2.module.runtime.rest;

import io.terminus.trantor2.common.internal.Internal;
import io.terminus.trantor2.module.runtime.cache.PreferenceCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Internal
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/trantor/devops/preference/cache")
public class PreferenceDevOpController {
    private final PreferenceCache preferenceCache;

    @GetMapping("/size")
    public Long size() {
        return preferenceCache.size();
    }

    @GetMapping("/clear")
    public void clear() {
        preferenceCache.invalidateAll();
        log.info("clear preference cache finish");
    }
}
