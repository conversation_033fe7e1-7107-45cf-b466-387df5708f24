package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.module.runtime.entity.Preference;
import org.jooq.tools.StringUtils;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 */
public interface AbstractPreferenceRepo<T extends Preference<?>> extends JpaRepository<T, Long> {
    default Preference.PreferenceType getPreferenceType() {
        return null;
    }

    T findByTeamCodeAndModuleKeyAndUserIdAndScope(String teamCode, String moduleKey, Long userId, String scope);

    void deleteAllByTeamCodeAndScopeStartingWith(String teamCode, String scope);

    default T findByCurUser() {
        return findByScopeAndCurUser(null);
    }

    default T findByScopeAndCurUser(String scope) {
        validationContext();
        return findByTeamCodeAndModuleKeyAndUserIdAndScope(TrantorContext.getTeamCode(), TrantorContext.getPortalCode(),
                TrantorContext.getCurrentUserId(), scope);
    }

    default void validationContext() {
        if (StringUtils.isEmpty(TrantorContext.getPortalCode())) {
            throw new ValidationException("bad portal key");
        }
        if (TrantorContext.getTeamCode() == null) {
            throw new TrantorRuntimeException("bad team");
        }
        if (TrantorContext.getCurrentUserId() == null) {
            throw new TrantorRuntimeException("user not login");
        }
    }
}
