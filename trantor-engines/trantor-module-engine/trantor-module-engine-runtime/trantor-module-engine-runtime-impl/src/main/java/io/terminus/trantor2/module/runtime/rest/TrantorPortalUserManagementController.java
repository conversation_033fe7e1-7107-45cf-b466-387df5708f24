package io.terminus.trantor2.module.runtime.rest;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.iam.api.request.user.LoginOnceCodeParams;
import io.terminus.iam.api.request.user.ThirdPartyAccountBindStatusParams;
import io.terminus.iam.api.request.user.UnbindThirdPartyAccountParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.iam.api.response.common.FrontEndConfig;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.LoginOnceCodeResult;
import io.terminus.iam.api.response.user.UserGroup;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.user.UserVO;
import io.terminus.trantor2.iam.dto.ApplicationParam;
import io.terminus.trantor2.iam.dto.ApplicationVO;
import io.terminus.trantor2.iam.dto.ThirdPartyAccountParam;
import io.terminus.trantor2.iam.dto.UserLoginOnceCodeParam;
import io.terminus.trantor2.iam.dto.UserLoginOnceCodeResp;
import io.terminus.trantor2.iam.dto.UserPagingParam;
import io.terminus.trantor2.iam.dto.UserQueryParam;
import io.terminus.trantor2.iam.dto.role.AssignRoleRequest;
import io.terminus.trantor2.iam.dto.usergroup.AssignUserGroupRequest;
import io.terminus.trantor2.iam.service.TrantorIAMApplicationConfigService;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.module.adapter.IamModuleMetaAdapter;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheCleanser;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import io.terminus.trantor2.permission.runtime.api.service.PortalRoleService;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户管理
 *
 * <AUTHOR>
 * 2022/11/24 3:02 下午
 */
@Tag(name = "用户管理接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/portal/user")
@RequiredArgsConstructor
@TrantorServiceRegistry
public class TrantorPortalUserManagementController {
    private final TrantorIAMUserService trantorIAMUserService;
    private final TrantorIAMApplicationConfigService trantorIAMApplicationConfigService;
    private final IamModuleMetaAdapter iamModuleMetaAdapter;
    private final PortalRoleService roleService;
    private final PermissionCacheCleanser permissionCacheCleanser;
    private final static String CURRENT = "current";
    private final static String RELATION = "relation";
    private final static String ALL = "all";

    /**
     * 用户列表分页查询
     *
     * @param params 查询条件
     * @return 用户分页列表
     */
    @PostMapping("/paging")
    @Operation(summary = "用户列表分页查询")
    public Response<Paging<UserVO>> paging(@RequestBody UserPagingParam params) {
        UserPagingParams userPagingParams = params.getParams();
        UserPagingParams pagingParams = handleDataScope(params.getScope(), userPagingParams);
        if (pagingParams != null) {
            // 是否脱敏
            boolean removeSensitive = handleRemoveSensitive(params.getRemoveSensitiveEnabled());
            Paging<UserVO> userVOS = trantorIAMUserService.pagingVO(pagingParams, removeSensitive);
            return Response.ok(userVOS);
        }
        return Response.ok();
    }

    @PostMapping("/paging/dynamic-portal")
    @Operation(summary = "用户列表分页查询（指定具体门户 [隔离iam的用户池]）")
    public Response<Paging<UserVO>> pagingByPool(@RequestBody UserPagingParam params) {
        Paging<UserVO> userPaging = new Paging<>();
        UserPagingParams userPagingParams = params.getParams();
        UserPagingParams pagingParams = handleDataScope(params.getScope(), userPagingParams);
        if (pagingParams != null) {
            // 获取指定的门户
            Portal portal;
            Long signByApplication = userPagingParams.getSignByApplication();
            if (signByApplication != null) {
                portal = iamModuleMetaAdapter.getPortalByIamAppId(signByApplication);
                if (portal == null) return Response.ok();
            } else {
                portal = TrantorContext.getCurrentPortal();
            }
            // 是否脱敏
            boolean removeSensitive = handleRemoveSensitive(params.getRemoveSensitiveEnabled());
            userPaging = trantorIAMUserService.pagingVOByPortal(pagingParams, portal, removeSensitive);
        }
        return Response.ok(userPaging);
    }

    /**
     * 获取IAM应用列表
     *
     * @return 用户分页列表
     */
    @GetMapping("/get-iam-app-list")
    @Operation(summary = "获取IAM应用列表")
    public Response<List<ApplicationVO>> getRelationApp(ApplicationParam param) {
        List<ApplicationVO> iamAppList = new ArrayList<>();
        String scope = Optional.ofNullable(param).map(ApplicationParam::getScope).orElse(null);
        if (scope == null) {
            return Response.ok();
        } else {
            switch (scope) {
                case CURRENT:
                    ApplicationVO applicationVO = new ApplicationVO();
                    applicationVO.setId(TrantorContext.getCurrentPortal().getIamEndpointId());
                    applicationVO.setName(TrantorContext.getCurrentPortal().getName());
                    iamAppList.add(applicationVO);
                    break;
                case RELATION:
                    iamAppList = iamModuleMetaAdapter.findRelatedIamApps(TrantorContext.getCurrentPortal());
                    break;
                case ALL:
                    iamAppList = iamModuleMetaAdapter.findAllIamApplicationByTeamId(TrantorContext.getTeamId());
                    break;
                default:
                    return Response.ok();
            }
        }
        return Response.ok(iamAppList);
    }

    @GetMapping("/get-front-end-config")
    @Operation(summary = "获取当前应用前端配置")
    public Response<FrontEndConfig> getPortalConfig() {
        return Response.ok(trantorIAMApplicationConfigService.findFrontEndConfigByIAMAppId(TrantorContext.getCurrentPortal().getIamEndpointId()));
    }

    /**
     * 通过用户id查询用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "通过用户id查询用户信息")
    public Response<UserVO> getUserById(@PathVariable Long id, UserQueryParam param) {
        if (param != null && param.getRemoveSensitiveEnabled() != null && param.getRemoveSensitiveEnabled()) {
            return Response.ok(trantorIAMUserService.findByIdVO(id));
        } else {
            return Response.ok(trantorIAMUserService.findPlaintextByIdVO(id));
        }
    }

    @GetMapping("/get-user-info")
    @Operation(summary = "获取用户信息")
    public Response<User> getUserInfo() {
        return Response.ok(TrantorContext.getCurrentUser());
    }


    @Operation(summary = "获取指定用户的一次性登录code")
    @PostMapping("/get-login-once-code")
    public Response<UserLoginOnceCodeResp> getUserOnceCode(@RequestBody UserLoginOnceCodeParam param, HttpServletRequest request, HttpServletResponse response) {
        LoginOnceCodeParams loginOnceCodeParams = new LoginOnceCodeParams();
        loginOnceCodeParams.setOperatorId(Objects.requireNonNull(TrantorContext.getCurrentUser()).getId());
        loginOnceCodeParams.setTargetUserId(param.getTargetUserId());
        loginOnceCodeParams.setTargetIamAppKey(param.getTargetIamAppKey());
        loginOnceCodeParams.setRedirectUrl(param.getRedirectUrl());
        LoginOnceCodeResult loginOnceCode = trantorIAMUserService.getLoginOnceCode(loginOnceCodeParams);
        log.info("get user once code, params: {}, result: {}", loginOnceCodeParams, loginOnceCode);
        UserLoginOnceCodeResp resp = new UserLoginOnceCodeResp();
        resp.setRedirectUrl(loginOnceCode.getLoginUrl());
        return Response.ok(resp);
    }


    @Operation(summary = "解绑当前用户第三方平台账号")
    @PostMapping("/unbind-third-party-account")
    public Response<Boolean> unbindThirdPartyAccount(@RequestBody @Validated ThirdPartyAccountParam params) {
        UnbindThirdPartyAccountParams accountParams = new UnbindThirdPartyAccountParams();
        accountParams.setUserId(TrantorContext.getCurrentUserId());
        accountParams.setThirdPartyType(params.getThirdPartyType());
        return Response.ok(trantorIAMUserService.unbindThirdPartyAccount(accountParams));
    }

    @Operation(summary = "查询当前用户第三方账号绑定状态")
    @PostMapping("/find-third-party-bind-status")
    public Response<Boolean> getThirdPartyBindStatus(@RequestBody @Validated ThirdPartyAccountParam params) {
        ThirdPartyAccountBindStatusParams bindStatusParams = new ThirdPartyAccountBindStatusParams();
        bindStatusParams.setUserId(TrantorContext.getCurrentUserId());
        bindStatusParams.setThirdPartyType(params.getThirdPartyType());
        return Response.ok(trantorIAMUserService.getThirdPartyBindStatus(bindStatusParams));
    }

    @Operation(summary = "创建")
    @PostMapping("/create")
    public Response<UserVO> create(@RequestBody CompleteUserParams params) {
        return Response.ok(trantorIAMUserService.createVO(params));
    }

    @Operation(summary = "更新")
    @PostMapping("/{id}/update")
    public Response<Boolean> update(@PathVariable Long id, @RequestBody CompleteUserParams params) {
        return Response.ok(trantorIAMUserService.updateById(id, params));
    }

    @Operation(summary = "禁用")
    @PostMapping("/{id}/disabled")
    public Response<Boolean> disabledById(@PathVariable Long id) {
        return Response.ok(trantorIAMUserService.disabledById(id));
    }

    @Operation(summary = "启用")
    @PostMapping("/{id}/enabled")
    public Response<Boolean> enabledById(@PathVariable Long id) {
        return Response.ok(trantorIAMUserService.enabledById(id));
    }

    @Operation(summary = "用户列表-用户解锁")
    @PostMapping("/{id}/unlock")
    public Response<Boolean> unlockById(@PathVariable Long id) {
        return Response.ok(trantorIAMUserService.unlockById(id));
    }

    @Operation(summary = "重置密码")
    @PostMapping("/{id}/reset-password")
    public Response<Boolean> resetPasswordById(@PathVariable Long id, @RequestBody CompleteUserParams params) {
        return Response.ok(trantorIAMUserService.resetPasswordById(id, params));
    }

    @Operation(summary = "注销")
    @PostMapping("/{id}/destroy")
    public Response<Boolean> destroyById(@PathVariable Long id) {
        return Response.ok(trantorIAMUserService.destroyById(id));
    }

    @Operation(summary = "用户-查询用户关联用户组列表")
    @GetMapping("/{id}/assigned-user-groups")
    public Response<Collection<UserGroup>> findUserGroupsByUserId(@PathVariable Long id) {
        // 查询用户关联用户组（用户组为Portal创建的）
        List<UserGroup> userGroups = trantorIAMUserService.findUserGroupsByUserId(id).stream()
                .filter(userGroup -> PermissionConstants.TRANTOR_PORTAL_USER_GROUP.equals(userGroup.getType()))
                .collect(Collectors.toList());
        return Response.ok(userGroups);
    }

    @Operation(summary = "用户-用户分配用户组")
    @PostMapping("/{id}/remove-or-assign-user-groups")
    public Response<Void> removeOrAssignUserGroups(@PathVariable Long id,
                                                   @RequestBody @Validated AssignUserGroupRequest request) {
        trantorIAMUserService.removeOrAssignUserGroups(id, request);
        permissionCacheCleanser.invalidateUserCache(id);
        return Response.ok();
    }

    @Operation(summary = "用户-查询用户关联角色列表")
    @GetMapping("/{id}/assigned-roles")
    public Response<Collection<Role>> findRolesByUserId(@PathVariable Long id) {
        // 查询用户关联角色（角色为Portal创建的）
        return Response.ok(roleService.findRoleByUserId(id));
    }

    @Operation(summary = "用户-用户分配角色")
    @PostMapping("/{id}/remove-or-assign-roles")
    public Response<Void> removeOrAssignRoles(@PathVariable Long id,
                                              @RequestBody @Validated AssignRoleRequest request) {
        trantorIAMUserService.removeOrAssignRoles(id, request);
        permissionCacheCleanser.invalidateUserCache(id);
        return Response.ok();
    }

    /**
     * 处理脱敏
     *
     * @param removeSensitiveEnabled 是否脱敏
     * @return 是否脱敏
     */
    private boolean handleRemoveSensitive(Boolean removeSensitiveEnabled) {
        return removeSensitiveEnabled != null && removeSensitiveEnabled;
    }


    /**
     * 处理查询数据范围
     *
     * @param scope            数据范围
     * @param userPagingParams 用户分页查询参数
     * @return 是否查询数据
     */
    private UserPagingParams handleDataScope(String scope, UserPagingParams userPagingParams) {
        UserPagingParams params = null;
        if (scope != null) {
            switch (scope) {
                case CURRENT:
                    userPagingParams.setSignByApplication(TrantorContext.getCurrentPortal().getIamEndpointId());
                    params = userPagingParams;
                    break;
                case RELATION:
                    Map<String, ModuleIamConfig> relatedIamConfigs = iamModuleMetaAdapter.getRelatedIamConfigs(TrantorContext.getCurrentPortal());
                    if (CollectionUtil.isNotEmpty(relatedIamConfigs)) {
                        userPagingParams.setSignByApplicationSet(relatedIamConfigs.values().stream().map(ModuleIamConfig::getIamEndPointId).collect(Collectors.toSet()));
                    }
                    params = userPagingParams;
                    break;
                case ALL:
                    params = userPagingParams;
                    break;
                default:
                    break;
            }
        }
        return params;
    }
}
