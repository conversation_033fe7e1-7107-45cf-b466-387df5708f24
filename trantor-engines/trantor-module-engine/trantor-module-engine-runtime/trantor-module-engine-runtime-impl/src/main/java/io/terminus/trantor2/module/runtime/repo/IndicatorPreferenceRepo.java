package io.terminus.trantor2.module.runtime.repo;

import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.IndicatorPreference;
import io.terminus.trantor2.module.runtime.entity.Preference;

/**
 * <AUTHOR>
 */
@InternalComponent
public interface IndicatorPreferenceRepo extends AbstractPreferenceRepo<IndicatorPreference> {
    @Override
    default Preference.PreferenceType getPreferenceType() {
        return Preference.PreferenceType.INDICATOR;
    }
}
