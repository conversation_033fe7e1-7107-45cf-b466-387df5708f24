package io.terminus.trantor2.module.runtime.controller;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.ide.repository.OverviewProcessRepo;
import io.terminus.trantor2.ide.repository.OverviewRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.objects.tree.folder.Folder;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.service.FolderService;
import io.terminus.trantor2.overview.OverviewMeta;
import io.terminus.trantor2.overview.OverviewProcessMeta;
import io.terminus.trantor2.overview.OverviewProcessNode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * TODO Process有个AOP，不知道影响有多大，文档相关接口暂时放在module下
 *
 * <AUTHOR>
 */
@Tag(name = "Portal 产品文档查询")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/trantor/portal/overview")
public class OverviewRuntimeController {

    private final FolderService folderService;
    private final OverviewProcessRepo overviewProcessRepo;
    private final OverviewRepo overviewRepo;
    private final MetaQueryService metaQueryService;


    @GetMapping(value = "/folders")
    public Response<List<Folder>> findFolders() {
        return Response.ok(folderService.folders(TrantorContext.getTeamId(), KeyUtil.SYS_OVERVIEW_KEY, true));
    }

    @GetMapping(value = "/process/query")
    public Response<OverviewProcessMeta> querySingleMeta(@RequestParam String key) {
        Optional<OverviewProcessMeta> opt = overviewProcessRepo.findOneByKey(key, ResourceContext.ctxFromThreadLocal());
        if (!opt.isPresent()) {
            return Response.ok();
        }
        OverviewProcessMeta overviewProcessMeta = opt.get();
        enrichOverviewProcess(overviewProcessMeta);

        return Response.ok(overviewProcessMeta);
    }

    @GetMapping(value = "/process/query-by-scene")
    public Response<List<OverviewProcessMeta>> queryOverviewProcessByScene(@RequestParam(required = false) String sceneKey,
                                                                           @RequestParam(required = false) String viewKey) {
        if (StringUtils.isBlank(sceneKey) && StringUtils.isBlank(viewKey)) {
            return Response.ok();
        }
        List<String> overviewProcessKeys;
        if (StringUtils.isNotBlank(viewKey)) {
            overviewProcessKeys = metaQueryService.findUsageKeysByKey(EditUtil.ctxFromThreadLocal(),
                    viewKey, Lists.newArrayList(MetaType.OverviewProcess.name()));
        } else {
            overviewProcessKeys = metaQueryService.findUsageKeysByKey(EditUtil.ctxFromThreadLocal(),
                    sceneKey, Lists.newArrayList(MetaType.OverviewProcess.name()));
        }
        List<OverviewProcessMeta> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(overviewProcessKeys)) {
            result = overviewProcessRepo.findAllByKeys(overviewProcessKeys, ResourceContext.ctxFromThreadLocal());
            result.forEach(this::enrichOverviewProcess);
        }

        return Response.ok(result);
    }

    private void enrichOverviewProcess(OverviewProcessMeta overviewProcessMeta) {
        List<OverviewMeta> overviews = overviewProcessMeta.getResourceProps().getOverviews();
        if (CollectionUtils.isNotEmpty(overviews)) {
            List<String> overviewKeys = overviews.stream()
                    .map(OverviewMeta::getKey)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            overviews = overviewRepo.findAllByKeys(overviewKeys, ResourceContext.ctxFromThreadLocal());
            overviewProcessMeta.getResourceProps().setOverviews(overviews);
        }

        if (CollectionUtils.isNotEmpty(overviewProcessMeta.getResourceProps().getProcessNodes())) {
            enrichOverview(overviewProcessMeta.getResourceProps().getProcessNodes());
        }
    }

    private void enrichOverview(List<OverviewProcessNode> processNodes) {
        Set<String> overviewKeys = processNodes.stream()
                .map(OverviewProcessNode::getOverviewKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<OverviewMeta> overviewMetas = overviewRepo.findAllByKeys(overviewKeys, ResourceContext.ctxFromThreadLocal());
        Map<String, OverviewMeta> overviewMetaMap = overviewMetas.stream()
                .collect(Collectors.toMap(OverviewMeta::getKey, o -> o));

        processNodes.forEach(processNode -> {
            OverviewMeta overviewMeta = overviewMetaMap.get(processNode.getOverviewKey());
            if (overviewMeta != null && overviewMeta.getResourceProps() != null) {
                processNode.setContent(overviewMeta.getResourceProps().getContent());
            }
        });
    }

}
