package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.module.runtime.cache.PreferenceCache;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import io.terminus.trantor2.module.runtime.request.PreferenceDeleteRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceQueryRequest;
import io.terminus.trantor2.module.runtime.request.PreferenceSaveRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPreferenceService<T extends Preference<?>> {
    @Autowired
    protected PreferenceCache preferenceCache;

    public abstract Preference.PreferenceType getType();

    public abstract AbstractPreferenceRepo<T> getPreferenceRepo();

    protected abstract <R extends PreferenceSaveRequest> T save(R req);

    @Transactional
    public <R extends PreferenceSaveRequest> void savePreference(R req) {
        T preference = save(req);
        invalidateData(preference);
    }

    public <R extends PreferenceDeleteRequest> void delete(R req) {
        throw new UnsupportedOperationException("delete not supported");
    }

    public <R extends PreferenceQueryRequest> PreferenceData query(@Nonnull R request) {
        String scope = Optional.of(request)
                .map(PreferenceQueryRequest::getScope)
                .filter(StringUtils::hasText)
                .orElseThrow(() -> new ValidationException("scope is required"));
        return getData(scope);
    }

    public T newPreference(Class<T> clazz) {
        T instance;
        try {
            instance = clazz.getDeclaredConstructor().newInstance();
            instance.setTeamCode(Optional.ofNullable(TrantorContext.getTeamCode())
                    .orElseThrow(() -> new TrantorRuntimeException("unable to get teamCode in request and context")));
            instance.setModuleKey(Optional.ofNullable(TrantorContext.getPortalCode())
                    .orElseThrow(() -> new TrantorRuntimeException("unable to get portal code in request and context")));
            instance.setUserId(Optional.ofNullable(TrantorContext.getCurrentUserId())
                    .orElseThrow(() -> new TrantorRuntimeException("unable to get userId in request and context")));
        } catch (Exception e) {
            log.error("resource has not a no args constructor, [{}]", clazz.getName());
            throw new TrantorRuntimeException("new instance resource failed");
        }
        return instance;
    }

    public void onSceneDeleted(String teamCode, String sceneKey) {
    }

    protected <DATA extends PreferenceData> DATA getData(@Nullable String scope) {
        return preferenceCache.get(getType(), scope);
    }

    protected void invalidateData(T preference) {
        preferenceCache.invalidate(preference);
    }
}
