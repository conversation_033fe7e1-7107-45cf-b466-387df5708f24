package io.terminus.trantor2.module.runtime.service.impl;

import com.google.common.collect.Lists;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.PortalI18nConfig;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.module.dto.MenuIconDTO;
import io.terminus.trantor2.module.entity.Position;
import io.terminus.trantor2.module.meta.*;
import io.terminus.trantor2.module.runtime.entity.IconCollector;
import io.terminus.trantor2.module.runtime.repo.ModuleRuntimeRepo;
import io.terminus.trantor2.module.runtime.service.ConfigurationRuntimeService;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.module.service.MenuRuntimeQueryService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import io.terminus.trantor2.module.service.TeamService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2022/9/21 5:41 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class PortalServiceImpl implements PortalService {

    private final ModuleRuntimeRepo moduleRepo;
    private final ConfigurationRuntimeService configurationService;
    private final TeamService teamService;
    private final ModuleRuntimeQueryService moduleService;
    private final IconRuntimeService iconService;
    private final MenuRuntimeQueryService menuService;

    @Override
    public Portal findPortalByDomainName(String domainName) {
        List<Position> collect = configurationService.findPositionByDomain(domainName);
        //存在多个同域名portal，需要通过path取当前portal
        if (collect.size() != 1) {
            return null;
        }
        Position position = collect.get(0);

        String teamCode = teamService.getTeamCode(position.getTeamId());
        TrantorContext.setTeamId(position.getTeamId());
        TrantorContext.setTeamCode(teamCode);

        ModuleMeta module = moduleRepo.findOneByKey(position.getMetaKey());
        ModuleIamConfig moduleIamConfig = configurationService.query(position.getTeamId(), module.getKey(), ConfigType.Module_IAM);
        TrantorContext.setTeamCode(teamCode);

        return Portal.builder()
            .id(module.getId())
            .teamId(module.getTeamId())
            .teamCode(teamCode)
            .code(module.getKey())
            .name(module.getName())
            .iamEndpointId(moduleIamConfig.getIamEndPointId())
            .loginUrl(moduleIamConfig.getLoginUrl())
            .loginCallbackUrl(moduleIamConfig.getLoginCallbackUrl())
            .accessKey(moduleIamConfig.getAccessKey())
            .accessSecret(moduleIamConfig.getAccessSecret())
            .i18nConfig(getPortalI18nConfig(position.getTeamId(), module.getKey()))
            .applicationKey(moduleIamConfig.getApplicationKey())
            .build();
    }

    @Override
    public Portal findPortalByCode(String code) {
        ModuleMeta module;
        ModuleIamConfig moduleIamConfig;
        String teamCode = null;
        if (code.contains("-")) {
            String portalCode = code.split("-")[0];
            teamCode = code.split("-")[1];
            Long teamId = teamService.getTeamIdByCode(teamCode);
            TrantorContext.setTeamId(teamId);
            TrantorContext.setTeamCode(teamCode);
            module = moduleService.findByKey(portalCode);
            moduleIamConfig = module.getResourceProps().getConfig();
        } else if (TrantorContext.getTeamCode() != null) {
            module = moduleService.findByKey(code);
            moduleIamConfig = module.getResourceProps().getConfig();
        } else {
            return null;
        }
        if (!module.isPortal()) {
            return null;
        }
        String path = null;
        String url = moduleIamConfig.getLoginCallbackUrl();
        if (url != null && url.contains("/")) {
            path = url.substring(url.lastIndexOf('/') + 1);
        }

        return Portal.builder()
            .id(module.getId())
            .teamId(module.getTeamId())
            .teamCode(module.getTeamCode())
            .code(module.getKey())
            .name(module.getName())
            .path(path)
            .iamEndpointId(moduleIamConfig.getIamEndPointId())
            .loginUrl(moduleIamConfig.getLoginUrl())
            .loginCallbackUrl(moduleIamConfig.getLoginCallbackUrl())
            .accessKey(moduleIamConfig.getAccessKey())
            .accessSecret(moduleIamConfig.getAccessSecret())
            .i18nConfig(getPortalI18nConfig(module.getTeamId(), module.getKey()))
            .applicationKey(moduleIamConfig.getApplicationKey())
            .build();
    }

    @Override
    public IconCollector queryAllIcon(Long teamId, String moduleKey, String packageKey) {
//        List<PortalIcon> portalIcons = iconService.queryPortalIcon(teamId, moduleKey);
        List<MenuIcon> menuIcons = iconService.queryMenuIcon(teamId, moduleKey, packageKey);

        IconCollector collector = new IconCollector();
        collector.setPortalIcons(Lists.newArrayList());
        collector.setMenuIcons(MenuIconDTO.convert(menuIcons));

        return collector;
    }

    @Override
    public Portal findPortalByKey(String moduleKey) {
        if (TrantorContext.getTeamCode() == null && TrantorContext.getTeamId() == null) {
            throw new TrantorRuntimeException("param error, team can't be null");
        }
        if (TrantorContext.getTeamCode() == null) {
            TrantorContext.setTeamCode(teamService.getTeamCode(TrantorContext.getTeamId()));
        }
        ModuleMeta module = moduleRepo.findOneByKey(moduleKey);
        if (module == null) {
            return null;
        }
        ModuleIamConfig moduleIamConfig = configurationService.query(module.getTeamId(), module.getKey(), ConfigType.Module_IAM);
        if (Objects.isNull(moduleIamConfig)) {
            return null;
        }
        return Portal.builder()
            .id(module.getId())
            .teamId(module.getTeamId())
            .teamCode(TrantorContext.getTeamCode())
            .code(module.getKey())
            .name(module.getName())
            .iamEndpointId(moduleIamConfig.getIamEndPointId())
            .loginUrl(moduleIamConfig.getLoginUrl())
            .loginCallbackUrl(moduleIamConfig.getLoginCallbackUrl())
            .accessKey(moduleIamConfig.getAccessKey())
            .accessSecret(moduleIamConfig.getAccessSecret())
            .i18nConfig(getPortalI18nConfig(module.getTeamId(), module.getKey()))
            .applicationKey(moduleIamConfig.getApplicationKey())
            .build();
    }

    @Override
    public void setRefererPathIfNecessary(Portal portal) {
        String url = portal.getLoginCallbackUrl();
        if (!url.matches("^(http[s]?://).+")) {
            url = "http://" + url;
        }
        try {
            String path = new URL(url).getPath();
            if (StringUtils.isBlank(path)) {
                return;
            }
            path = path.replaceFirst("/", "");
            portal.setPath(path);
            portal.setObtainType(Portal.ObtainType.refererPath);
        } catch (Exception e) {
            log.error("parse referer failed", e);
        }
    }

    @Override
    public String fetchAccessUrl(Portal portal, String sceneKey, String viewKey, String action, String recordId) {
        if (portal == null || sceneKey == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(portal.getLoginCallbackUrl());
        sb.append("/");
        sb.append(portal.getCode());
        sb.append("/");
        Optional<MenuMeta> menu = menuService.getMenuTreeMeta(portal.getCode()).flatMenuAndChildrenRecursively()
                .stream().filter(it -> it.routeScene() && it.getRouteConfig("sceneKey").equals(sceneKey)).findFirst();
        if (!menu.isPresent()) {
            return null;
        }
        sb.append(menu.get().getKey());
        sb.append("/");
        sb.append("page?");
        sb.append("_tab_id=").append(RandomStringUtils.randomAlphanumeric(6));
        sb.append("&sceneKey=").append(sceneKey);
        if (recordId != null) {
            sb.append("&recordId=").append(recordId);
        }
        if (action != null) {
            sb.append("&action=").append(action);
        }
        if (viewKey != null) {
            sb.append("&viewKey=").append(viewKey);
        }
        return sb.toString();
    }

    private PortalI18nConfig getPortalI18nConfig(Long teamId, String portalCode) {
        I18nConfig i18nConfig = configurationService.query(teamId, portalCode, ConfigType.I18n_Config);
        if (i18nConfig == null) {
            i18nConfig = I18nConfig.defaultConfig();
        }
        return new PortalI18nConfig(i18nConfig.getEnabled(), i18nConfig.getDefaultLang(),
            i18nConfig.getLanguages()
                .stream().filter(I18nLanguagePack::getEnabled)
                .map(it -> PortalI18nConfig.PortalI18nLanguage.of(it.getKey(), it.getName(), it.getDownloadUrl()))
                .collect(Collectors.toList()));
    }

}
