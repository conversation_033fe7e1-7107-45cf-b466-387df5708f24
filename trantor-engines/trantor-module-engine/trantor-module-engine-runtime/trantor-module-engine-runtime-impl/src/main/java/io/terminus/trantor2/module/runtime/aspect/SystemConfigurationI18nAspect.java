package io.terminus.trantor2.module.runtime.aspect;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.module.i18n.I18nTarget;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.SystemConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static io.terminus.trantor2.module.constants.I18nConst.PORTAL_PREFIX;

/**
 * ConfigurationService.query 方法的国际化处理切面
 * 专门处理 ConfigType.System_Config 类型的请求
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class SystemConfigurationI18nAspect {
    private final I18nTarget i18nTarget;
    private static final String NAME = "name";

    /**
     * 拦截 ConfigurationService.query 方法
     * 当第三个参数为 ConfigType.System_Config 时进行国际化处理
     */
    @Around("execution(* io.terminus.trantor2.module.service.ConfigurationService.query(..))")
    public Object aroundQuery(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            Object[] args = joinPoint.getArgs();

            if (args.length >= 3 && args[2] instanceof ConfigType) {
                ConfigType configType = (ConfigType) args[2];
                if (ConfigType.System_Config.equals(configType)) {
                    Object result = joinPoint.proceed();
                    if (result instanceof SystemConfig) {
                        return processSystemConfigI18n((SystemConfig) result);
                    }
                    return result;
                }
            }

            return joinPoint.proceed();
        } catch (Exception e) {
            log.warn("System configuration i18n processing failed, returning original result", e);
            return joinPoint.proceed();
        }
    }

    /**
     * 处理 SystemConfig 的国际化
     */
    private SystemConfig processSystemConfigI18n(SystemConfig systemConfig) {
        if (!TrantorContext.i18nEnabled()) {
            return systemConfig;
        }
        if (systemConfig == null) {
            return null;
        }

        if (systemConfig.getTopConfigs() != null) {
            for (SystemConfig.TopConfig topConfig : systemConfig.getTopConfigs()) {
                if (topConfig.getName() != null) {
                    topConfig.setName(processI18n(topConfig.getName()));
                }
            }
        }
        if (systemConfig.getCommonConfigs() != null) {
            systemConfig.getCommonConfigs().forEach((key, value) -> {
                if (value instanceof ArrayNode) {
                    ArrayNode arrayNode = (ArrayNode) value;
                    arrayNode.forEach(node -> {
                        if (node.has(NAME)) {
                            String name = node.get(NAME).asText();
                            if (name != null && !name.trim().isEmpty()) {
                                ((ObjectNode) node).put(NAME, processI18n(name));
                            }
                        }
                    });
                } else if (value instanceof ObjectNode) {
                    ObjectNode objectNode = (ObjectNode) value;
                    if (objectNode.has(NAME)) {
                        String name = objectNode.get(NAME).asText();
                        if (name != null && !name.trim().isEmpty()) {
                            objectNode.put(NAME, processI18n(name));
                        }
                    }
                }
            });
        }
        return systemConfig;
    }

    private String processI18n(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }
        List<String> keysToFind = List.of(text, PORTAL_PREFIX + text);
        Map<String, String> i18nResources = i18nTarget.findI18nResources(keysToFind);

        return i18nResources.getOrDefault(PORTAL_PREFIX + text,
                i18nResources.getOrDefault(text, text));
    }
} 