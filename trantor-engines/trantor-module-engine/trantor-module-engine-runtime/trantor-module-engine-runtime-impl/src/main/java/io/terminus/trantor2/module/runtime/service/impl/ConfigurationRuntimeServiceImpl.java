package io.terminus.trantor2.module.runtime.service.impl;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.dto.PortalConfigDTO;
import io.terminus.trantor2.module.entity.EnvironmentConfigurationEntity;
import io.terminus.trantor2.module.entity.Position;
import io.terminus.trantor2.module.environment.EnvConfigHandler;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.EnvConfig;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.repository.ConfigurationRepository;
import io.terminus.trantor2.module.runtime.service.ConfigurationRuntimeService;
import io.terminus.trantor2.module.service.AbstractConfigurationService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.module.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@Primary
public class ConfigurationRuntimeServiceImpl extends AbstractConfigurationService implements ConfigurationRuntimeService {

    private final LoadingCache<ConfigCacheKey, ? extends EnvConfig> configCache;
    private final TeamService teamService;

    public ConfigurationRuntimeServiceImpl(List<EnvConfigHandler<?>> handlers, ConfigurationRepository repository, TeamService teamService) {
        super(handlers, repository);
        this.teamService = teamService;
        this.configCache = Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                .build(key -> super.query(key.getTeamId(), key.getKey(), key.getType()));
    }

    public List<Position> findPositionByDomain(String domain) {
        return repository.findAllByType(ConfigType.Module_IAM).stream()
            .filter(config -> {
                ModuleIamConfig iamConfig = JsonUtil.fromJson(config.getConfig(), ModuleIamConfig.class);
                String loginCallbackUrl = iamConfig.getLoginCallbackUrl();
                String portalUrl = ObjectUtils.cleanDomain(loginCallbackUrl);
                return Objects.equals(portalUrl, domain);
            }).map(EnvironmentConfigurationEntity::getPosition).collect(Collectors.toList());
    }


    @Override
    public List<PortalConfigDTO> queryAllPortalConfig() {
        return repository.findAllByType(ConfigType.Module_IAM).stream().map(item -> {
            PortalConfigDTO config = new PortalConfigDTO();
            config.setTeamId(item.getTeamId());
            config.setTeamCode(teamService.getTeamCode(item.getTeamId()));
            config.setPortalCode(item.getMetaKey());
            ModuleIamConfig iamConfig = JsonUtil.fromJson(item.getConfig(), ModuleIamConfig.class);
            config.setIamEndpointId(iamConfig.getIamEndPointId());
            config.setLoginUrl(iamConfig.getLoginUrl());
            config.setLoginCallbackUrl(iamConfig.getLoginCallbackUrl());
            config.setAccessKey(iamConfig.getAccessKey());
            config.setAccessSecret(iamConfig.getAccessSecret());
            config.setApplicationKey(iamConfig.getApplicationKey());
            return config;
        }).collect(Collectors.toList());
    }

    @Override
    public <T extends EnvConfig> T query(Long teamId, String metaKey, ConfigType type) {
        return (T) configCache.get(new ConfigCacheKey(teamId, metaKey, type));
    }

    @Data
    @EqualsAndHashCode
    @AllArgsConstructor
    static class ConfigCacheKey {
        /**
         * 所属团队
         */
        private Long teamId;
        /**
         * 元数据标识
         */
        private String key;

        /**
         * 类型
         */
        private ConfigType type;
    }
}
