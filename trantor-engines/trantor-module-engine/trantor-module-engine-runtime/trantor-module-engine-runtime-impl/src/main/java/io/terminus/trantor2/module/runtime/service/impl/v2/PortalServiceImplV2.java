package io.terminus.trantor2.module.runtime.service.impl.v2;

import com.google.common.collect.Lists;
import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.module.dto.ApplicationMenuDTO;
import io.terminus.trantor2.module.dto.PortalWorkspaceDTO;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.oss.TrantorOssConfig;
import io.terminus.trantor2.module.runtime.service.ConfigurationRuntimeService;
import io.terminus.trantor2.module.runtime.service.v2.PortalServiceV2;
import io.terminus.trantor2.module.service.MenuRuntimeQueryService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.DeepCopier;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.module.util.OSSUtil;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.runtime.api.service.IPermissionRuntimeService;
import io.terminus.trantor2.permission.runtime.api.service.evaluate.UserPermissionEvaluateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class PortalServiceImplV2 implements PortalServiceV2 {

    private final IPermissionRuntimeService permissionService;
    private final MenuRuntimeQueryService menuService;
    private final ConfigurationRuntimeService configurationService;
    private final OSSService ossService;
    private final TrantorOssConfig ossConfig;
    private final UserPermissionEvaluateService userPermissionEvaluateService;
    private final PermissionConfigService permissionConfigService;

    @Override
    public PortalWorkspaceDTO currentPortalWorkspace() {
        PortalWorkspaceDTO result = new PortalWorkspaceDTO();

        Portal currentPortal = TrantorContext.getCurrentPortal();

        result.setId(currentPortal.getId());
        result.setName(currentPortal.getName());
        result.setCode(currentPortal.getCode());

        result.setSystemConfig(configurationService.query(currentPortal.getTeamId(), currentPortal.getCode(), ConfigType.System_Config));

        Long userId = Objects.requireNonNull(TrantorContext.getCurrentUser()).getId();
        // 内部使用了缓存，这里需要执行深拷贝，防止后续修改操作影响原始缓存对象
        List<MenuMeta> menuTree = menuService.getMenuTree(currentPortal.getCode()).stream()
                .map(DeepCopier.INSTANCE::deepCopy).collect(Collectors.toList());
        ApplicationMenuDTO app = new ApplicationMenuDTO();
        app.setWorkbenches(MenuUtils.getWorkbenchesFromMenus(menuTree));
        // 检测是否开启功能权限鉴权
        if (permissionService.needExecFunctionPermission()) {
            filterAuthorizedAppMenu(userId, currentPortal.getCode(), currentPortal.getIamEndpointId(), menuTree);
        }
        app.setMenus(menuTree);
        app.setAppId(currentPortal.getId());
        app.setAppName(currentPortal.getName());
        app.setAppCode(currentPortal.getCode());
        result.setApplications(Lists.newArrayList(app));
        result.setOssEndpoint(ossService.getEndpoint(false));
        List<String> privateEndpoint = new ArrayList<>();
        privateEndpoint.add(ossService.getEndpoint(true));
        if (CollectionUtils.isNotEmpty(ossConfig.getExtend())) {
            ossConfig.getExtendBucket().forEach((key, val) -> {
                if (val.isPrivateRead()) {
                    String bucketEndpoint = OSSUtil.getBucketEndpoint(val.getEndpoint(), key, ossConfig.getFileStoreType());
                    privateEndpoint.add(bucketEndpoint);
                }
            });
        }
        result.setOssPrivateEndpoints(privateEndpoint);
        result.setOssPrivateEnable(ossConfig.isPrivateRead());
        return result;
    }

    /**
     * 根据已授权功能权限过滤出指定门户下用户可访问的菜单资源
     *
     * @param userId     用户id
     * @param portalCode 门户对象id
     */
    private void filterAuthorizedAppMenu(@NotNull Long userId, @NotNull String portalCode, @NotNull Long iamEndpointId, List<MenuMeta> menuTree) {
        // 1. 获取所有菜单permissionKey并去重
        List<MenuMeta> flatMenuMetaList = new ArrayList<>();
        MenuUtils.flatMenuMetaListRecursively(menuTree, flatMenuMetaList);
        Set<String> allPermissionKeys = flatMenuMetaList.stream()
                .map(MenuMeta::getPermissionKey).filter(Objects::nonNull).collect(Collectors.toSet());
        // 2. 获取用户的菜单权限授权状态
        Map<String, AuthorizationEffect> userEndpointPermissions =
                userPermissionEvaluateService.findUserFunctionPermissionEffect(userId, portalCode, new ArrayList<>(allPermissionKeys));
        PolicyEnforcementMode enforcementMode = permissionConfigService.getFunctionPermissionPolicyEnforcementMode(iamEndpointId);
        // 3. 根据授权状态过滤菜单
        filterMenuResourceByPermission(menuTree, userEndpointPermissions, enforcementMode);
    }

    private void filterMenuResourceByPermission(Collection<MenuMeta> menuTree,
                                                Map<String, AuthorizationEffect> userEndpointPermissions,
                                                PolicyEnforcementMode enforcementMode) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(menuTree)) {
            return;
        }
        Iterator<MenuMeta> iterator = menuTree.iterator();
        while (iterator.hasNext()) {
            MenuMeta menu = iterator.next();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(menu.getChildren())) {
                filterMenuResourceByPermission(menu.getChildren(), userEndpointPermissions, enforcementMode);
            }
            //未授权的菜单需要移除
            if (!isGrantedMenu(menu, userEndpointPermissions, enforcementMode)) {
                iterator.remove();
            }
        }
    }

    private boolean isGrantedMenu(MenuMeta menu,
                                  Map<String, AuthorizationEffect> userEndpointPermissions,
                                  PolicyEnforcementMode enforcementMode) {
        // 文件夹菜单判断是否存在可访问的下级子菜单，不存在则不透出
        if (MenuMeta.RouteType.None.equals(menu.getRouteType())) {
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(menu.getChildren());
        }
        // 非文件夹类型的菜单
        if (Objects.isNull(menu.getPermissionKey())) {
            // 没有权限项 严格模式 返回false 宽松返回true
            return !PolicyEnforcementMode.ENFORCING.equals(enforcementMode);
        }
        return AuthorizationEffect.ALLOW.equals(userEndpointPermissions.get(menu.getPermissionKey()));
    }
}
