package io.terminus.trantor2.module.runtime.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.internal.Internal;
import io.terminus.trantor2.module.runtime.entity.FilterPreferenceData;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import io.terminus.trantor2.module.runtime.request.*;
import io.terminus.trantor2.module.runtime.service.impl.AbstractPreferenceService;
import io.terminus.trantor2.module.runtime.service.impl.FilterPreferenceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Internal
@RestController
@RequiredArgsConstructor
@Tag(name = "用户个性化配置")
@RequestMapping(path = "/api/trantor2default/preference")
public class PreferenceController {
    public static final String DEPRECATED_API_PREFIX = "/api/trantor2/runtime/preference";

    @Autowired
    private List<AbstractPreferenceService<?>> serviceList;

    private Map<Preference.PreferenceType, AbstractPreferenceService<?>> serviceMap;

    @PostConstruct
    public void init() {
        serviceMap = serviceList.stream().collect(Collectors.toMap(
                AbstractPreferenceService::getType,
                Function.identity())
        );
    }

    @Operation(summary = "保存当前用户个性化配置")
    @PostMapping("/{type}/save")
    public <R extends PreferenceSaveRequest> Response<String> save(@PathVariable Preference.PreferenceType type,
                                                                   @Valid @RequestBody R request) {
        AbstractPreferenceService<?> service = serviceMap.get(type);
        service.savePreference(request);
        return Response.ok();
    }

    @Operation(summary = "删除用户个性化配置")
    @PostMapping("/{type}/delete")
    public <R extends PreferenceDeleteRequest> Response<Void> delete(@PathVariable Preference.PreferenceType type,
                                                                     @Valid @RequestBody R request) {
        AbstractPreferenceService<?> service = serviceMap.get(type);
        service.delete(request);
        return Response.ok();
    }

    @Operation(summary = "获取用户个性化配置")
    @GetMapping("/{type}")
    public <R extends PreferenceQueryRequest> Response<PreferenceData> query(@PathVariable Preference.PreferenceType type,
                                                                             @Valid R request) {
        AbstractPreferenceService<?> service = serviceMap.get(type);
        return Response.ok(service.query(request));
    }

    @Operation(summary = "获取用户个性化配置")
    @PostMapping("/{type}")
    public <R extends PreferenceQueryRequest> Response<PreferenceData> queryWithRequestBody(@PathVariable Preference.PreferenceType type,
                                                                                            @Valid @RequestBody R request) {
        AbstractPreferenceService<?> service = serviceMap.get(type);
        return Response.ok(service.query(request));
    }

    @Internal
    @RestController
    @RequiredArgsConstructor
    @Tag(name = "筛选器偏好", description = "已废弃，使用 /api/trantor2default/preference/{type} 替代")
    @RequestMapping(path = DEPRECATED_API_PREFIX + "/filter")
    @Deprecated()
    private static class FilterPreferenceController {
        private final FilterPreferenceService service;

        @Operation(
            summary = "保存当前用户筛选器偏好",
            description = "scope 需门户下唯一，否则会覆盖，同 scope 下 code 唯一，否则会覆盖"
        )
        @PostMapping("/save")
        public Response<Void> save(@Valid @RequestBody FilterPreferenceSaveRequest request) {
            service.savePreference(request);
            return Response.ok();
        }

        @Operation(summary = "删除用户筛选器")
        @PostMapping("/delete")
        public Response<Void> delete(@Valid @RequestBody FilterPreferenceDeleteRequest request) {
            service.delete(request);
            return Response.ok();
        }

        @Operation(summary = "获取当前筛选器的用户自定义筛选器")
        @GetMapping("/{scope}")
        public Response<List<FilterPreferenceData.Filter>> findByScope(@PathVariable String scope) {
            return Response.ok(service.getCurUserPreferenceInScope(scope));
        }
    }
}
