package io.terminus.trantor2.module.runtime.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.iam.mapper.TrantorUserConverter;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.common.user.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * 2022/9/7 2:18 下午
 */
@Tag(name = "portal用户接口")
@Slf4j
@RestController
@RequestMapping("/api/trantor/portal/user")
@RequiredArgsConstructor
public class PortalUserController {

    private final TrantorIAMUserService trantorIAMUserService;
    private final TrantorUserConverter converter;

    @GetMapping("/current")
    @Operation(summary = "获取当前登陆用户")
    public Response<User> currentUser() {
        UserVO user = trantorIAMUserService.findPlaintextByIdVO(TrantorContext.getCurrentUserId());
        return Response.ok(converter.convert(user));
    }

    @GetMapping("/login")
    @Operation(summary = "登陆")
    public void login(@RequestParam(required = false) String redirectUrl, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 需求：portal的地址需支持url path的方式，原[author: yyq] 的代码暂时先注释掉
//        String loginUrl = portalService.currentPortalLoginUrl();
//
//        String params =
//                redirectUrl == null ? "/login" : "/login?redirectUrl="
//                        + URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name());
//        response.sendRedirect(loginUrl + params);

        Portal portal = TrantorContext.getCurrentPortal();
        String path = redirectUrl == null ? "/login" : "/login?redirectUrl=" + URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name());
        response.sendRedirect(portal.getLoginUrl() + path);
    }

    @GetMapping("/logout")
    @Operation(summary = "登出")
    public void logout(@RequestParam(required = false) String redirectUrl, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 需求：portal的地址需支持url path的方式，原[author: yyq] 的代码暂时先注释掉
//        String loginUrl = portalService.currentPortalLoginUrl();
//
//        String params =
//            redirectUrl == null ? "/logout" : "/logout?redirectUrl="
//                + URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name());
//        response.sendRedirect(loginUrl + params);

        Portal portal = TrantorContext.getCurrentPortal();
        String path = redirectUrl == null ? "/logout" : "/logout?redirectUrl=" + URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name());
        trantorIAMUserService.userPlaintextByIdCacheEvict(TrantorContext.getCurrentUserId());
        response.sendRedirect(portal.getLoginUrl() + path);
    }

    @GetMapping("/account")
    @Operation(summary = "用户中心")
    public void account(HttpServletResponse response, HttpServletRequest request) throws IOException {
        String path = "/account";
        Portal portal = TrantorContext.getCurrentPortal();
        String loginUrl = portal.getLoginUrl();
        if (loginUrl.endsWith("/")) {
            loginUrl = loginUrl.substring(0, loginUrl.lastIndexOf("/"));
        }
        String url = ((!loginUrl.startsWith("https://") && !loginUrl.startsWith("http://")) ? request.getScheme() + "://" : "") + portal.getLoginUrl() + path;
        response.sendRedirect(url);
    }
}
