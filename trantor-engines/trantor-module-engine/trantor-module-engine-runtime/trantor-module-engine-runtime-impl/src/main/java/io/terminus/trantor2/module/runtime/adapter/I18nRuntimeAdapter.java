package io.terminus.trantor2.module.runtime.adapter;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.module.runtime.cache.PortalI18nCache;
import io.terminus.trantor2.module.runtime.cache.PortalI18nCaffeine;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import io.terminus.trantor2.module.util.I18nProperties;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 目前从 oss 加载国际化资源
 *
 * <AUTHOR>
 */
@Slf4j
@Primary
@Component
@RequiredArgsConstructor
public class I18nRuntimeAdapter implements I18nRuntimeTarget {
    private final PortalI18nCache portalI18nCache;

    @Override
    @Nonnull
    public I18nProperties loadI18nProperties(@NotNull Portal portal, @NotNull String lang) {
        if (!TrantorContext.i18nEnabled()) {
            return new I18nProperties();
        }
        return portalI18nCache.getI18nPropertiesFromOSS(portal, lang);
    }

    @Override
    @Nonnull
    public Map<String, String> findI18nResources(@NotNull Portal portal, @NotNull String lang,
            Collection<String> keys) {
        if (keys.isEmpty()) {
            return new HashMap<>(2);
        }
        Map<String, String> result = new HashMap<>(keys.size());
        if (!TrantorContext.i18nEnabled()) {
            return result;
        }
        if (shouldCache(keys) && portalI18nCache instanceof PortalI18nCaffeine) {
            PortalI18nCaffeine caffeine = (PortalI18nCaffeine) portalI18nCache;

            List<String> uncachedKeys = new ArrayList<>();

            for (String key : keys) {
                String cachedValue = caffeine.get(portal.getCode(), lang, key);
                if (cachedValue != null) {
                    result.put(key, cachedValue);
                } else {
                    uncachedKeys.add(key);
                }
            }
            if (!uncachedKeys.isEmpty()) {
                Map<String, String> uncachedResult = findI18nResourcesInternal(portal, lang, uncachedKeys);
                for (String key : uncachedKeys) {
                    String value = uncachedResult.get(key);
                    if (value != null) {
                        result.put(key, value);
                        caffeine.put(portal.getCode(), lang, key, value);
                    }
                }
            }
            return result;
        }
        return findI18nResourcesInternal(portal, lang, keys);
    }

    @Override
    @Nonnull
    public Map<String, String> findI18nResourcesByRegex(@NotNull Portal portal, @NotNull String lang,
            @NotNull String regex) {
        Properties properties = loadI18nProperties(portal, lang);
        Pattern pattern = Pattern.compile(regex);
        return properties.entrySet().parallelStream()
                .filter(entry -> !ObjectUtils.isEmpty(entry.getValue())
                        && pattern.matcher((String) entry.getKey()).matches())
                .collect(Collectors.toMap(entry -> (String) entry.getKey(), entry -> (String) entry.getValue()));
    }

    @NotNull
    @Override
    public Map<String, String> findI18nResourcesByPrefix(@NotNull Portal portal, @NotNull String lang,
            @NotNull String prefix) {
        Properties properties = loadI18nProperties(portal, lang);
        return properties.entrySet().parallelStream()
                .filter(entry -> entry.getKey().toString().startsWith(prefix) && !ObjectUtils.isEmpty(entry.getValue()))
                .collect(Collectors.toMap(entry -> (String) entry.getKey(), entry -> (String) entry.getValue()));
    }

    /**
     * 判断是否应该缓存
     * 当keys数量为1或2时进行缓存
     */
    private boolean shouldCache(Collection<String> keys) {
        return keys != null && (keys.size() == 1 || keys.size() == 2);
    }

    /**
     * 内部方法：执行实际的国际化资源查询
     */
    private Map<String, String> findI18nResourcesInternal(@NotNull Portal portal, @NotNull String lang,
            Collection<String> keys) {
        Properties properties = loadI18nProperties(portal, lang);
        Set<String> trimmedKeys = keys.stream().map(String::trim).collect(Collectors.toSet());
        if (properties.size() > 5000) {
            Map<String, String> res = new HashMap<>();
            for (String key : trimmedKeys) {
                String property = properties.getProperty(key);
                if (StringUtils.isNotBlank(property)) {
                    res.put(key, property);
                }
            }
            return res;
        } else {
            return properties.entrySet().stream()
                    .filter(entry -> trimmedKeys.contains(entry.getKey()) && !ObjectUtils.isEmpty(entry.getValue()))
                    .collect(Collectors.toMap(entry -> (String) entry.getKey(), entry -> (String) entry.getValue()));
        }
    }
}
