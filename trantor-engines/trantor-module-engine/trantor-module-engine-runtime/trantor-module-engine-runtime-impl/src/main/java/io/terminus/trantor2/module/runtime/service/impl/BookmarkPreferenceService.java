package io.terminus.trantor2.module.runtime.service.impl;

import io.terminus.trantor2.common.exception.ValidationException;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.meta.api.cache.MetaCache;
import io.terminus.trantor2.meta.api.model.MetaTreeNode;
import io.terminus.trantor2.module.runtime.entity.*;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import io.terminus.trantor2.module.runtime.repo.BookmarkPreferenceRepo;
import io.terminus.trantor2.module.runtime.repo.FilterPreferenceRepo;
import io.terminus.trantor2.module.runtime.request.*;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@InternalComponent
public class BookmarkPreferenceService extends AbstractPreferenceService<BookmarkPreference> {
    private final BookmarkPreferenceRepo repo;
    private final FilterPreferenceRepo filterPreferenceRepo;
    private final MetaCache metaCache;


    @Override
    public Preference.PreferenceType getType() {
        return Preference.PreferenceType.BOOKMARK;
    }

    @Override
    public AbstractPreferenceRepo<BookmarkPreference> getPreferenceRepo() {
        return repo;
    }

    @Override
    protected <R extends PreferenceSaveRequest> BookmarkPreference save(R req) {
        if (!(req instanceof BookmarkPreferenceSaveRequest)) {
            throw new ValidationException("bad request");
        }
        BookmarkPreference userBookmark = repo.findByCurUser();
        BookmarkPreferenceSaveRequest request = (BookmarkPreferenceSaveRequest) req;
        boolean create = request.getBookmarkCode() == null;
        return create ? createBookmark(userBookmark, request) : editBookmark(userBookmark, request);
    }

    @Override
    @Transactional
    public <R extends PreferenceDeleteRequest> void delete(R req) {
        if (!(req instanceof BookmarkPreferenceDeleteRequest)) {
            throw new ValidationException("bad request");
        }
        BookmarkPreferenceDeleteRequest request = (BookmarkPreferenceDeleteRequest) req;
        String bookmarkCode = request.getBookmarkCode();
        AbstractPreferenceRepo<BookmarkPreference> repo = getPreferenceRepo();
        BookmarkPreference userBookmark = repo.findByCurUser();
        if (userBookmark == null || userBookmark.getData() == null) {
            return;
        }
        List<BookmarkPreferenceData.Bookmark> bookmarks = userBookmark.getData().getBookmarks();
        bookmarks.removeIf(bookmark -> bookmark.getCode().equals(bookmarkCode));
        if (!CollectionUtils.isEmpty(bookmarks)) {
            repo.saveAndFlush(userBookmark);
        } else {
            repo.delete(userBookmark);
        }
        invalidateData(userBookmark);
    }

    @Override
    public <R extends PreferenceQueryRequest> BookmarkPreferenceData query(@Nonnull R request) {
        BookmarkPreferenceData data = getData(request.getScope());
        if (data == null) {
            return null;
        }
        if (request instanceof BookmarkPreferenceQueryRequest) {
            List<BookmarkPreferenceData.Bookmark> bookmarks = data.getBookmarks();
            if (CollectionUtils.isEmpty(bookmarks)) {
                return data;
            }
            BookmarkPreferenceQueryRequest queryRequest = (BookmarkPreferenceQueryRequest) request;

            bookmarks = filterByTypes(bookmarks, queryRequest.getTypes());
            bookmarks = filterByReference(bookmarks, queryRequest.getRef());
            bookmarks = applyLimit(bookmarks, queryRequest.getLimit());

            data = new BookmarkPreferenceData();
            data.setBookmarks(bookmarks);
        } else {
            // bookmark center should be verified
            checkValidBookmarks(data.getBookmarks());
        }
        return data;
    }


    private List<BookmarkPreferenceData.Bookmark> filterByViewReference(@Nonnull List<BookmarkPreferenceData.Bookmark> bookmarks,
                                                                        @Nonnull BookmarkPreferenceData.ViewReference viewReference) {
        return bookmarks.stream()
                .filter(bookmark -> bookmark.getRef() instanceof BookmarkPreferenceData.ViewReference)
                .filter(bookmark -> {
                    BookmarkPreferenceData.ViewReference bookmarkRef = (BookmarkPreferenceData.ViewReference) bookmark.getRef();
                    return (viewReference.getSceneKey() == null || viewReference.getSceneKey().equals(bookmarkRef.getSceneKey())) &&
                            (viewReference.getViewKey() == null || viewReference.getViewKey().equals(bookmarkRef.getViewKey()));
                })
                .collect(Collectors.toList());
    }

    private BookmarkPreference createBookmark(@Nullable BookmarkPreference userBookmark, @Nonnull BookmarkPreferenceSaveRequest request) {
        userBookmark = userBookmark != null ? userBookmark : newPreference(BookmarkPreference.class);
        BookmarkPreferenceData bookmarkData = userBookmark.getData() != null ? userBookmark.getData() : new BookmarkPreferenceData();

        if (CollectionUtils.isEmpty(bookmarkData.getBookmarks())) {
            bookmarkData.setBookmarks(new ArrayList<>(2));
        }
        bookmarkData.getBookmarks().add(request.toBookmark());
        userBookmark.setData(bookmarkData);
        return repo.saveAndFlush(userBookmark);
    }

    private BookmarkPreference editBookmark(BookmarkPreference userBookmark, BookmarkPreferenceSaveRequest request) {
        if (userBookmark == null || userBookmark.getData() == null) {
            throw new ValidationException("preference not found");
        }
        BookmarkPreferenceData bookmarkData = userBookmark.getData();
        List<BookmarkPreferenceData.Bookmark> bookmarks = bookmarkData.getBookmarks();

        BookmarkPreferenceData.Bookmark bookmark = findBookmarkByCode(bookmarks, request.getBookmarkCode());
        updateBookmarkDetails(bookmark, request);
        moveBookmark(bookmarks, bookmark, request.getIndex());
        return repo.saveAndFlush(userBookmark);
    }


    private BookmarkPreferenceData.Bookmark findBookmarkByCode(List<BookmarkPreferenceData.Bookmark> bookmarks, String bookmarkCode) {
        return bookmarks.stream()
                .filter(bookmark -> bookmark.getCode().equals(bookmarkCode))
                .findFirst()
                .orElseThrow(() -> new ValidationException("bookmark not found by code " + bookmarkCode));
    }

    private void updateBookmarkDetails(BookmarkPreferenceData.Bookmark bookmark, BookmarkPreferenceSaveRequest request) {
        bookmark.setName(request.getName());
        bookmark.setFrontendConfig(request.getFrontendConfig());
        bookmark.setIcon(request.getIcon());
    }

    private void moveBookmark(List<BookmarkPreferenceData.Bookmark> bookmarks, BookmarkPreferenceData.Bookmark bookmark, Integer newIndex) {
        if (newIndex == null) {
            return;
        }
        int oldIndex = bookmarks.indexOf(bookmark);
        if (oldIndex == -1) {
            return;
        }
        if (newIndex < 0 || newIndex >= bookmarks.size()) {
            throw new ValidationException("bad index " + newIndex);
        } else if (oldIndex != newIndex) {
            bookmarks.remove(oldIndex);
            bookmarks.add(newIndex, bookmark);
        }
    }

    /**
     * 检查书签引用的资源是否存在，不存在的话将书签标记为 invalid
     */
    private void checkValidBookmarks(List<BookmarkPreferenceData.Bookmark> bookmarks) {
        if (CollectionUtils.isEmpty(bookmarks)) {
            return;
        }
        List<BookmarkPreferenceData.Bookmark> refBookmarks = bookmarks.stream().filter(it -> !it.getType().equals(BookmarkPreferenceData.BookmarkType.URL)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refBookmarks)) {
            return;
        }
        Set<String> viewKeySet = refBookmarks.stream()
                .map(it -> ((BookmarkPreferenceData.ViewReference) it.getRef()).getViewKey()).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(viewKeySet)) {
            Set<String> existViewKeySet = metaCache.getAll(viewKeySet).stream().map(MetaTreeNode::getKey).collect(Collectors.toSet());
            refBookmarks.forEach(it -> it.setInvalid(!existViewKeySet.contains(((BookmarkPreferenceData.ViewReference) it.getRef()).getViewKey())));
        }
        if (bookmarks.stream().anyMatch(it -> it.getType().equals(BookmarkPreferenceData.BookmarkType.FILTER))) {
            List<FilterPreference> allByCurUser = filterPreferenceRepo.findAllByCurUser();
            if (CollectionUtils.isEmpty(allByCurUser)) {
                return;
            }
            Map<String, Set<String>> scope2FilterCodes = allByCurUser.stream().collect(Collectors.toMap(FilterPreference::getScope,
                    filterPreference -> filterPreference.getData() != null ?
                            filterPreference.getData().getFilters().stream()
                                    .map(FilterPreferenceData.Filter::getCode)
                                    .collect(Collectors.toSet()) :
                            new HashSet<>()));

            bookmarks.stream().filter(it -> !it.getInvalid() && it.getType().equals(BookmarkPreferenceData.BookmarkType.FILTER)).forEach(it -> {
                String scope = ((BookmarkPreferenceData.FilterReference) it.getRef()).getFilterScope();
                String code = ((BookmarkPreferenceData.FilterReference) it.getRef()).getFilterCode();
                if (!scope2FilterCodes.containsKey(scope) || !scope2FilterCodes.get(scope).contains(code)) {
                    it.setInvalid(true);
                }
            });
        }
    }

    private List<BookmarkPreferenceData.Bookmark> filterByTypes(@Nonnull List<BookmarkPreferenceData.Bookmark> bookmarks,
                                                                @Nullable Set<BookmarkPreferenceData.BookmarkType> types) {
        if (!CollectionUtils.isEmpty(types)) {
            return bookmarks.stream()
                    .filter(bookmark -> types.contains(bookmark.getType()))
                    .collect(Collectors.toList());
        }
        return bookmarks;
    }

    private List<BookmarkPreferenceData.Bookmark> filterByReference(@Nonnull List<BookmarkPreferenceData.Bookmark> bookmarks,
                                                                    @Nullable BookmarkPreferenceData.Reference ref) {
        if (ref instanceof BookmarkPreferenceData.ViewReference) {
            bookmarks = filterByViewReference(bookmarks, (BookmarkPreferenceData.ViewReference) ref);
        }
        if (ref instanceof BookmarkPreferenceData.FilterReference) {
            bookmarks = filterByFilterReference(bookmarks, (BookmarkPreferenceData.FilterReference) ref);
        }
        return bookmarks;
    }

    private List<BookmarkPreferenceData.Bookmark> filterByFilterReference(@Nonnull List<BookmarkPreferenceData.Bookmark> bookmarks,
                                                                          @Nonnull BookmarkPreferenceData.FilterReference filterReference) {
        return bookmarks.stream()
                .filter(bookmark -> bookmark.getRef() instanceof BookmarkPreferenceData.FilterReference)
                .filter(bookmark -> {
                    BookmarkPreferenceData.FilterReference bookmarkRef = (BookmarkPreferenceData.FilterReference) bookmark.getRef();
                    return (filterReference.getFilterScope() == null || filterReference.getFilterScope().equals(bookmarkRef.getFilterScope())) &&
                            (filterReference.getFilterCode() == null || filterReference.getFilterCode().equals(bookmarkRef.getFilterCode()));
                })
                .collect(Collectors.toList());
    }

    private List<BookmarkPreferenceData.Bookmark> applyLimit(List<BookmarkPreferenceData.Bookmark> bookmarks, Integer limit) {
        if (limit != null) {
            return bookmarks.stream().limit(limit).collect(Collectors.toList());
        }
        return bookmarks;
    }
}
