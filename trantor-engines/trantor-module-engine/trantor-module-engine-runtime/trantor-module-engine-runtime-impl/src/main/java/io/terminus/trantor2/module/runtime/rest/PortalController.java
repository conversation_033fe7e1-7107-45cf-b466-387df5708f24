package io.terminus.trantor2.module.runtime.rest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.module.dto.PortalDTO;
import io.terminus.trantor2.module.dto.PortalWorkspaceDTO;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.FeatureGuideConfigData;
import io.terminus.trantor2.module.meta.PortalAnnouncementConfigData;
import io.terminus.trantor2.module.model.dto.ModuleDTO;
import io.terminus.trantor2.module.oss.TrantorOssConfig;
import io.terminus.trantor2.module.runtime.entity.IconCollector;
import io.terminus.trantor2.module.runtime.service.PortalService;
import io.terminus.trantor2.module.runtime.service.v2.PortalServiceV2;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.FeatureGuideConfigService;
import io.terminus.trantor2.module.service.PortalAnnouncementConfigService;
import io.terminus.trantor2.module.service.ModuleRuntimeQueryService;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.trantor2.module.util.OSSUtil;
import io.terminus.trantor2.service.report.annotation.TrantorServiceRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2022/9/21 3:36 下午
 */
@TrantorServiceRegistry
@Slf4j
@Tag(name = "portal接口")
@RestController
@RequestMapping(path = "/api/trantor/portal")
public class PortalController {
    @Autowired
    private PortalService portalService;
    @Autowired
    private PortalServiceV2 portalServiceV2;
    @Autowired
    private ModuleRuntimeQueryService moduleService;
    @Autowired
    private OSSService ossService;
    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private TrantorOssConfig ossConfig;

    @Autowired
    private FeatureGuideConfigService featureGuideConfigService;

    @Autowired
    private PortalAnnouncementConfigService portalAnnouncementConfigService;


    @GetMapping("/application/list")
    @Operation(summary = "获取工作台应用")
    public Response<PortalWorkspaceDTO> list() {
        return Response.ok(portalServiceV2.currentPortalWorkspace());
    }

    @GetMapping("/current")
    @Operation(summary = "获取当前门户信息")
    public Response<PortalDTO> currentPortal() {
        Portal currentPortal = TrantorContext.getCurrentPortal();
        PortalDTO dto = PortalDTO.copy(currentPortal);
        dto.setSystemConfig(configurationService.query(currentPortal.getTeamId(), currentPortal.getCode(), ConfigType.System_Config));
        dto.setDebugConfig(configurationService.query(currentPortal.getTeamId(), currentPortal.getCode(), ConfigType.Debug_Config));
        dto.setOssEndpoint(ossService.getEndpoint(false));
        dto.setHasFeatureGuide(featureGuideConfigService.check(currentPortal.getTeamId(), currentPortal.getCode()));
        dto.setHasPortalAnnouncement(portalAnnouncementConfigService.check(currentPortal.getTeamId(), currentPortal.getCode()));
        List<String> privateEndpoint = new ArrayList<>();
        privateEndpoint.add(ossService.getEndpoint(true));
        if (CollectionUtils.isNotEmpty(ossConfig.getExtend())) {
            ossConfig.getExtendBucket().forEach((key, val) -> {
                if (val.isPrivateRead()) {
                    String bucketEndpoint = OSSUtil.getBucketEndpoint(val.getEndpoint(), key, ossConfig.getFileStoreType());
                    privateEndpoint.add(bucketEndpoint);
                }
            });
        }
        dto.setOssPrivateEndpoints(privateEndpoint);
        dto.setOssPrivateEnable(ossConfig.isPrivateRead());
        return Response.ok(dto);
    }

    @GetMapping("/relation")
    @Operation(summary = "获取门户关联")
    public Response<ModuleDTO> currentPortalRelation() {
        return Response.ok(moduleService.queryPortalRelation(TrantorContext.getCurrentPortal().getCode()));
    }

    @GetMapping("/icon")
    @Operation(summary = "查询图标")
    public Response<IconCollector> queryAllIcon(@RequestParam(required = false) String packageKey) {
        Portal portal = TrantorContext.getCurrentPortal();
        return Response.ok(portalService.queryAllIcon(portal.getTeamId(), portal.getCode(), packageKey));
    }

    // 获取功能引导配置
    @GetMapping("/feature-guide")
    @Operation(summary = "查询功能引导配置")
    public Response<List<FeatureGuideConfigData>> queryFeatureGuideConfig() {
        Portal portal = TrantorContext.getCurrentPortal();
        return Response.ok(featureGuideConfigService.queryEnabled(portal.getTeamId(), portal.getCode()));
    }

    // 获取门户公告配置
    @GetMapping("/portal-announcement")
    @Operation(summary = "查询门户公告配置")
    public Response<List<PortalAnnouncementConfigData>> queryPortalAnnouncementConfig() {
        Portal portal = TrantorContext.getCurrentPortal();
        return Response.ok(portalAnnouncementConfigService.queryEnabled(portal.getTeamId(), portal.getCode()));
    }


    @GetMapping("/access-url")
    @Operation(summary = "根据场景和视图信息获取访问地址")
    public Response<String> fetchAccessUrl(@RequestParam String sceneKey,
                                           @RequestParam(required = false) String viewKey,
                                           @RequestParam(required = false) String action,
                                           @RequestParam(required = false) String recordId) {
        Portal portal = TrantorContext.getCurrentPortal();
        return Response.ok(portalService.fetchAccessUrl(portal, sceneKey, viewKey, action, recordId));
    }
}
