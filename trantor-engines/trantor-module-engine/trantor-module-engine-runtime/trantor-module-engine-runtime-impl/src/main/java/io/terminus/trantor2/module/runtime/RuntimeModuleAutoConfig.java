package io.terminus.trantor2.module.runtime;

import io.terminus.trantor2.common.internal.OnTrantor2DefaultModeCondition;
import io.terminus.trantor2.module.runtime.cache.CaffeinePreferenceCache;
import io.terminus.trantor2.module.runtime.cache.MultiLevelPreferenceCache;
import io.terminus.trantor2.module.runtime.cache.PreferenceCache;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
public class RuntimeModuleAutoConfig {

    @Bean
    @Conditional(OnTrantor2DefaultModeCondition.class)
    public CaffeinePreferenceCache caffeinePreferenceCache(List<AbstractPreferenceRepo<?>> preferenceRepos) {
        return new CaffeinePreferenceCache(preferenceRepos);
    }

    @Bean
    @Primary
    @ConditionalOnBean(name = {"stringRedisTemplate", "caffeinePreferenceCache"})
    public MultiLevelPreferenceCache preferenceCache(StringRedisTemplate stringRedisTemplate,
                                                     CaffeinePreferenceCache caffeinePreferenceCache) {
        return new MultiLevelPreferenceCache(stringRedisTemplate, caffeinePreferenceCache);
    }
}
