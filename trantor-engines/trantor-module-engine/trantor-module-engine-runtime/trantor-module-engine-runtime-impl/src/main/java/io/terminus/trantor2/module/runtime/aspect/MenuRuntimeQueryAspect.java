package io.terminus.trantor2.module.runtime.aspect;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.MenuTreeMeta;
import io.terminus.trantor2.module.runtime.service.I18nRuntimeTarget;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static io.terminus.trantor2.module.constants.I18nConst.MENU_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class MenuRuntimeQueryAspect {
    private final I18nRuntimeTarget i18nRuntimeTarget;


    @Pointcut("execution(* io.terminus.trantor2.module.service.MenuQueryService+.*(..))")
    public void menuQueryMethods() {
    }

    @AfterReturning(pointcut = "menuQueryMethods()", returning = "result")
    public void enhanceI18nMenu(Object result) {
        try {
            if (!TrantorContext.i18nEnabled()) {
                return;
            }
            if (result instanceof MenuTreeMeta) {
                handleMenuTree((MenuTreeMeta) result);
            } else if (result instanceof Collection) {
                if (!((Collection<?>) result).isEmpty() && ((Collection<?>) result).iterator().next() instanceof MenuMeta) {
                    handleMenuMetas((Collection<MenuMeta>) result);
                }
            }

        } catch (Exception e) {
            log.error("enhanceI18nMenu error", e);
        }
    }

    private void handleMenuTree(MenuTreeMeta treeMeta) {
        handleMenuMetas(treeMeta.getMenus());
    }
    private void handleMenuMetas(Collection<MenuMeta> menuMetas) {
        if (CollectionUtils.isEmpty(menuMetas)) {
            return;
        }
        menuMetas = flattenMenus(menuMetas);
        Set<String> menuI18nKeySet = menuMetas.stream().map(it -> MENU_PREFIX + it.getLabel()).collect(Collectors.toSet());
        Map<String, String> i18nResources = findI18nResourcesByKeys(menuI18nKeySet);
        menuMetas.forEach(menu ->
                menu.setLabel(i18nResources.getOrDefault(MENU_PREFIX + menu.getLabel(), menu.getLabel())));
    }

    private List<MenuMeta> flattenMenus(Collection<MenuMeta> menus) {
        List<MenuMeta> result = new ArrayList<>();
        for (MenuMeta menu : menus) {
            result.addAll(getMenusRecursively(menu));
        }
        return result;
    }

    private List<MenuMeta> getMenusRecursively(MenuMeta menu) {
        List<MenuMeta> menus = new ArrayList<>();
        menus.add(menu);
        List<MenuMeta> children = menu.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return menus;
        }
        children.forEach(child -> menus.addAll(getMenusRecursively(child)));
        return menus;
    }
    

    private Map<String, String> findI18nResourcesByKeys(Collection<String> keys) {
        return i18nRuntimeTarget.findI18nResources(keys);
    }

}
