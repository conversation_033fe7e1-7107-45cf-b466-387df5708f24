package io.terminus.trantor2.module.runtime.event.listener;

import io.terminus.trantor2.module.event.PortalI18nLanguagePackChanged;
import io.terminus.trantor2.module.runtime.cache.PortalI18nCache;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheCleanser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PortalEventListener {
    private final PortalI18nCache portalI18nCache;
    private final PermissionCacheCleanser permissionCacheCleanser;

    @EventListener(value = PortalI18nLanguagePackChanged.class)
    public void onI18nLanguageUpdated(PortalI18nLanguagePackChanged event) {
        log.info("I18nLanguageUpdated event: {}", event);
        portalI18nCache.invalidate(event.getTeamCode(), event.getPortalCode(), event.getOldLanguagePack());
        permissionCacheCleanser.invalidatePortalCache(event.getPortalId());
    }
}
