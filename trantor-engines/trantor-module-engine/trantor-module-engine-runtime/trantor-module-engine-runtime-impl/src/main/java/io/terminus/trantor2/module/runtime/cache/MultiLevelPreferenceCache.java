package io.terminus.trantor2.module.runtime.cache;

import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.runtime.entity.NullPreferenceData;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static java.util.concurrent.CompletableFuture.runAsync;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class MultiLevelPreferenceCache extends AbstractPreferenceCache {
    private final StringRedisTemplate stringRedisTemplate;
    private final CaffeinePreferenceCache caffeineCache;
    private static final int REDIS_CACHE_EXPIRATION_TIME = 2;
    private static final String REDIS_KEY_PREFIX = "PREFERENCE:";
    private static final PreferenceData NULL_PREFERENCE_DATA = new NullPreferenceData();

    @Override
    public <T extends PreferenceData> T get(@Nonnull PreferenceCacheKey cacheKey) {
        T preference = caffeineCache.getIfPresent(cacheKey);
        if (preference != null) {
            return preference;
        }
        String redisKey = getRedisKey(cacheKey);
        Object obj = stringRedisTemplate.opsForValue().get(redisKey);
        if (obj == null) {
            T dbPreference = (T) caffeineCache.load(cacheKey);
            runAsync(() -> put(cacheKey, dbPreference));
            return dbPreference;
        }
        PreferenceData data = JsonUtil.fromJson(obj.toString(), PreferenceData.class);
        if (data instanceof NullPreferenceData) {
            return null;
        } else {
            return (T) data;
        }
    }

    @Override
    public void put(@Nonnull PreferenceCacheKey cacheKey, @Nullable PreferenceData preference) {
        caffeineCache.put(cacheKey, preference);
        if (preference == null) {
            preference = NULL_PREFERENCE_DATA;
        }
        String redisKey = getRedisKey(cacheKey);

        stringRedisTemplate.opsForValue().set(redisKey, JsonUtil.toJson(preference), REDIS_CACHE_EXPIRATION_TIME, TimeUnit.HOURS);
    }

    @Override
    public void invalidate(@Nonnull PreferenceCacheKey cacheKey) {
        caffeineCache.invalidate(cacheKey);
        String redisKey = getRedisKey(cacheKey);
        stringRedisTemplate.delete(redisKey);
    }

    @Override
    public void invalidateAll() {
        caffeineCache.invalidateAll();
        Set<String> keys = stringRedisTemplate.keys(REDIS_KEY_PREFIX + "*");
        if (!CollectionUtils.isEmpty(keys)) {
            stringRedisTemplate.delete(keys);
        }
    }

    @Override
    public long size() {
        long count = 0;
        ScanOptions options = ScanOptions.scanOptions().match(REDIS_KEY_PREFIX + "*").count(1000).build();
        try (Cursor<byte[]> cursor = stringRedisTemplate.executeWithStickyConnection(
                redisConnection -> redisConnection.scan(options)
        )) {
            if (cursor != null) {
                while (cursor.hasNext()) {
                    cursor.next();
                    count++;
                }
            }
        }
        return count;
    }

    private String getRedisKey(PreferenceCacheKey cacheKey) {
        return REDIS_KEY_PREFIX + cacheKey.toString();
    }
}
