package io.terminus.trantor2.module.runtime.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.trantor2.common.internal.InternalComponent;
import io.terminus.trantor2.module.runtime.entity.Preference;
import io.terminus.trantor2.module.runtime.entity.PreferenceData;
import io.terminus.trantor2.module.runtime.repo.AbstractPreferenceRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;

import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@InternalComponent
@RequiredArgsConstructor
public class CaffeinePreferenceCache extends AbstractPreferenceCache {
    private final List<AbstractPreferenceRepo<?>> preferenceRepos;
    private Map<Preference.PreferenceType, AbstractPreferenceRepo<?>> repoMap;

    private final Cache<PreferenceCacheKey, PreferenceData> cache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(2000).build();

    @PostConstruct
    public void init() {
        repoMap = preferenceRepos.stream().collect(Collectors.toMap(
                AbstractPreferenceRepo::getPreferenceType,
                Function.identity())
        );
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends PreferenceData> T get(@Nonnull PreferenceCacheKey cacheKey) {
        return (T) cache.get(cacheKey, this::load);
    }


    @SuppressWarnings("unchecked")
    public <T extends PreferenceData> T getIfPresent(@Nonnull PreferenceCacheKey cacheKey) {
        return (T) cache.getIfPresent(cacheKey);
    }

    @Override
    public void put(@Nonnull PreferenceCacheKey cacheKey, @Nullable PreferenceData preference) {
        if (preference != null) {
            cache.put(cacheKey, preference);
        } else {
            cache.invalidate(cacheKey);
        }
    }

    @Override
    public void invalidate(@Nonnull PreferenceCacheKey cacheKey) {
        cache.invalidate(cacheKey);
    }

    @Override
    public void invalidateAll() {
        cache.invalidateAll();
    }

    public long size() {
        return cache.estimatedSize();
    }

    public @Nullable PreferenceData load(@NonNull PreferenceCacheKey cacheKey) {
        return Optional.ofNullable(repoMap.get(cacheKey.getType())
                        .findByScopeAndCurUser(cacheKey.getScope()))
                .map(Preference::getData)
                .orElse(null);
    }
}
