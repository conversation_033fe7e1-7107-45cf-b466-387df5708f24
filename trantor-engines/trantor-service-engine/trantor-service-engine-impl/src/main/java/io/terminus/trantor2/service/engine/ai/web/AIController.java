package io.terminus.trantor2.service.engine.ai.web;

import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.service.common.ai.prompt.PromptManager;
import io.terminus.trantor2.service.common.utils.Placeholder;
import io.terminus.trantor2.service.dsl.Agent;
import io.terminus.trantor2.service.dsl.enums.VariableType;
import io.terminus.trantor2.service.dsl.properties.HttpTool;
import io.terminus.trantor2.service.dsl.properties.LlmModel;
import io.terminus.trantor2.service.dsl.properties.ServiceTool;
import io.terminus.trantor2.service.dsl.properties.SkillTool;
import io.terminus.trantor2.service.engine.ServiceEngine;
import io.terminus.trantor2.service.engine.ai.client.httpclient.StreamingHandler;
import io.terminus.trantor2.service.engine.ai.context.AIContext;
import io.terminus.trantor2.service.engine.ai.core.chat.session.ChatSessionManager;
import io.terminus.trantor2.service.engine.ai.core.memory.ChatMemory;
import io.terminus.trantor2.service.engine.ai.core.memory.MemoryManager;
import io.terminus.trantor2.service.engine.ai.core.message.AiMessage;
import io.terminus.trantor2.service.engine.ai.core.message.Content;
import io.terminus.trantor2.service.engine.ai.core.message.CurrentAgent;
import io.terminus.trantor2.service.engine.ai.core.message.Message;
import io.terminus.trantor2.service.engine.ai.core.message.MessageBuilder;
import io.terminus.trantor2.service.engine.ai.core.message.MessageHelper;
import io.terminus.trantor2.service.engine.ai.core.message.MetadataConstant;
import io.terminus.trantor2.service.engine.ai.core.message.PingMessage;
import io.terminus.trantor2.service.engine.ai.core.message.TextContent;
import io.terminus.trantor2.service.engine.ai.core.message.UserMessage;
import io.terminus.trantor2.service.engine.ai.core.notify.session.AsyncNotify;
import io.terminus.trantor2.service.engine.ai.core.notify.session.NotifySessionManager;
import io.terminus.trantor2.service.engine.ai.core.session.Session;
import io.terminus.trantor2.service.engine.ai.llm.LlmClientService;
import io.terminus.trantor2.service.engine.ai.llm.tool.execution.ToolExecutionFactory;
import io.terminus.trantor2.service.engine.ai.llm.util.ToolCallUtil;
import io.terminus.trantor2.service.engine.ai.web.request.DynamicGreetingsRequest;
import io.terminus.trantor2.service.engine.ai.web.request.DynamicPromptRequest;
import io.terminus.trantor2.service.engine.ai.web.request.DynamicValueRequest;
import io.terminus.trantor2.service.engine.ai.web.request.NewSessionAndGreetingsRequest;
import io.terminus.trantor2.service.engine.ai.web.request.RecommendQuestionsRequest;
import io.terminus.trantor2.service.engine.ai.web.request.SendNoticeRequest;
import io.terminus.trantor2.service.engine.ai.web.response.NewSessionAndGreetings;
import io.terminus.trantor2.service.engine.delegate.Key;
import io.terminus.trantor2.service.engine.delegate.Metadata;
import io.terminus.trantor2.service.engine.executor.async.AsyncTask;
import io.terminus.trantor2.service.engine.impl.context.SimpleVariableContextFactory;
import io.terminus.trantor2.service.engine.impl.context.VariableContext;
import io.terminus.trantor2.service.engine.impl.value.ValueFactory;
import io.terminus.trantor2.service.engine.loader.metadata.AgentMetadataQuery;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * AIController
 *
 * <AUTHOR> Created on 2025/5/8 15:56
 */
@Tag(name = "AI辅助接口", description = "AI相关的接口，与业务无关，主要用于辅助AI功能的实现")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/trantor/ai")
@ConditionalOnProperty(prefix = "ai", name = "enable", havingValue = "true")
public class AIController {

    private final ScheduledExecutorService noticeScheduler = Executors.newScheduledThreadPool(100);

    private final ServiceEngine serviceEngine;
    private final MemoryManager memoryManager;
    private final AgentMetadataQuery agentMetadataQuery;
    private final LlmClientService llmClientService;
    private final NotifySessionManager notifySessionManager;
    private final ChatSessionManager chatSessionManager;
    private final AsyncNotify asyncNotify;
    private final ToolExecutionFactory toolExecutionFactory;

    @Deprecated
    @GetMapping(value = "/new-session")
    public Response<String> newSession(@RequestParam(value = "bizKey", required = false) String bizKey) {
        Long userId = TrantorContext.getCurrentUserId();
        String sessionId = chatSessionManager.createNewSessionId(bizKey, userId);
        return Response.ok(sessionId);
    }

    @PostMapping(value = "/new-session")
    public Response<NewSessionAndGreetings> newSession(@RequestBody NewSessionAndGreetingsRequest request) {
        String agentKey = request.getAgentKey();
        Long userId = TrantorContext.getCurrentUserId();

        String sessionId = chatSessionManager.createNewSessionId(agentKey, userId);
        String greetings = request.getGreetings();

        if (StringUtils.isBlank(greetings)) {
            greetings = "你好，请问有什么可以帮忙的？";
        } else if (StringUtils.contains(greetings, "${")) {
            // 替换占位符需要的上下文
            VariableContext context = SimpleVariableContextFactory.create(serviceEngine);

            // 把页面变量放入上下文
            if (request.getPageVariables() != null) {
                context.setVariables(request.getPageVariables());
            }

            List<SkillTool> skillTools = request.getGreetingsRelatedTools();
            if (CollectionUtils.isNotEmpty(skillTools)) {
                // 调用工具获取变量
                for (SkillTool skillTool : skillTools) {
                    String tempVariableName = VariableType.NODE_OUTPUT.formatKey(skillTool.getKey());
                    // 判断欢迎词中是否使用了该工具的结果，如果没有使用则忽略该工具
                    if (StringUtils.contains(greetings, tempVariableName)) {
                        Object result = executeSkillTool(skillTool, context);
                        context.setVariable(tempVariableName, result);
                    }
                }
            }

            greetings = Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(greetings, (key) -> {
                Object value = context.getVariable(key.split("\\."));
                if (value != null) {
                    return value.toString();
                } else {
                    return null;
                }
            });
        }

        // 把开场白存储到历史记录中
        ChatMemory chatMemory = new ChatMemory(sessionId);
        chatMemory.setKey(agentKey);
        chatMemory.setUserId(userId);
        chatMemory.setTitle(MetadataConstant.FAKE_CHAT_TITLE);
        chatMemory.addMessage(MessageBuilder.newBuilder()
                .autoMessageId()
                .conversationId(sessionId)
                .currentAgent(getCurrentAgent(agentKey))
                .build(TextContent.ofText(greetings)));
        memoryManager.saveChatMemory(chatMemory);

        NewSessionAndGreetings result = new NewSessionAndGreetings();
        result.setSessionId(sessionId);
        result.setGreetings(greetings);

        return Response.ok(result);
    }

    private CurrentAgent getCurrentAgent(String agentKey) {
        Metadata metadata = agentMetadataQuery.findMeta(Key.of(TrantorContext.getTeamId(), agentKey));
        if (metadata != null && metadata.getDefinition() != null) {
            Agent agent = (Agent) metadata.getDefinition();
            return new CurrentAgent(agentKey, agent.getName(), agent.getProps().getAvatar());
        }
        return new CurrentAgent(agentKey, agentKey, null);
    }

    @GetMapping(value = "/notice-connection")
    public SseEmitter noticeConnection() {
        if (Boolean.TRUE.equals(TrantorContext.inConsole())) {
            log.warn("SSE notice-connection is not supported in console, user_id:{}",
                    TrantorContext.getCurrentUserId());
            throw new UnsupportedOperationException("SSE is not supported in console environment.");
        }

        SseEmitter sseEmitter = new SseEmitter(Long.MAX_VALUE);
        Session session = notifySessionManager.create(UUID.randomUUID().toString(), "", new StreamingHandler() {
            final LongAdder pingErrCounter = new LongAdder();

            @Override
            public void onMessage(String event, String data) {
                try {
                    sseEmitter.send(SseEmitter.event().name(event).data(data));
                    pingErrCounter.reset();
                } catch (Exception e) {
                    if (e instanceof java.lang.IllegalStateException ise) {
                        if ("ResponseBodyEmitter has already completed".equals(e.getMessage())) {
                            throw ise;
                        }
                    }
                    // 只要ping不通超过5次，则直接抛出异常，抛出异常后会主动关闭连接
                    if (pingErrCounter.sum() >= 5) {
                        throw new RuntimeException("SSE notice-connection ping error to many times!");
                    }
                    // 记录ping不通次数
                    if ("ping".equals(event)) {
                        if (pingErrCounter.sum() == 0) {
                            log.warn("SSE notice-connection send ping error!", e);
                        }
                        pingErrCounter.increment();
                    } else {
                        log.error("SSE notice-connection send error!", e);
                    }
                }
            }

            @Override
            public void onError(Throwable error) {
                try {
                    // 把sse连接关闭掉
                    sseEmitter.completeWithError(error);
                } catch (Exception ignored) {
                }
            }
        });

        final Long userId = session.getCurrentUserId();

        sseEmitter.onError(err -> {
            log.warn("SSE notice-connection error, user_id:{}, error: {}", userId, err.getMessage(), err);
            // 这里很诡异，收到了前端的broken err，但是前端的连接还能接受ping中
            // session.close();
        });

        sseEmitter.onTimeout(() -> {
            log.warn("SSE notice-connection timeout, user_id:{}", userId);
            session.close();
        });

        // 处理临时Session消息
        final NotifySessionManager.TemporaryNotify temporaryNotify = notifySessionManager.getTemporaryNotify(userId);
        if (temporaryNotify != null) {
            Runnable asyncTask = new AsyncTask() {
                @Override
                protected Object execute() {
                    // 发送该消息，就是让前端去恢复历史记录，不在发送消息，不然前端会显示2遍内容
                    PingMessage pingMessage = MessageBuilder.newBuilder()
                            .autoMessageId()
                            .conversationId(temporaryNotify.getSessionId())
                            .build();
                    asyncNotify.sendNotice(temporaryNotify.getAgentKey(), userId, pingMessage);
                    return null;
                }
            };
            noticeScheduler.schedule(asyncTask, 1L, TimeUnit.SECONDS);
        }

        session.sendPing();

        return sseEmitter;
    }

    @PostMapping(value = "/send-notice")
    public Response<Void> sendNotice(@Valid @RequestBody SendNoticeRequest request) {
        asyncNotify.sendNotice(request.getAgentKey(), request.getUserId(), request.getMessage());
        return Response.ok();
    }

    @PostMapping(value = {"/recommend-questions", "/recommend-prompt"/*该链接在830废弃*/})
    public Response<List<String>> recommendQuestions(@RequestBody RecommendQuestionsRequest request) {
        ChatMemory chatMemory = memoryManager.findChatMemory(request.getSessionId());
        if (chatMemory == null) {
            return Response.ok(Collections.emptyList());
        }

        String agentKey = null;
        List<Message> messages = chatMemory.getMessages().stream()
                .filter(m -> m instanceof AiMessage am && am.getContent() instanceof TextContent)
                .toList();
        AiMessage lastMessage = (AiMessage) messages.get(messages.size() - 1);
        if (lastMessage.getMeta() != null && lastMessage.getMeta().get("key") != null) {
            agentKey = (String) lastMessage.getMeta().get("key");
        }
        if (agentKey == null) {
            return Response.ok(Collections.emptyList());
        }
        Metadata metadata = agentMetadataQuery.findMeta(Key.of(TrantorContext.getTeamId(), agentKey));
        if (metadata == null) {
            return Response.ok(Collections.emptyList());
        }

        List<String> result = new ArrayList<>(3);
        Agent agent = (Agent) metadata.getDefinition();
        LlmModel llmModel = agent.getProps().getModel();

        if (agent.getProps().isUserQuestionsSuggest()) {
            String prompt = agent.getProps().getUserQuestionsSuggestionPrompt();
            Integer round = agent.getProps().getModel().getSetting().getChatRounds();

            // 查询历史记录
            List<String> history = MessageHelper.getHistoryMessages(chatMemory, round, message -> {
                StringBuilder historyBuilder = new StringBuilder();
                if (message instanceof UserMessage userMessage) {
                    historyBuilder.append(message.getRole().value()).append(": ");
                    for (Content content : userMessage.getContent()) {
                        if (content instanceof TextContent) {
                            historyBuilder.append(((TextContent) content).getText());
                        }
                    }
                } else if (message instanceof AiMessage aiMessage) {
                    historyBuilder.append(message.getRole().value()).append(": ");
                    if (aiMessage.getContent() instanceof TextContent textContent) {
                        historyBuilder.append(textContent.getText());
                    }
                } else {
                    historyBuilder.append(message.getRole().value()).append(": ");
                    historyBuilder.append(message.messageContent());
                }

                if (historyBuilder.isEmpty()) {
                    return null;
                } else {
                    return historyBuilder.toString();
                }
            }).stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (history.isEmpty()) {
                return Response.ok(Collections.emptyList());
            }

            Map<String, Object> env = MapUtil.of("historyChat", String.join("\n", history), "userPrompt", prompt);
            String sysPrompt = PromptManager.getPrompt("/prompts/recommend_questions.prompt", env);

            // 调用大模型
            String response = llmClientService.chatCompletion(
                    llmModel.getModelPublisher(),
                    llmModel.getName(),
                    llmModel.getSetting().getTemperature(),
                    llmModel.getSetting().getMaxTokens(),
                    sysPrompt,
                    String.class);
            try {
                result.addAll(JsonUtil.fromJson(response, new TypeReference<List<String>>() {
                }));
            } catch (Exception e) {
                log.info("recommendPrompt response: {}, parse array err", response, e);
            }
        }
        return Response.ok(result);
    }

    /**
     * 动态欢迎语
     *
     * @deprecated 合并到new-session接口中
     */
    @Deprecated
    @PostMapping("/dynamic-greetings")
    public Response<String> getDynamicGreetings(@RequestBody DynamicGreetingsRequest request) {
        String greetings = request.getGreetings();
        if (StringUtils.isBlank(greetings)) {
            return Response.ok("你好，请问有什么可以帮忙的？");
        }

        VariableContext context = SimpleVariableContextFactory.create(serviceEngine);

        List<SkillTool> skillTools = request.getGreetingsRelatedTools();
        if (CollectionUtils.isNotEmpty(skillTools)) {
            // 调用工具获取变量
            for (SkillTool skillTool : skillTools) {
                String tempVariableName = VariableType.NODE_OUTPUT.formatKey(skillTool.getKey());
                // 判断欢迎词中是否使用了该工具的结果，如果没有使用则忽略该工具
                if (StringUtils.contains(greetings, tempVariableName)) {
                    Object result = executeSkillTool(skillTool, context);
                    context.setVariable(tempVariableName, result);
                }
            }
        }

        String result = Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(greetings, (key) -> {
            Object value = context.getVariable(key.split("\\."));
            if (value != null) {
                return value.toString();
            } else {
                return null;
            }
        });

        return Response.ok(result);
    }

    private Object executeSkillTool(SkillTool skillTool, VariableContext context) {
        try {
            AIContext.init();
            AIContext.getContext().setVariableContext(context);
            String paramsJson;
            if (skillTool instanceof ServiceTool serviceTool) {
                Map<String, Object> params = ValueFactory.getDefaultValue(serviceTool.getInput(), context);
                paramsJson = JsonUtil.toJson(params);
            } else if (skillTool instanceof HttpTool) {
                paramsJson = null;
            } else {
                throw new IllegalArgumentException("Not support tool type: " + skillTool.getClass().getSimpleName());
            }
            String result = toolExecutionFactory.invokeTool(skillTool,
                    ToolCallUtil.getServiceKeyInLlmToolName(skillTool.getKey()), paramsJson);
            if (result != null) {
                return JsonUtil.fromJson(result, Object.class);
            }
            return null;
        } finally {
            AIContext.clear();
        }
    }

    @PostMapping("/dynamic-value")
    public Response<Object> getDynamicValue(@RequestBody DynamicValueRequest request) {
        VariableContext context = SimpleVariableContextFactory.create(serviceEngine, request.getVariables());
        Object value = ValueFactory.getValue(null, request.getValue(), context);
        return Response.ok(value);
    }

    @PostMapping("/dynamic-prompt")
    public Response<String> getDynamicPrompt(@RequestBody DynamicPromptRequest request) {
        VariableContext context = SimpleVariableContextFactory.create(serviceEngine, request.getVariables());
        String result = Placeholder.PLACE_DOLLAR_BRACES_HOLDER.replaceHolder(request.getPrompt(), (key) -> {
            Object value = context.getVariable(key.split("\\."));
            if (value != null) {
                return value.toString();
            } else {
                return null;
            }
        });
        return Response.ok(result);
    }
}
