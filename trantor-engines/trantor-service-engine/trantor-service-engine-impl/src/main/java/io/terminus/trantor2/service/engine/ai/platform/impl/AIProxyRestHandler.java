package io.terminus.trantor2.service.engine.ai.platform.impl;

import com.alibaba.fastjson.JSONValidator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.common.utils.ApplicationContextUtil;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.service.common.utils.HttpClient;
import io.terminus.trantor2.service.engine.ai.configuration.AiProperties;
import io.terminus.trantor2.service.engine.ai.platform.AIExternalRestHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class AIProxyRestHandler implements AIExternalRestHandler {
    private volatile static AIProxyRestHandler handler = null;
    /**
     * API接口前缀
     */
    public static final String API_PREFIX = "api/ai-proxy";
    /**
     * 接口请求地址
     */
    private final String urlPrefix;
    /**
     * AI-Proxy 接口必要的鉴权请求头信息
     */
    Map<String, String> headers;
    /**
     * AI 配置
     */
    private final AiProperties aiProperties;

    private AIProxyRestHandler() {
        aiProperties = (AiProperties) ApplicationContextUtil.getApplicationContext().getBean("aiProperties");
        String aiProxyDomain = aiProperties.getAiProxy().getDomain();
        String accessKeyId = aiProperties.getAiProxy().getAccessKeyId();
        urlPrefix = String.format("%s/%s", aiProxyDomain, API_PREFIX);
        headers = Maps.newHashMap();
        headers.put("Authorization", String.format("Bearer %s", accessKeyId));
    }

    public static AIProxyRestHandler newInstance() {
        // DCL 实例化
        if (null == handler) {
            synchronized (AIProxyRestHandler.class) {
                if (null == handler) {
                    handler = new AIProxyRestHandler();
                }
            }
        }
        return handler;
    }

    @Setter
    @Getter
    public static class ClientDetails {
        private AIProxyClient client;
        private List<AIProxyProviderModel> models;
    }

    @Setter
    @Getter
    public static class AIProxyClient {
        private String id;
        private String name;
        private String desc;
        private String accessKeyId;
        private String secretKeyId;
    }

    @Setter
    @Getter
    public static class AIProxyProviderModel {
        private AIProxyModel model;
        private AIProxyProvider provider;
    }

    /**
     * 根据供应商ID获取该供应商提供的模型名称列表
     *
     * @param providerType 供应商类型
     * @return 模型名称列表
     */
    public List<Map<String, Object>> getModelNamesWithTypeByProviderType(String providerType) {
        // @formatter:off
        return getClientDetails().getModels()
                                 .stream()
                                 .filter(item -> item.getProvider().getType().equals(providerType) &&
                                                 // 过滤指定模型名称
                                                 !filterModelName(item.getModel().getName()))
                                 .map(item -> {
                                     Map<String, Object> map = Maps.newHashMap();
                                     map.put("type", item.getModel().getType().toUpperCase());
                                     // 如果是gpt-4-32k 则需要特殊处理，因为有两个可用的服务实例（us、jp）
                                     if (item.getModel().getName().equals("gpt-4-32k")) {
                                          map.put("name", String.format("%s_%s", item.getModel().getName(), item.getProvider().getName()));
                                     } else {
                                         map.put("name", item.getModel().getName());
                                     }
                                     if (Objects.nonNull(item.getModel().getMetadata())) {
                                         if (Objects.nonNull(item.getModel().getMetadata().get_public().getAbilities())) {
                                             map.put("abilities", item.getModel().getMetadata().get_public().getAbilities().getAbilities());
                                         }
                                     }
                                     return map;
                                 })
                                 .collect(Collectors.toList());
        // @formatter:on
    }

    @Setter
    @Getter
    public static class AIProxyProvider {
        private String id;
        private String name;
        private String desc;
        private String type;
    }

    @Setter
    @Getter
    public static class AIProxyModel {
        private String id;
        private String name;
        private String desc;
        private String type;
        private String providerId;
        private String publisher;
        private AIProxyModelMetadata metadata;
    }

    /**
     * 获取供应商信息
     *
     * @return 供应商对象信息
     */
    public Map<String, AIProxyProvider> getProviders() {
        // @formatter:off
        return getClientDetails().getModels()
                                 .stream()
                                 .map(AIProxyProviderModel::getProvider)
                                 .collect(Collectors.groupingBy(AIProxyProvider::getType))
                                 .entrySet()
                                 .stream()
                                 .collect(Collectors.toMap(Map.Entry::getKey, value -> value.getValue().get(0)));
        // @formatter:on
    }

    @Setter
    @Getter
    private static class AIProxyModelMetadata {
        @JsonProperty("public")
        private AIProxyModelMetadataPublic _public;

        @Setter
        @Getter
        private static class AIProxyModelMetadataPublic {
            private AIProxyModelMetadataAbilities abilities;
            private AIProxyModelMetadataContext context;
            private AIProxyModelMetadataPricing pricing;
            private String logo;

            @Setter
            @Getter
            private static class AIProxyModelMetadataAbilities {
                private List<String> abilities;
                @JsonProperty("input_modalities")
                private List<String> inputModalities;
                @JsonProperty("output_modalities")
                private List<String> outputModalities;
            }

            @Setter
            @Getter
            private static class AIProxyModelMetadataContext {
                @JsonProperty("context_length")
                private Integer contextLength;
                @JsonProperty("max_completion_tokens")
                private Integer maxCompletionTokens;
                @JsonProperty("max_prompt_tokens")
                private Integer maxPromptTokens;
            }

            @Setter
            @Getter
            private static class AIProxyModelMetadataPricing {
                private Object completion;
                private Object image;
                @JsonProperty("input_cache_read")
                private Object inputCacheRead;
                @JsonProperty("input_cache_write")
                private Object inputCacheWrite;
                @JsonProperty("internal_reasoning")
                private Object internalReasoning;
                @JsonProperty("prompt")
                private Object prompt;
                @JsonProperty("request")
                private Object request;
                @JsonProperty("unit")
                private Object unit;
                @JsonProperty("web_search")
                private Object webSearch;
            }
        }
    }

    @Deprecated
    public List<String> getModelNamesByProviderType(String providerType) {
        // @formatter:off
        return getClientDetails().getModels()
                                 .stream()
                                 .filter(item -> item.getProvider().getType().equals(providerType) &&
                                                 // 过滤指定模型名称
                                                 !filterModelName(item.getModel().getName()))
                                 .map(item -> {
                                     // 如果是gpt-4-32k 则需要特殊处理，因为有两个可用的服务实例（us、jp）
                                     if (item.getModel().getName().equals("gpt-4-32k")) {
                                         return String.format("%s_%s", item.getModel().getName(), item.getProvider().getName());
                                     } else {
                                         return item.getModel().getName();
                                     }
                                 })
                                 .collect(Collectors.toList());
        // @formatter:on
    }

    /**
     * 根据模型名称获取对应模型ID
     *
     * @param modelName 模型名称
     * @return 模型ID
     */
    public String getModelIdByName(String modelName) {
        return getClientDetails().getModels()
                .stream()
                .filter(item -> item.getModel().getName().equals(modelName))
                .findAny()
                .map(item -> item.getModel().getId())
                .orElse(null);
    }

    /**
     * 过滤掉符合过滤配置规则的模型
     *
     * @param modelName
     * @return 符合过滤去除规则就返回 true
     */
    public boolean filterModelName(String modelName) {
        if (Objects.nonNull(aiProperties.getModelFilter())) {
            String nameFilter = Objects.toString(aiProperties.getModelFilter().getNameContent(), "");
            String nameExclude = Objects.toString(aiProperties.getModelFilter().getNameExclude(), "");
            List<String> excludes = Arrays.stream(nameExclude.split(","))
                                          .map(String::trim)
                                          .collect(Collectors.toList());
            if (!excludes.isEmpty()) {
                if (excludes.contains(modelName)) {
                    return false;
                }
            }
            if (StringUtils.isNotBlank(nameFilter)) {
                return modelName.contains(aiProperties.getModelFilter().getNameContent());
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public ClientDetails getClientDetails() {
        String url = String.format("%s/clients/actions/get-by-access-key-id", urlPrefix);
        try {
            String result = HttpClient.INSTANCE.get(url, headers, null);
            if (JSONValidator.from(result).validate()) {
                Response<ClientDetails> response = JsonUtil.fromJson(result,
                                                                     new TypeReference<Response<ClientDetails>>() {});
                if (response.isSuccess()) {
                    log.info("client details response :\n{}", JsonUtil.toJson(response.getData()));
                    return response.getData();
                } else {
                    String errorMessage = response.getErr().getSuggest();
                    if (StringUtils.isBlank(errorMessage)) {
                        errorMessage = response.getErrMsg();
                    }
                    log.error("invoke ai-proxy request client details error: {}", errorMessage);
                }
            } else {
                log.error("invoke ai-proxy request client details error, response is not json");
            }
        } catch (Exception e) {
            log.error("invoke ai-proxy request client details exception", e);
        }
        return null;
    }
}
