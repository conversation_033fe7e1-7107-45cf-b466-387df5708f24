package io.terminus.trantor2.model.management.meta.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.datasource.model.ModuleDataSourceDTO;
import io.terminus.trantor2.datasource.service.DataSourceConfigService;
import io.terminus.trantor2.datasource.service.MilvusClientFactory;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.DataType;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import io.terminus.trantor2.meta.api.dto.MetaTreeNodeExt;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.api.service.QueryOp;
import io.terminus.trantor2.meta.event.EventPublisher;
import io.terminus.trantor2.meta.event.ModelShardingConfigUpdateEvent;
import io.terminus.trantor2.meta.request.SimpleModelInfoRequest;
import io.terminus.trantor2.meta.task.TaskOutputResult;
import io.terminus.trantor2.meta.request.QueryByKeysRequest;
import io.terminus.trantor2.meta.resource.BaseMeta;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.model.common.exception.ModelMetaException;
import io.terminus.trantor2.model.common.util.DeepCopyUtil;
import io.terminus.trantor2.model.management.api.model.Column;
import io.terminus.trantor2.model.management.api.model.DeleteIndexRequest;
import io.terminus.trantor2.model.management.api.model.ImportModelInfo;
import io.terminus.trantor2.model.management.api.model.Index;
import io.terminus.trantor2.model.management.api.model.ModelBatchCreateRequest;
import io.terminus.trantor2.model.management.api.model.SaveIndexRequest;
import io.terminus.trantor2.model.management.api.model.Schema;
import io.terminus.trantor2.model.management.api.model.SchemaPageRequest;
import io.terminus.trantor2.model.management.api.model.StatisticModelCreateByPersistentModel;
import io.terminus.trantor2.model.management.api.model.StatisticModelCreateBySchema;
import io.terminus.trantor2.model.management.ddl.DDLGenerator;
import io.terminus.trantor2.model.management.ddl.DDLInfo;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.check.ModelMetaDifference;
import io.terminus.trantor2.model.management.meta.check.ModuleModelMetaDifference;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.model.management.meta.dialect.entity.ColumnStructure;
import io.terminus.trantor2.model.management.meta.dialect.entity.TableStructure;
import io.terminus.trantor2.model.management.meta.domain.*;
import io.terminus.trantor2.model.management.meta.domain.search.SearchFieldType;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelConfigMeta;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelFieldConfigMeta;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingConfig;
import io.terminus.trantor2.model.management.meta.domain.sharding.ShardingField;
import io.terminus.trantor2.model.management.meta.domain.sharding.type.ShardingType;
import io.terminus.trantor2.model.management.meta.domain.sharding.type.SubStringProperties;
import io.terminus.trantor2.model.management.meta.domain.statistic.ImportType;
import io.terminus.trantor2.model.management.meta.domain.statistic.StatisticConfig;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticFieldConfig;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticValueSubstitution;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticValueSubstitutionType;
import io.terminus.trantor2.model.management.meta.enums.IndexTypeEnum;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.enums.SearchModelSyncTypeEnum;
import io.terminus.trantor2.model.management.meta.model.*;
import io.terminus.trantor2.model.management.meta.repository.DataStructNodeRepo;
import io.terminus.trantor2.model.management.meta.system.SystemModelInstanceHolder;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.model.management.meta.util.SqlKeywordUtil;
import io.terminus.trantor2.model.management.meta.util.SystemReserved;
import io.terminus.trantor2.model.management.model.DDLGenerationRequest;
import io.terminus.trantor2.model.management.model.DiffRequest;
import io.terminus.trantor2.model.management.model.ModelShardingConfigDTO;
import io.terminus.trantor2.model.management.model.TableInfo;
import io.terminus.trantor2.model.management.model.search.SearchFullSyncRequest;
import io.terminus.trantor2.model.management.model.search.SearchModelSyncTaskDto;
import io.terminus.trantor2.model.management.model.search.SyncTaskQueryRequest;
import io.terminus.trantor2.module.meta.ConfigType;
import io.terminus.trantor2.module.meta.ModelShardingConfig;
import io.terminus.trantor2.module.meta.ModuleDatasourceConfig;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.TeamService;
import io.terminus.trantor2.properties.management.ModelManagementProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static io.terminus.trantor2.model.management.meta.util.AliasUtil.fieldKeyToAlias;


/**
 * @Author: 诸立达
 * @Date: 2023/2/24 4:43 下午
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DataStructNodeService implements ApplicationEventPublisherAware {
    /**
     * 模型字段标识校验正则
     */
    private static final String MODEL_FIELD_REGEX = "^[\\da-zA-Z_]+(\\$)?[a-z][\\da-z_]{0,255}";

    private static final String ORIGIN_ORG_ID_FIELD = "origin_org_id";

    private static final String ORDER_NUMBER_FIELD = "order_number";

    private static final String TENANT_ID_FIELD = "tenant_id";
    /**
     * 模型字段标识校验Pattern
     */
    private static final Pattern MODEL_FIELD_REGEX_PATTERN = Pattern.compile(MODEL_FIELD_REGEX);
    private final DataStructNodeRepo dataStructNodeRepo;
    private final DDLGenerator ddlGenerator;
    private final DataStructMetaCache cache;
    private final SystemModelInstanceHolder systemModelInstanceHolder;
    private ApplicationEventPublisher eventPublisher;
    private final RedissonClient redissonClient;
    private final SearchModelDumpService searchModelDumpService;
    private final ConfigurationService configurationService;
    private final DataSourceConfigService dataSourceConfigService;
    private final MilvusClientFactory milvusClientFactory;
    private final EventPublisher metaEventPublisher;
    private final ModelManagementProperties modelManagementProperties;
    private final MetaQueryService metaQueryService;
    private final TeamService teamService;

    public List<DataStructNode> findAllSearchModel(Long teamId) {
        return dataStructNodeRepo.findAllByTeamId(teamId).stream()
            .filter(DataStructNode::isSearchModel).collect(Collectors.toList());
    }

    public DataStructNode findModel(Long teamId, String key) {
        return cache.getModelMeta(teamId, null, key);
    }

    /**
     * 查询数据结构对象分页
     *
     * @param pageRequest
     * @return Pageable<DataStructNode>
     */
    public Paging<DataStructNode> page(DataStructNodePageRequest pageRequest) {
        return dataStructNodeRepo.page(pageRequest);
    }

    public List<DataStructNode> findAll(DataStructNodeQueryAllRequest pageRequest) {
        return dataStructNodeRepo.findAll(pageRequest);
    }

    public List<DataStructNode> findAllSimpleInfo(DataStructNodeQueryAllRequest pageRequest) {
        List<DataStructNode> models = dataStructNodeRepo.findAll(pageRequest);
        if (CollectionUtils.isEmpty(models)) {
            return new ArrayList<>();
        }

        List<DataStructNode> result = new ArrayList<>();
        models.forEach(item -> {
            DataStructNode model = new DataStructNode();
            model.setKey(item.getKey());
            model.setName(item.getName());

            List<DataStructFieldNode> children = new ArrayList<>();
            item.getChildren().forEach(child -> {
                DataStructFieldNode field = new DataStructFieldNode();
                field.setKey(child.getKey());
                field.setName(child.getName());

                DataStructFieldProperties props = new DataStructFieldProperties();
                props.setFieldType(child.getProps().getFieldType());
                props.setRelationMeta(child.getProps().getRelationMeta());
                props.setDictPros(child.getProps().getDictPros());
                field.setProps(props);

                children.add(field);
            });
            model.setChildren(children);

            result.add(model);
        });

        return result;
    }

    public List<DataStructNode> listModelInfo(ModelListRequest request) {
        if (request.getType() == null) {
            request.setType(DataStructType.PERSIST);
        }

        if (StringUtils.isEmpty(request.getParentKey())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{"parentKey"});
        }

        List<DataStructNode> models = dataStructNodeRepo.listModelInfo(request);

        // ER 图仅需模型关联关系，去除无用的字段，避免返回过大
        List<DataStructNode> result = new ArrayList<>();
        models.forEach(model -> {
            if (request.getType() != model.getProps().getType() || model.getChildren() == null) {
                return;
            }
            List<DataStructFieldNode> fields = model.getChildren().stream().filter(x -> x.getProps().getRelationMeta() != null).collect(Collectors.toList());
            model.setChildren(fields);

            result.add(model);
        });

        return result;
    }

    public Map<String, String> generateDDLByModelKey(DDLGenerationRequest request) {
        if (CollectionUtils.isEmpty(request.getModelKeys())) {
            return new HashMap<>();
        }

        Map<String, String> ddls = new HashMap<>();
        for (String modelKey : request.getModelKeys()) {
            DataStructNode meta = findModel(TrantorContext.getTeamId(), modelKey);
            if (meta.getProps().getType() == DataStructType.PERSIST) {
                ddls.put(modelKey, ddlGenerator.createDDL(meta).collectSQLs().get(0));
            }
        }

        return ddls;
    }

    public List<DDLInfo> diffModelMetaWithSchema(DiffRequest request, QueryOp queryOp) {
        if (null == request.getIsExecutable()) {
            request.setIsExecutable(false);
        }
        List<DDLInfo> ddlInfos = new ArrayList<>();
        List<DataStructNode> metas;
        if (null == request.getTeamId()) {
            throw new ModelMetaException(ErrorType.BAD_REQUEST, "teamId不能为空");
        }
        if (null == request.getModuleKey()) {
            metas = dataStructNodeRepo.findAllByTeamId(request.getTeamId());
            if (CollectionUtils.isEmpty(metas)) {
                log.info(String.format("通过 teamId: %d 获取元数据为空", request.getTeamId()));
                return ddlInfos;
            }
            for (DataStructNode meta : metas) {
                if (meta.getProps().getType() == DataStructType.PERSIST) {
                    ddlInfos.add(ddlGenerator.update(meta,false, request.getIsExecutable()));
                }
            }
        } else {
            List<MetaTreeNodeExt> metaTreeNodes = queryOp.findAll(Field.type().equal(MetaType.Model.name()).and(Cond.moduleOf(request.getModuleKey())));
            if (CollectionUtils.isEmpty(metaTreeNodes)) {
                log.info(String.format("通过 teamId: %d, moduleKey: %s 获取元数据为空", request.getTeamId(), request.getModuleKey()));
                return ddlInfos;
            }
            for (MetaTreeNodeExt metaTreeNode : metaTreeNodes) {
                DataStructNode meta = findModel(request.getTeamId(), metaTreeNode.getKey());
                if (meta.getProps().getType() == DataStructType.PERSIST) {
                    ddlInfos.add(ddlGenerator.update(meta,false, request.getIsExecutable()));
                }
            }
        }
        return ddlInfos;
    }

    public Paging<DataStructObjectInfo> getInfo(DataStructNodePageRequest request) {
        List<DataStructObjectInfo> result = new ArrayList<>();
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        long total = dataStructNodeRepo.count(request);
        List<DataStructNode> keyAndName;
        if (request.getKey() == null && request.getType() == null) {
            keyAndName = dataStructNodeRepo.findAliasAndName(request.getTeamId(), pageNo, pageSize);
        } else if (request.getKey() != null && request.getType() == null) {
            keyAndName = dataStructNodeRepo.findAliasAndNameByKey(request.getTeamId(), "%" + request.getKey() + "%", pageNo, pageSize);
        } else if (request.getKey() == null && request.getType() != null) {
            keyAndName = dataStructNodeRepo.findAliasAndNameByType(request.getTeamId(), request.getType().name(), pageNo, pageSize);
        } else {
            keyAndName = dataStructNodeRepo.findAliasAndNameByKeyAndType(request.getTeamId(), "%" + request.getKey() + "%", request.getType().name(), pageNo, pageSize);
        }
        if (CollectionUtil.isNotEmpty(keyAndName)) {
            result.addAll(keyAndName.stream()
                .map(it -> new DataStructObjectInfo(it.getAlias(), it.getName())).collect(Collectors.toList()));
        }
        return new Paging<>((int) total, result);
    }

    /**
     * 分页查询指定条件的数据结构对象
     *
     * @param pageRequest 分页查询参数对象
     * @return
     */
    public Paging<DataStructNode> page(WorkspaceDataStructNodePageRequest pageRequest) {
        return dataStructNodeRepo.page(pageRequest);
    }


    /**
     * 查询模块内全量表结构
     */
    public Paging<TableInfo> queryTables(SchemaPageRequest tablesRequest) {
        List<TableInfo> tableInfos = ddlGenerator.getTables(tablesRequest.getTeamId(), tablesRequest.getModuleKey(), tablesRequest.getTableName(), tablesRequest.getPageable());
        Long pageTotal = ddlGenerator.getPageTotal(tablesRequest.getTeamId(), tablesRequest.getModuleKey(), tablesRequest.getTableName(), tablesRequest.getPageable());
        return new Paging<>(pageTotal, tableInfos);
    }

    /**
     * 编辑数据结构对象
     */
    @Transactional(rollbackFor = Exception.class)
    public DataStructNode save(SaveStructNodeRequest request) {
        DataStructNode dataStructNode = request.convertToEntity();
        // 老版本兼容，模型编辑保存时补充搜索模型信息
        formSearchInfoWhenUpdateModel(dataStructNode);

        doSave(dataStructNode);

        // 为了兼容多环境元数据迁移使用的，当通过save接口来新增，且是搜索模型时，自动触发全量同步
        if (null == request.getId() && dataStructNode.isSearchModel()) {
            searchModelDumpService.doSaveSearchModel(dataStructNode, SearchModelSyncTypeEnum.CREATE);
        }

        return dataStructNode;
    }

    @Transactional
    public void createStatisticModelByPersistentModel(StatisticModelCreateByPersistentModel request) {
        validateStatisticModelCreateByPersistentModel(request);

        List<DataStructNode> persistentModels = dataStructNodeRepo.findAllByKeys(request.getTeamId(), request.getPersistentModelKeys());
        if (CollectionUtils.isEmpty(persistentModels) || persistentModels.size() != request.getPersistentModelKeys().size()) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, String.format(ErrorType.MODEL_INVALID_STATE.getMessage(), "查询结果数量不匹配"), new Object[]{"查询结果数量不匹配"});
        }

        persistentModels.forEach(model -> {
            DataStructNode statisticModel = new DataStructNode();

            String key = TrantorContext.getModuleKey() + KeyUtil.MODULE_SEPARATOR + KeyUtil.shortKey(model.getKey()) + "_statistic";
            statisticModel.setKey(key);
            statisticModel.setAlias(key);
            statisticModel.setName(model.getName() + "统计模型");
            statisticModel.setTeamId(request.getTeamId());
            statisticModel.setParentKey(request.getParentKey());

            DataStructProperties statisticProps = new DataStructProperties();
            statisticProps.setType(DataStructType.STATISTIC);
            statisticProps.setStatisticConfig(new StatisticConfig(ImportType.PERSISTENT, null, null));
            statisticProps.setTableName(model.getProps().getTableName());
            statisticModel.setProps(statisticProps);

            List<DataStructFieldNode> fields = new ArrayList<>();
            model.getChildren().forEach(field -> {
                if (field.getProps().getRelationMeta() != null) {
                    if (field.getProps().getRelationMeta().getRelationType() == ModelRelationTypeEnum.PARENT_CHILD) {
                        // 忽略一对多字段
                        return;
                    }
                    // 一对一字段统一改为数字类型
                    field.getProps().setFieldType(FieldType.NUMBER);
                    field.getProps().setRelationMeta(null);
                } else if (field.getProps().getFieldType() == FieldType.EMAIL || field.getProps().getFieldType() == FieldType.ATTACHMENT) {
                    field.getProps().setFieldType(FieldType.TEXT);
                } else if (field.getProps().getFieldType() == FieldType.ENUM) {
                    // 持久模型的枚举类型字段统一改为文本字段， 枚举值改为
                    field.getProps().setFieldType(FieldType.TEXT);
                    if (CollectionUtils.isNotEmpty(field.getProps().getDictPros().getDictValues())) {
                        List<StatisticValueSubstitution> substitutions = new ArrayList<>();
                        field.getProps().getDictPros().getDictValues().forEach(dictItem -> {
                            StatisticValueSubstitution substitution = new StatisticValueSubstitution();
                            substitution.setType(StatisticValueSubstitutionType.CUSTOM);
                            substitution.setOldValue(dictItem.get("value"));
                            substitution.setNewValue(dictItem.get("label"));
                            substitutions.add(substitution);
                        });

                        StatisticFieldConfig statisticFieldConfig = new StatisticFieldConfig();
                        statisticFieldConfig.setValueSubstitutions(substitutions);
                        field.getProps().setStatisticFieldConfig(statisticFieldConfig);
                    }

                }

                fields.add(field);
            });
            statisticModel.setChildren(fields);

            dataStructNodeRepo.save(statisticModel);
        });
    }

    public TaskOutputResult createStatisticModelBySchema(StatisticModelCreateBySchema request) {
        // 1. 根据 module 获取数据源 2. 遍历 table schema 列表，获取 table schema 信息 3. 根据 schema 信息反向生成统计模型元数据 4. 持久化统计模型元数据
        validateStatisticModelCreateBySchema(request);

        List<TaskOutputResult.ResultDetail> failDetails = new ArrayList<>();
        request.getModels().forEach(item -> {
            TableStructure tableStructure = ddlGenerator.getTableStructure(request.getTeamId(), AliasUtil.moduleKey(item.getModelKey()), item.getTableName());
            if (tableStructure == null) {
                log.error("failed to fetch table info for table: {}", item.getTableName());
                return;
            }

            DataStructNode exist = this.findModel(request.getTeamId(), item.getModelKey());
            if (exist != null) {
                log.info("model {} already exists", item.getModelKey());
                failDetails.add(TaskOutputResult.ResultDetail.failed(item.getModelKey(), "模型已存在"));
                return;
            }

            // 根据 table schema 构建模型元数据数据结构
            DataStructNode dataStructNode = buildStatisticModelMeta(request, item, tableStructure);

            dataStructNodeRepo.save(dataStructNode);
        });

        TaskOutputResult result = new TaskOutputResult();

        TaskOutputResult.ResultSummary summary = new TaskOutputResult.ResultSummary();
        summary.setTotal(request.getModels().size());
        summary.setFailed(failDetails.size());
        summary.setSuccess(request.getModels().size() - failDetails.size());
        result.setSummary(summary);

        result.setDetails(failDetails);

        return result;
    }

    public void syncStatisticModel(StatisticModelSyncRequest request) {
        if (StringUtils.isEmpty(request.getModelKey())) {
           throw new ModelMetaException(
                   ErrorType.MODEL_MISSING_PARAM,
                   String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "modelKey"),
                   new Object[]{"modelKey"});
        }

        DataStructNode dataStructNode = dataStructNodeRepo.findByKey(request.getModelKey()).orElse(null);
        if (dataStructNode == null) {
            throw new ModelMetaException(
                    ErrorType.NODE_NOT_FOUND_ERROR,
                    String.format(ErrorType.NODE_NOT_FOUND_ERROR.getMessage(), request.getModelKey()),
                    new Object[]{request.getModelKey()});
        }

        if (dataStructNode.getProps().getStatisticConfig() == null || dataStructNode.getProps().getStatisticConfig().getImportType() == ImportType.SQL) {
            throw new ModelMetaException(
                    ErrorType.MODEL_INVALID_STATE,
                    String.format(ErrorType.MODEL_INVALID_STATE.getMessage(), request.getModelKey()),
                    new Object[]{ "SQL 生成的统计模型不支持同步: " + request.getModelKey()});
        }

        if (dataStructNode.getProps().getStatisticConfig().getImportType() == ImportType.PERSISTENT) {
            // 同步持久模型字段新增/删除至统计模型
            String persistentModelKey = request.getModelKey().substring(0, request.getModelKey().length() - "_statistic".length());
            DataStructNode persistentModel = dataStructNodeRepo.findByKey(persistentModelKey).orElse(null);
            if (persistentModel == null) {
                throw new ModelMetaException(
                        ErrorType.NODE_NOT_FOUND_ERROR,
                        String.format(ErrorType.NODE_NOT_FOUND_ERROR.getMessage(), persistentModelKey),
                        new Object[]{persistentModelKey});
            }

            Map<String, DataStructFieldNode> fieldNodeMap = dataStructNode.toFieldMap();
            // 待新增字段
            List<DataStructFieldNode> toAddFields = new ArrayList<>();
            persistentModel.getChildren().forEach(field -> {
                if (fieldNodeMap.containsKey(field.getAlias())) {
                    return;
                }

                // 收集新增字段
                if (field.getProps().getRelationMeta() != null) {
                    if (field.getProps().getRelationMeta().getRelationType() == ModelRelationTypeEnum.PARENT_CHILD) {
                        // 忽略一对多字段
                        return;
                    }
                    // 一对一字段统一改为数字类型
                    field.getProps().setFieldType(FieldType.NUMBER);
                    field.getProps().setRelationMeta(null);
                } else if (field.getProps().getFieldType() == FieldType.EMAIL || field.getProps().getFieldType() == FieldType.ATTACHMENT) {
                    field.getProps().setFieldType(FieldType.TEXT);
                }

                toAddFields.add(field);
            });

            // 待删除字段
            List<DataStructFieldNode> toDeleteFields = new ArrayList<>();
            Map<String, DataStructFieldNode> persistentFieldMap = persistentModel.toFieldMap();
            dataStructNode.getChildren().forEach(field -> {
                if (persistentFieldMap.containsKey(field.getAlias()) || (field.getProps().getStatisticFieldConfig() != null && field.getProps().getStatisticFieldConfig().isComputeField())) {
                    return;
                }
                toDeleteFields.add(field);
            });

            dataStructNode.getChildren().addAll(toAddFields);
            dataStructNode.getChildren().removeAll(toDeleteFields);

            dataStructNodeRepo.save(dataStructNode);

        } else if (dataStructNode.getProps().getStatisticConfig().getImportType() == ImportType.SCHEMA) {
            // todo 同步物理表字段新增/删除至统计模型
            TableStructure tableStructure = ddlGenerator.getTableStructure(request.getTeamId(), AliasUtil.moduleKey(request.getModelKey()), dataStructNode.getProps().getTableName());
            if (tableStructure == null || MapUtils.isEmpty(tableStructure.getColumns())) {
                throw new ModelMetaException(
                        ErrorType.NODE_NOT_FOUND_ERROR,
                        String.format(ErrorType.NODE_NOT_FOUND_ERROR.getMessage(), dataStructNode.getProps().getTableName()),
                        new Object[]{dataStructNode.getProps().getTableName()});
            }


            // 待新增字段
            List<DataStructFieldNode> toAddFields = new ArrayList<>();
            Map<String, DataStructFieldNode> fieldMap = dataStructNode.getChildren().stream().collect(Collectors.toMap(DataStructFieldNode::getKey, Function.identity()));
            tableStructure.getColumns().values().forEach(column -> {
                if (fieldMap.containsKey(column.getColumnName())) {
                    return;
                }
                toAddFields.add(buildStatisticModelField(column));
            });

            // 待删除字段
            List<DataStructFieldNode> toDeleteFields = new ArrayList<>();
            dataStructNode.getChildren().forEach(field -> {
                if (tableStructure.getColumns().containsKey(field.getKey())
                        || (field.getProps().getStatisticFieldConfig() != null && field.getProps().getStatisticFieldConfig().isComputeField())) {
                    return;
                }
                toDeleteFields.add(field);
            });

            dataStructNode.getChildren().addAll(toAddFields);
            dataStructNode.getChildren().removeAll(toDeleteFields);

            dataStructNodeRepo.save(dataStructNode);
        }
    }

    private DataStructNode buildStatisticModelMeta(StatisticModelCreateBySchema request, ImportModelInfo importModelInfo, TableStructure tableStructure) {
        DataStructNode dataStructNode = new DataStructNode();

        dataStructNode.setTeamId(request.getTeamId());
        dataStructNode.setParentKey(request.getParentKey());
        dataStructNode.setKey(importModelInfo.getModelKey() + "_statistic");
        dataStructNode.setAlias(importModelInfo.getModelKey() + "_statistic");
        dataStructNode.setName(importModelInfo.getModelName() + "统计模型");

        DataStructProperties props = new DataStructProperties();
        props.setType(DataStructType.STATISTIC);
        props.setStatisticConfig(new StatisticConfig(ImportType.SCHEMA, null, null));
        props.setTableName(tableStructure.getTableName());
        dataStructNode.setProps(props);

        dataStructNode.setChildren(buildStatisticModelFields(tableStructure));

        return dataStructNode;
    }

    private List<DataStructFieldNode> buildStatisticModelFields(TableStructure tableStructure) {
        List<DataStructFieldNode> dataStructFieldNodes = new ArrayList<>();

        tableStructure.getColumns().values().forEach(column -> {
            dataStructFieldNodes.add(buildStatisticModelField(column));
        });

        return dataStructFieldNodes;
    }

    private DataStructFieldNode buildStatisticModelField(ColumnStructure column) {
        DataStructFieldNode dataStructFieldNode = new DataStructFieldNode();
        dataStructFieldNode.setKey(column.getColumnName());
        dataStructFieldNode.setAlias(AliasUtil.fieldKeyToAlias(column.getColumnName()));
        if (StringUtils.isNotEmpty(column.getComment())) {
            dataStructFieldNode.setName(column.getComment());
        } else {
            dataStructFieldNode.setName(column.getColumnName());
        }

        DataStructFieldProperties props = new DataStructFieldProperties();
        if (StringUtils.isNotEmpty(column.getComment())) {
            props.setComment(column.getComment());
        }
        props.setFieldType(inferType(column.getType()));
        fillFieldLengthProperty(props, column);
        props.setRequired(column.isNonNull());
        if (column.getDefaultValue() != null) {
            props.setDefaultValue(column.getDefaultValue());
        }

        dataStructFieldNode.setProps(props);

        return dataStructFieldNode;
    }

    private void validateStatisticModelCreateBySchema(StatisticModelCreateBySchema request) {
        if (request.getTeamId() == null) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "teamId" });
        }
        if (CollectionUtils.isEmpty(request.getModels())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "models" });
        }

        request.getModels().forEach(item -> {
            if (StringUtils.isEmpty(item.getModelKey())) {
                throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "modelKey" });
            }
            if (StringUtils.isEmpty(item.getTableName())) {
                throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "tableName" });
            }
        });
    }

    private void validateStatisticModelCreateByPersistentModel(StatisticModelCreateByPersistentModel request) {
        if (StringUtils.isEmpty(request.getParentKey())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "parentKey"), new Object[]{"parentKey"});
        }

        if (CollectionUtils.isEmpty(request.getPersistentModelKeys())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "persistentModelKeys"), new Object[]{"persistentModelKeys"});
        }
    }

    public void saveField(ModelFieldSaveRequest request) {
        validateSaveFieldRequest(request);

        DataStructNode dataStructNode = cache.getModelMeta(request.getTeamId(), null, request.getModelKey());
        if (dataStructNode == null) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS, String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), request.getModelKey()), new Object[]{request.getModelKey()});
        }

        switch (request.getOperationType()) {
            case CREATE:
                // 新增字段
                dataStructNode.getChildren().add(request.getField());
                break;
            case UPDATE:
                // 更新字段
                Map<String, DataStructFieldNode> fieldMap = dataStructNode.getChildren().stream().collect(Collectors.toMap(DataStructFieldNode::getKey, Function.identity()));
                if (!fieldMap.containsKey(request.getField().getKey())) {
                    throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS, String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(), request.getField().getKey()),
                        new Object[]{request.getField().getKey()});
                }
                fieldMap.put(request.getField().getKey(), request.getField());
                dataStructNode.setChildren(new ArrayList<>(fieldMap.values()));
                break;
            default:
                return;
        }

        // 老版本兼容，模型编辑保存时补充搜索模型信息
        formSearchInfoWhenUpdateModel(dataStructNode);

        doSave(dataStructNode);
    }

    private void validateSaveFieldRequest(ModelFieldSaveRequest request) {
        if (StringUtils.isEmpty(request.getModelKey())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "alias"), new Object[]{"alias"});
        }
        if (request.getField() == null) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "field"), new Object[]{"field"});
        }
        if (request.getOperationType() == null) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "operationType"), new Object[]{"operationType"});
        }
        return;
    }

    public void deleteField(ModelFieldDeleteRequest request) {
        if (StringUtils.isEmpty(request.getField())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "field"), new Object[]{"field"});
        }

        DataStructNode dataStructNode = cache.getModelMeta(request.getTeamId(), null, request.getModelKey());
        if (dataStructNode == null) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS, String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), request.getModelKey()), new Object[]{request.getModelKey()});
        }

        Map<String, DataStructFieldNode> fieldMap = dataStructNode.getChildren().stream().collect(Collectors.toMap(DataStructFieldNode::getKey, Function.identity()));
        if (!fieldMap.containsKey(request.getField())) {
            throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS, String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(), request.getField()), new Object[]{request.getField()});
        }
        fieldMap.remove(request.getField());
        dataStructNode.setChildren(new ArrayList<>(fieldMap.values()));

        // 老版本兼容，模型编辑保存时补充搜索模型信息
        formSearchInfoWhenUpdateModel(dataStructNode);

        doSave(dataStructNode);
    }

    /**
     * 当前界面不支持搜索模型相关配置， 所以当在console点击模型保存时，会丢失搜索模型原来props下面的信息
     * 需要补充回去props下面的搜索配置信息
     *
     * @param dataStructNode
     */
    private void formSearchInfoWhenUpdateModel(DataStructNode dataStructNode) {
        try {
            if (null == dataStructNode.getId()) {
                return;
            }
            DataStructNode dataStructNodeInDb = dataStructNodeRepo.findByKey(dataStructNode.getKey()).orElse(null);
            if (null == dataStructNodeInDb) {
                return;
            }
            if (dataStructNodeInDb.isSearchModel() && null == dataStructNode.getProps().getSearchModelConfigMeta()) {
                dataStructNode.getProps().setSearchModel(true);
                dataStructNode.getProps().setSearchModelConfigMeta(dataStructNodeInDb.getProps().getSearchModelConfigMeta());
            }
            // 还原日志配置 logConfig（与搜索配置同样的策略）
            if (dataStructNodeInDb.getProps() != null && dataStructNodeInDb.getProps().getLogConfig() != null
                && dataStructNode.getProps() != null) {
                dataStructNode.getProps().setLogConfig(dataStructNodeInDb.getProps().getLogConfig());
            }
        } catch (Exception e) {
            // 不影响主流程
            log.error("formSearchInfoWhenUpdateModel error,err:{},node:{}", e.getMessage(), dataStructNode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doSave(DataStructNode dataStructNode) {
        validationUpdateParams(dataStructNode);

        boolean isUpdate = dataStructNode.getId() != null;
        boolean lockSuccessFul = false;
        String key = "Lock" + dataStructNode.getKey() + dataStructNode.getAppId() + dataStructNode.getTeamId();
        RLock rLock = null;
        try {
            if (isUpdate) {
                // 更新操作加锁,保证同一时间只能有单一进行调用
                rLock = redissonClient.getLock(key);
                lockSuccessFul = tryLock(rLock, dataStructNode.getKey());
                // 删除一对多关系/一对多变成一对一时需要将反向自动生成的一对一删除掉
                cascadeDeleteRelation(dataStructNode);
            }
            // 校验创建入参相关方法
            validationParams(isUpdate, dataStructNode);
            initialProps(isUpdate, dataStructNode);
            // 执行一对多关联关系的创建
            assembleParentToChildrenRelation(dataStructNode);

            // 创建关系表时根据用户传入的关联模型生成两个关联字段
            if (!isUpdate && dataStructNode.getProps().isRelationModel()) {
                createRelationField(dataStructNode);
            }

            // 开启分表时填充默认分表配置值
            fillShardingConfigDefaultValue(dataStructNode.getProps());

            // 提前拿到保存前的元数据结构，用于后面 DDL 差异比对，需要以老的元数据为基准比较，避免用户手动在数据库添加的字段无法被 trantor 托管到，导致保存时误删
            DataStructNode oldModel = cache.getModelMeta(dataStructNode.getTeamId(), null, dataStructNode.getKey());

            syncI18nModel(dataStructNode);

            fillImmutableProps(dataStructNode, oldModel);

            // 持久化 数据结构对象
            dataStructNodeRepo.save(dataStructNode);

            // 若为持久化模型类型，调用接口生成 DDL
            DataStructType type = dataStructNode.getProps().getType();
            if (type == DataStructType.PERSIST) {
                ModuleDataSourceDTO config = dataSourceConfigService.queryModuleDataSourceConfig(dataStructNode.getTeamId(), KeyUtil.moduleKey(dataStructNode.getKey()));
                // 兼容 ERP 修改低版本元数据时未配置任何数据源的情况
                if (config != null) {
                    ddlGenerator.update(dataStructNode, oldModel, !isUpdate, config.getAutoDDL());
                }

                // 持久模型若开启向量化, 同步创建向量库的 collection，字段为为模型内的字段
                if (dataStructNode.getProps().isVectorDatabaseEnabled()) {
                    try {
                        createVectorCollection(dataStructNode);
                    } catch (Exception e) {
                        log.error("Failed to create vector collection for model: {}, error: {}", dataStructNode.getKey(), e.getMessage(), e);
                        throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "Failed to create vector collection, error: " + e.getMessage());
                    }
                }
            }
        } finally {
            if (isUpdate && lockSuccessFul) {
                rLock.unlock();
            }
        }

    }

    /**
     * 保存业务模型时创建/更新对应的 i18n 模型
     *
     * 1. i18n 模型不存在时创建
     * 2. i18n 模型存在时，以当前模型开启的 i18n 属性字段为准同步更新至 i18n 模型中
     * @param dataStructNode
     */
    private void syncI18nModel(DataStructNode dataStructNode) {
        if (dataStructNode.getProps().getType() != DataStructType.PERSIST || !dataStructNode.getProps().isI18nEnabled()) {
            return;
        }

        String i18nModelKey = dataStructNode.getKey() + "_i18n";
        DataStructNode i18nModel = cache.getModelMeta(dataStructNode.getTeamId(), null, i18nModelKey);
        if (i18nModel == null) {
            // 模型先创建后开启 i18n 场景，需要创建 i18n 模型
            i18nModel = new DataStructNode();
            i18nModel.setParentKey(dataStructNode.getParentKey());
            i18nModel.setKey(i18nModelKey);
            i18nModel.setName(dataStructNode.getName() + "i18n");
            i18nModel.setTeamId(dataStructNode.getTeamId());
            i18nModel.setAppId(dataStructNode.getAppId());

            DataStructProperties props = new DataStructProperties();
            props.setType(DataStructType.PERSIST);
            props.setTableName(dataStructNode.getProps().getTableName() + "_i18n");
            props.setMainField(dataStructNode.getProps().getMainField());
            props.setSourceModelKey(dataStructNode.getKey());
            i18nModel.setProps(props);

            // i18n 模型字段包含两部分: 1. i18n 模型的内置字段 2. 业务模型字段开启 i18n 属性的字段
            // 开启 i18n 属性的字段
            List<DataStructFieldNode> children = dataStructNode.getChildren().stream()
                    .filter(it -> it.getProps().isI18nEnabled())
                    .filter(it -> it.getProps().getFieldType() == FieldType.TEXT || it.getProps().getFieldType() == FieldType.MULTI_TEXT)
                    .collect(Collectors.toList());

            // i18n 模型内置字段
            Map<String, FieldType> commonFieldMap = new HashMap<>();
            commonFieldMap.put("id", FieldType.NUMBER);
            commonFieldMap.put("source_id", FieldType.NUMBER);
            commonFieldMap.put("locale", FieldType.TEXT);
            commonFieldMap.put("created_by", FieldType.OBJECT);
            commonFieldMap.put("created_at", FieldType.DATE);
            commonFieldMap.put("updated_by", FieldType.OBJECT);
            commonFieldMap.put("updated_at", FieldType.DATE);
            commonFieldMap.put("deleted", FieldType.NUMBER);
            commonFieldMap.put("version", FieldType.NUMBER);
            commonFieldMap.forEach((k, v) -> {
                DataStructFieldNode field = new DataStructFieldNode();
                field.setKey(k);
                field.setAlias(fieldKeyToAlias(k));

                DataStructFieldProperties fieldProperties = new DataStructFieldProperties();
                fieldProperties.setFieldType(v);
                fieldProperties.setColumnName(k);
                fieldProperties.setAutoGenerated(true);
                fieldProperties.setIsSystemField(true);
                if (v == FieldType.NUMBER) {
                    fieldProperties.setLength(20);
                    fieldProperties.setIntLength(20);
                }

                switch (k) {
                    case "id":
                        field.setName("ID");
                        fieldProperties.setUnique(true);
                        fieldProperties.setRequired(true);
                        break;
                    case "source_id":
                        field.setName("来源 id");
                        fieldProperties.setRequired(true);
                        break;
                    case "locale":
                        field.setName("所属语言");
                        fieldProperties.setRequired(true);
                        break;
                    case "created_by":
                        field.setName("创建人");
                        RelationMeta relationMeta = new RelationMeta();
                        relationMeta.setRelationModelAlias(AliasUtil.moduleKey(dataStructNode.getKey()) + "$user");
                        relationMeta.setRelationType(ModelRelationTypeEnum.LINK);
                        fieldProperties.setRelationMeta(relationMeta);
                        break;
                    case "created_at":
                        field.setName("创建时间");
                        break;
                    case "updated_by":
                        field.setName("更新人");
                        RelationMeta updateRelationMeta = new RelationMeta();
                        updateRelationMeta.setRelationModelAlias(AliasUtil.moduleKey(dataStructNode.getKey()) + "$user");
                        updateRelationMeta.setRelationType(ModelRelationTypeEnum.LINK);
                        fieldProperties.setRelationMeta(updateRelationMeta);
                        break;
                    case "updated_at":
                        field.setName("更新时间");
                        break;
                    case "deleted":
                        field.setName("逻辑删除标识");
                        break;
                    case "version":
                        field.setName("版本号");
                        break;
                }
                field.setProps(fieldProperties);
                children.add(field);
            });

            i18nModel.setChildren(children);
            dataStructNodeRepo.save(i18nModel);

            ModuleDataSourceDTO config = dataSourceConfigService.queryModuleDataSourceConfig(dataStructNode.getTeamId(), KeyUtil.moduleKey(dataStructNode.getKey()));
            if (config != null) {
                // 创建 i18n table
                ddlGenerator.update(i18nModel, null, true, config.getAutoDDL());
            }
        } else if (dataStructNode.getId() != null) {
            DataStructNode newI18nModel = DeepCopyUtil.copy(i18nModel);
            // i18n 模型存在时，校验开启 i18n 的字段是否变化，若变化，需将变化同步更新至 i18n 模型
            // 业务模型更新时，同步更新 i18n 模型，主要是将 i18n 属性变化的字段更新至 i18n 模型中
            List<DataStructFieldNode> i18nSystemFields = i18nModel.getChildren().stream().filter(it -> it.getProps().getIsSystemField()).collect(Collectors.toList());
            List<DataStructFieldNode> i18nBusinessFields = dataStructNode.getChildren().stream().filter(it -> it.getProps().isI18nEnabled())
                    .filter(it -> it.getProps().getFieldType() == FieldType.TEXT || it.getProps().getFieldType() == FieldType.MULTI_TEXT)
                    .collect(Collectors.toList());

            List<DataStructFieldNode> i18nFields = new ArrayList<>();
            i18nFields.addAll(i18nSystemFields);
            i18nFields.addAll(i18nBusinessFields);
            newI18nModel.setChildren(i18nFields);
            dataStructNodeRepo.save(newI18nModel);

            ModuleDataSourceDTO config = dataSourceConfigService.queryModuleDataSourceConfig(dataStructNode.getTeamId(), KeyUtil.moduleKey(dataStructNode.getKey()));
            if (config != null) {
                // 更新 i18n table
                ddlGenerator.update(newI18nModel, i18nModel, false, config.getAutoDDL());
            }
        }
    }

    /**
     * 为关系模型新增两个关联字段
     * @param dataStructNode
     */
    private void createRelationField(DataStructNode dataStructNode) {
        if (dataStructNode.getProps().getRelationModelKeys() == null || dataStructNode.getProps().getRelationModelKeys().size() != 2) {
            return;
        }

        DataStructNode mainModel = cache.getModelMeta(dataStructNode.getTeamId(), null, dataStructNode.getProps().getRelationModelKeys().get(0));
        DataStructFieldNode mainModelField = generateRelationField(mainModel, dataStructNode);

        DataStructNode subModel = cache.getModelMeta(dataStructNode.getTeamId(), null, dataStructNode.getProps().getRelationModelKeys().get(1));
        DataStructFieldNode subModelField = generateRelationField(subModel, dataStructNode);

        dataStructNode.getChildren().add(mainModelField);
        dataStructNode.getChildren().add(subModelField);
    }

    private DataStructFieldNode generateRelationField(DataStructNode model, DataStructNode relationModel) {
        DataStructFieldNode field = new DataStructFieldNode();
        String key = getLinkFieldAlias(model.getKey(), relationModel);
        field.setKey(key);
        field.setAlias(AliasUtil.fieldKeyToAlias(key));
        field.setName(field.getAlias());

        DataStructFieldProperties props = new DataStructFieldProperties();
        props.setFieldType(FieldType.OBJECT);
        props.setColumnName(key);
        props.setComment(model.getName() + "关联字段");
        props.setAutoGenerated(true);
        props.setRelationField(true);

        RelationMeta relation = new RelationMeta();
        relation.setRelationType(ModelRelationTypeEnum.LINK);
        relation.setSync(false);
        relation.setCurrentModelAlias(relationModel.getKey());
        relation.setCurrentModelFieldAlias(field.getAlias());
        relation.setRelationModelAlias(model.getKey());
        props.setRelationMeta(relation);

        field.setProps(props);

        return field;
    }

    /**
     * 更新时，有些属性一旦创建后不允许更改，需要从已保存的元数据中获取填充
     * @param dataStructNode
     * @param oldModel
     */
    private void fillImmutableProps(DataStructNode dataStructNode, DataStructNode oldModel) {
        if (oldModel == null) {
            return;
        }

        if (dataStructNode.getProps() != null && oldModel.getProps() != null) {
            dataStructNode.getProps().setType(oldModel.getProps().getType());
            dataStructNode.getProps().setTableName(oldModel.getProps().getTableName());
        }

        if (dataStructNode.getProps().getStatisticConfig() != null && oldModel.getProps().getStatisticConfig() != null) {
            dataStructNode.getProps().getStatisticConfig().setImportType(oldModel.getProps().getStatisticConfig().getImportType());
        }

        // 如果模型是关系模型，更新时前端没有传此标识时不能变为 false
        if (oldModel.getProps().isRelationModel()) {
            dataStructNode.getProps().setRelationModel(true);
        }
    }

    private void fillShardingConfigDefaultValue(DataStructProperties props) {
        if (props.getShardingConfig() == null || !props.getShardingConfig().isEnabled()) {
            return;
        }

        // 内置参与分表计算的默认尾号数
        props.getShardingConfig().setShardingSuffixLength(3);

        List<ShardingField> shardingFields = props.getShardingConfig().getShardingFields().stream().filter(x -> StringUtils.isNotEmpty(x.getField())).collect(Collectors.toList());
        for (ShardingField shardingField : shardingFields) {
            if (shardingField.getShardingType() == ShardingType.SUBSTRING) {
                SubStringProperties subStringProperties = (SubStringProperties) shardingField.getShardingProperties();
                // 默认填充 SUBSTRING 分表算法的 end
                subStringProperties.setEnd(subStringProperties.getStart() + props.getShardingConfig().getShardingSuffixLength());
            }
        }
        props.getShardingConfig().setShardingFields(shardingFields);
    }

    private boolean tryLock(RLock rLock, String modelAlias) {
        try {
            boolean lockSuccessFul = rLock.tryLock(5, TimeUnit.SECONDS);
            if (!lockSuccessFul) {
                throw new ModelMetaException(ErrorType.UPDATE_GET_LOCK_FAILED,
                    String.format(ErrorType.UPDATE_GET_LOCK_FAILED.getMessage(), modelAlias), new Object[]{modelAlias});
            }
            return true;
        } catch (InterruptedException e) {
            throw new ModelMetaException(ErrorType.UPDATE_GET_LOCK_FAILED,
                String.format(ErrorType.UPDATE_GET_LOCK_FAILED.getMessage(), modelAlias), new Object[]{modelAlias});
        }
    }

    private void updateRelationMetaForLinkRelation(boolean isUpdate, DataStructNode dataStructNode) {
        if (!isUpdate) {
            return;
        }
        List<DataStructFieldNode> children = dataStructNode.getChildren();
        for (DataStructFieldNode fieldNode : children) {
            RelationMeta relationMeta = fieldNode.getProps().getRelationMeta();
            if (relationMeta == null || relationMeta.getRelationType() != ModelRelationTypeEnum.LINK) {
                continue;
            }
            String relationModelAlias = relationMeta.getRelationModelAlias();
            DataStructNode relatedModel = dataStructNodeRepo.findByAppIdAndTeamIdAndAlias(dataStructNode.getAppId(), dataStructNode.getTeamId(), relationModelAlias);
            if (relatedModel == null) {
                continue;
            }
            Optional<DataStructFieldNode> parentChildRelation = relatedModel.getChildren().stream().filter(it -> {
                RelationMeta relation = it.getProps().getRelationMeta();
                if (relation == null) {
                    return false;
                }
                if (relation.getRelationType() != ModelRelationTypeEnum.PARENT_CHILD) {
                    return false;
                }
                return dataStructNode.getAlias().equals(relation.getRelationModelAlias());
            }).findFirst();
            if (parentChildRelation.isPresent()) {
                parentChildRelation.get().getProps().getRelationMeta().setLinkModelFieldAlias(fieldNode.getAlias());
                // 更新元信息
                dataStructNodeRepo.save(relatedModel);
            }
        }
    }

    @Transactional
    public void delete(Collection<Long> ids, boolean force) {
        List<DataStructNode> dataStructNodes = dataStructNodeRepo.findAllById(Lists.newArrayList(ids));
        for (DataStructNode dataStructNode : dataStructNodes) {
            dataStructNodeRepo.delete(dataStructNode, false, force);

            if (dataStructNode.getProps().getType() == DataStructType.PERSIST) {
                try {
                    ddlGenerator.delete(dataStructNode);
                } catch (Exception e) {
                    log.error("delete model error: ", e);
                    throw new ModelMetaException(ErrorType.MODEL_DELETE_ERROR, new Object[]{ dataStructNode.getProps().getTableName() });
                }
            }
            configurationService.deleteConfig(dataStructNode.getTeamId(), dataStructNode.getKey());
        }
    }

    @Transactional
    public void delete(Long teamId, Collection<String> keys, boolean force) {
        keys = keys.stream().filter(it -> !isSystemModel(it)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(keys)) {
            return;
        }
        List<DataStructNode> dataStructNodes = dataStructNodeRepo.findAllByKeys(teamId, Lists.newArrayList(keys));
        for (DataStructNode dataStructNode : dataStructNodes) {
            dataStructNodeRepo.delete(dataStructNode, false, force);

            if (dataStructNode.getProps().getType() == DataStructType.PERSIST) {
                try {
                    ddlGenerator.delete(dataStructNode);
                } catch (Exception e) {
                    log.error("delete model error: ", e);
                    throw new ModelMetaException(ErrorType.MODEL_DELETE_ERROR, new Object[]{ dataStructNode.getProps().getTableName() });
                }

                // 删除关联的向量库 collection
                if (dataStructNode.getProps().isVectorDatabaseEnabled()) {
                    deleteVectorCollection(dataStructNode);
                }
            }
            configurationService.deleteConfig(dataStructNode.getTeamId(), dataStructNode.getKey());
        }
    }

    public List<ModuleModelMetaDifference> diffModeMetaByTeam() {
        List<DataStructNode> models = dataStructNodeRepo.findAllByTeamId(TrantorContext.getTeamId());
        if (CollectionUtil.isEmpty(models)) {
            return null;
        }

        List<ModelMetaDifference> diffModels = new ArrayList<>();
        models.forEach(model -> {
            if (model.getProps().getType() != DataStructType.PERSIST || model.getKey().endsWith("$user")) {
                // 非持久化模型 & 每个模块下的用户模型忽略
                return;
            }

            ModelMetaDifference diffModel = ddlGenerator.diffModelMetaWithSchema(model);
            if (diffModel != null) {
                diffModels.add(diffModel);
            }
        });

        if (CollectionUtils.isEmpty(diffModels)) {
            return null;
        }

        // 获取当前项目下的所有模块信息
        QueryOp q = metaQueryService.queryInTeam(TrantorContext.getTeamCode());
        Map<String, ModuleMeta> moduleMap = q.findAll(Field.type().equal(MetaType.Module.name()))
                .stream()
                .map(x -> BaseMeta.newInstanceFrom(x, ModuleMeta.class))
                .collect(Collectors.toMap(ModuleMeta::getKey, Function.identity()));

        // 获取当前项目下给定 parentKey 的所有目录路径
//        List<String> parentKeys = diffModels.stream().map(ModelMetaDifference::getParentKey).collect(Collectors.toList());
//        metaQueryService.findIndexBunchFromRoot(EditUtil.ctxFromThreadLocal(), parentKeys);


        /**
         * key: moduleKey  value: 当前模块下元数据存在差异的模型集合
         */
        Map<String, ModuleModelMetaDifference> moduleModelMetaDiffMap = new HashMap<>();
        for (ModelMetaDifference diffModel : diffModels) {
            String moduleKey = AliasUtil.moduleKey(diffModel.getKey());

            ModuleModelMetaDifference moduleModelMetaDifference = moduleModelMetaDiffMap.get(moduleKey);
            if (moduleModelMetaDifference == null) {
                moduleModelMetaDifference = new ModuleModelMetaDifference();

                ModuleMeta moduleMeta = moduleMap.get(moduleKey);
                if (moduleMeta == null) {
                    log.warn("module not found: {}", moduleKey);
                    continue;
                }

                moduleModelMetaDifference.setModuleId(moduleMeta.getId());
                moduleModelMetaDifference.setModuleKey(moduleMeta.getKey());
                moduleModelMetaDifference.setModuleName(moduleMeta.getName());
            }

            List<ModelMetaDifference> diffModelList = moduleModelMetaDifference.getDiffModels();
            if (diffModelList == null) {
                diffModelList = new ArrayList<>();
            }
            diffModelList.add(diffModel);
            moduleModelMetaDifference.setDiffModels(diffModelList);


            moduleModelMetaDiffMap.put(moduleKey, moduleModelMetaDifference);
        }

        return new ArrayList<>(moduleModelMetaDiffMap.values());
    }

    public ModelMetaDifference diffModeMetaByKey(String key) {
        Optional<DataStructNode> optional = dataStructNodeRepo.findByKey(key);
        if (!optional.isPresent()) {
            return null;
        }

        if (optional.get().getProps().getType() != DataStructType.PERSIST || optional.get().getKey().endsWith("$user")) {
            // 非持久化模型 & 用户模型忽略
            return null;
        }

        return ddlGenerator.diffModelMetaWithSchema(optional.get());
    }

    /**
     * 初始化字段配置，比如默认长度，是否系统字段
     */
    private void initialProps(boolean isUpdate, DataStructNode dataStructNode) {
        if (!isUpdate) {
            dataStructNode.setCreatedAt(new Date());
            dataStructNode.setCreatedBy(TrantorContext.getCurrentUser().getId());
        }
        dataStructNode.setUpdatedAt(new Date());
        dataStructNode.setUpdatedBy(TrantorContext.getCurrentUser().getId());
        List<DataStructFieldNode> children = dataStructNode.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        for (DataStructFieldNode child : children) {
            DataStructFieldProperties props = child.getProps();
            String name = child.getName();
            if (props != null) {
                if (props.getIsSystemField() == null) {
                    props.setIsSystemField(false);
                }

                if (StringUtils.isEmpty(props.getComment())) {
                    props.setComment(name);
                }

                setFieldDefaultLength(props);
            }
        }
    }

    private void setFieldDefaultLength(DataStructFieldProperties props) {
        FieldType fieldType = props.getFieldType();
        Integer length = props.getLength();
        switch (fieldType) {
            case TEXT:
            case ENUM:
            case EMAIL:
                if (length == null) {
                    props.setLength(256);
                }
                break;
            case ATTACHMENT:
                if (length == null) {
                    props.setLength(4000);
                } else if (length < 1000) {
                    // 调大最小附件长度限制为 1000(存在单 url 长度约 600 的数据)
                    props.setLength(1000);
                }
                break;
            case BOOL:
                props.setLength(1);
                break;
            default:
                break;
        }
    }

    private void assembleParentToChildrenRelation(DataStructNode dataStructNode) {
        DataStructType type = dataStructNode.getProps().getType();
        if (type != DataStructType.PERSIST) {
            return;
        }
        for (DataStructFieldNode dataStructFieldNode : dataStructNode.getChildren()) {
            if (FieldType.OBJECT.equals(dataStructFieldNode.getProps().getFieldType())
                && ModelRelationTypeEnum.PARENT_CHILD.equals(
                dataStructFieldNode.getProps().getRelationMeta().getRelationType())) {
                RelationMeta relationMeta = dataStructFieldNode.getProps().getRelationMeta();
                relationMeta.setCurrentModelAlias(dataStructNode.getKey());
                relationMeta.setLinkModelAlias(relationMeta.getRelationModelAlias());
                relationMeta.setCurrentModelFieldAlias(dataStructFieldNode.getAlias());
                relationMeta.setLinkModelFieldAlias(getChildLinkFieldAlias(dataStructFieldNode.getKey(), relationMeta.getRelationModelAlias(),
                        dataStructNode.getAppId(), dataStructNode.getTeamId(), dataStructNode.getKey(), StringUtils.isEmpty(relationMeta.getLinkModelFieldAlias())));

                DataStructNode child = cache.getModelMeta(dataStructNode.getTeamId(), null, relationMeta.getRelationModelAlias());
                relationMeta.setLinkRelationModel(child.getProps().isRelationModel());
                dataStructFieldNode.getProps().setRelationMeta(relationMeta);
            }
        }
    }

    /**
     * 如果关联子模型已经存在 关联了主模型字段，直接返回该字段
     * 否则就直接生成一个
     *
     * @param relationModelAlias
     * @param appId
     * @param teamId
     * @param masterAlias
     * @return String
     */
    private String getChildLinkFieldAlias(String currentFieldKey, String relationModelAlias, Long appId, Long teamId, String masterAlias, boolean isGenerate) {
        DataStructNode child = dataStructNodeRepo.
            findByAppIdAndTeamIdAndAlias(appId, teamId, relationModelAlias);

        // one-to-many relationship，two model definition should be in the same module, cross module isn't supported
        if (child == null) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, String.format(ErrorType.MODEL_INVALID_STATE.getMessage(), "主子模型不支持跨模块"),
                new Object[]{"主子模型不支持跨模块"});
        }

        if (child.getProps().isRelationModel() || !isGenerate) {
            // 优先返回主模型名匹配且反向生成的字段匹配主模型字段信息的字段(主模型可能有多个字段一对多关联同一子模型的情况）
            for (DataStructFieldNode dataStructFieldNode : child.getChildren()) {
                if (dataStructFieldNode.getProps().getRelationMeta() == null) {
                    continue;
                }

                if (dataStructFieldNode.getProps().getRelationMeta().getRelationModelAlias().equals(masterAlias)
                        && dataStructFieldNode.getKey().contains(currentFieldKey)) {
                    return dataStructFieldNode.getAlias();
                }
            }

            for (DataStructFieldNode dataStructFieldNode : child.getChildren()) {
                if (dataStructFieldNode.getProps().getRelationMeta() == null) {
                    continue;
                }

                if (dataStructFieldNode.getProps().getRelationMeta().getRelationModelAlias().equals(masterAlias)) {
                    return dataStructFieldNode.getAlias();
                }
            }
        }

        DataStructFieldNode dataStructFieldNode = new DataStructFieldNode();
        String targetFieldKey = getChildLinkFieldAlias(masterAlias, currentFieldKey, child);
        if (targetFieldKey.startsWith(KeyUtil.EXT_KEY_PREFIX)) {
            dataStructFieldNode.setExt(true);
        }
        String alias = fieldKeyToAlias(targetFieldKey);
        dataStructFieldNode.setAlias(alias);
        dataStructFieldNode.setKey(targetFieldKey);
        dataStructFieldNode.setName(alias);
        DataStructFieldProperties dataStructFieldProperties = new DataStructFieldProperties();
        dataStructFieldProperties.setFieldType(FieldType.OBJECT);
        dataStructFieldProperties.setColumnName(targetFieldKey);
        dataStructFieldProperties.setRequired(false);
        dataStructFieldProperties.setIsSystemField(false);
        RelationMeta relationMeta = new RelationMeta();
        relationMeta.setRelationModelKey(masterAlias);
        relationMeta.setRelationModelAlias(masterAlias);
        relationMeta.setRelationType(ModelRelationTypeEnum.LINK);
        relationMeta.setCurrentModelAlias(relationModelAlias);
        relationMeta.setCurrentModelFieldAlias(alias);
        dataStructFieldProperties.setRelationMeta(relationMeta);
        dataStructFieldProperties.setAutoGenerated(true);
        dataStructFieldNode.setProps(dataStructFieldProperties);
        child.getChildren().add(dataStructFieldNode);

        ModuleDatasourceConfig config = configurationService.query(teamId, KeyUtil.moduleKey(relationModelAlias), ConfigType.Module_Datasource);
        ddlGenerator.update(child, false, config == null || config.getAutoDDL());
        dataStructNodeRepo.save(child);
        return alias;
    }

    private String getChildLinkFieldAlias(String masterModelKey, String masterFieldKey, DataStructNode childModel) {
        String childLinkFieldAlias = AliasUtil.shortKey(masterModelKey) + "_" + masterFieldKey + "_id";
        if (BooleanUtils.isTrue(childModel.getExtensible())) {
            return KeyUtil.extKey(childLinkFieldAlias);
        } else if (childLinkFieldAlias.startsWith(KeyUtil.EXT_KEY_PREFIX)) {
            // childModel 在内部模块但 masterModelKey 的 shortKey 以 ext_ 为前缀，则去除 ext_ 前缀避免误判为二开字段
            return KeyUtil.originalKey(childLinkFieldAlias);
        }
        return childLinkFieldAlias;
    }

    private String getLinkFieldAlias(String masterModelKey, DataStructNode childModel) {
        String childLinkFieldAlias = AliasUtil.shortKey(masterModelKey) + "_id";
        if (BooleanUtils.isTrue(childModel.getExtensible())) {
            return KeyUtil.extKey(childLinkFieldAlias);
        } else if (childLinkFieldAlias.startsWith(KeyUtil.EXT_KEY_PREFIX)) {
            // masterModel 在内部模块但 masterModelKey 以 ext_ 为前缀且 childModel 也在内部模块，则去除 ext_
            return KeyUtil.originalKey(childLinkFieldAlias);
        }
        return childLinkFieldAlias;
    }

    private void validationParams(boolean isUpdate, DataStructNode request) {
        if (!isUpdate) {
            // 校验模型别名唯一
            validationAliasUnique(request);
        }

        // 校验模型名称
        validateModelAlias(request.getAlias());

        // 校验模型名是否为 sql 关键字
        validateSqlKeyword(request);

        // 校验分表配置合法性
        validateShardingConfig(request);

        // 校验组织字段
        validateOriginOrgIdField(request);

        // 校验多租户字段
        validateTenantIdField(request);

        // 校验排序字段
        validateOrderNumberField(request);

        // 校验字段名称
        validateFieldName(request);

        // 校验单表字段名称唯一
        validationModelFieldUnique(request.getChildren());

        // 校验关联模型必须存在
        validationRelationModelRequire(request.getChildren(), request.getTeamId());

        validateModelI18nEnabled(request);

        validateVectorDatabaseEnabled(request);
    }

    private void validateShardingConfig(DataStructNode model) {
        ShardingConfig shardingConfig = model.getProps().getShardingConfig();
        if (shardingConfig == null || !shardingConfig.isEnabled()) {
            return;
        }

        if (CollectionUtils.isEmpty(shardingConfig.getShardingFields())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, "分表键配置", new Object[]{"分表键配置"});
        }

        if (shardingConfig.getShardingFields().stream().noneMatch(ShardingField::isBusinessKey)) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "开启分表时必须配置分表键", new Object[]{"开启分表时必须配置分表键"});
        }

        if (shardingConfig.getShardingFields().stream().filter(ShardingField::isBusinessKey).count() > 1) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "开启分表时分表键只能配一个", new Object[]{"开启分表时分表键只能配一个"});
        }

        if (shardingConfig.getShardingFields().stream().filter(ShardingField::isBusinessKey).noneMatch(field -> field.getShardingType() == ShardingType.HASH)) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "开启分表时必分表键算法必须为 HASH", new Object[]{"开启分表时必分表键算法必须为 HASH"});
        }

        if (shardingConfig.getShardingFields().stream().filter(field -> field.getShardingType() == ShardingType.SUBSTRING).count() > 1) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "开启分表时路由键只能配一个", new Object[]{"开启分表时路由键只能配一个"});
        }

        if (shardingConfig.getShardingFields().stream().noneMatch(field -> field.getShardingType() == ShardingType.SUFFIX && field.getField().equals("id"))) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "开启分表时默认路由键 id 不存在或算法不是 SUFFIX", new Object[]{"开启分表时默认路由键 id 不存在或算法不是 SUFFIX"});
        }
    }

    private void validateModelAlias(String alias) {
        if (!matches(MODEL_FIELD_REGEX_PATTERN, alias)) {
            throw new ModelMetaException(ErrorType.DATA_STRUCT_ALIAS_ILLEGAL,
                String.format(ErrorType.DATA_STRUCT_ALIAS_ILLEGAL.getMessage(), alias), new Object[]{alias});
        }
//        if (systemModelInstanceHolder.isSystemModel(alias)) {
//            throw new ModelMetaException(ErrorType.DATA_STRUCT_ALIAS_ILLEGAL,
//                String.format(ErrorType.DATA_STRUCT_ALIAS_ILLEGAL.getMessage(), alias));
//        }
    }

    private void validateOrderNumberField(DataStructNode dataStructNode) {
        if (dataStructNode.getProps().getType() != DataStructType.PERSIST) {
            return;
        }

        List<String> fieldNames = dataStructNode.getChildren().stream().map(DataStructFieldNode::getKey).collect(Collectors.toList());
        if (dataStructNode.getProps().isOrderNumberEnabled()) {
            // 保存模型时，若排序字段开关开启，字段应包含 order_number 字段
            if (!fieldNames.contains(ORDER_NUMBER_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS, String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(), ORDER_NUMBER_FIELD),
                        new Object[]{ORDER_NUMBER_FIELD});
            }
        } else {
            // 保存模型时，若排序字段开关关闭，字段不应包含 order_number 字段
            if (fieldNames.contains(ORDER_NUMBER_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_INVALID_PARAM, String.format(ErrorType.MODEL_INVALID_PARAM.getMessage(), "order_number 为系统保留字段"),
                        new Object[]{"order_number 为系统保留字段"});
            }
        }
    }

    private void validateOriginOrgIdField(DataStructNode dataStructNode) {
        if (dataStructNode.getProps().getType() != DataStructType.PERSIST) {
            return;
        }

        List<String> fieldNames = dataStructNode.getChildren().stream().map(DataStructFieldNode::getKey).collect(Collectors.toList());
        if (dataStructNode.getProps().isOriginOrgIdEnabled()) {
            // 保存模型时，若多组织系统字段开关开启，字段应包含 origin_org_id 字段
            if (!fieldNames.contains(ORIGIN_ORG_ID_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS, String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(), ORIGIN_ORG_ID_FIELD),
                    new Object[]{ORIGIN_ORG_ID_FIELD});
            }
        } else {
            // 保存模型时，若多组织系统字段开关关闭，字段不应包含 origin_org_id 字段
            if (fieldNames.contains(ORIGIN_ORG_ID_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_INVALID_PARAM, String.format(ErrorType.MODEL_INVALID_PARAM.getMessage(), "origin_org_id 为系统保留字段"),
                    new Object[]{"origin_org_id 为系统保留字段"});
            }
        }
    }

    /**
     *
     * @param dataStructNode
     */
    private void validateTenantIdEnabled(DataStructNode dataStructNode) {
        if (dataStructNode.getId() == null || dataStructNode.getProps().getType() != DataStructType.PERSIST) {
            return;
        }

        if (dataStructNode.getId() != null) {
            // 模型更新时限制多租户开关只能从未开启改为开启，不能从开启改为未开启
            DataStructNode oldModel = cache.getModelMeta(dataStructNode.getTeamId(), null, dataStructNode.getKey());
            if (oldModel.getProps().isTenantIdEnabled() && !dataStructNode.getProps().isTenantIdEnabled()) {
                throw new ModelMetaException(
                        ErrorType.MODEL_INVALID_STATE,
                        String.format(ErrorType.MODEL_INVALID_PARAM.getMessage(), "模型开启多租户后不允许关闭"),
                        new Object[]{"模型开启多租户后不允许关闭"}
                );
            }
        }
    }

    private void validateTenantIdField(DataStructNode dataStructNode) {
        List<String> fieldNames = dataStructNode.getChildren().stream().map(DataStructFieldNode::getKey).collect(Collectors.toList());
        if (dataStructNode.getProps().isTenantIdEnabled()) {
            // 保存模型时，校验若模型开启多租户，字段应包含 tenant_id 字段
            if (!fieldNames.contains(TENANT_ID_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS, String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(), TENANT_ID_FIELD),
                        new Object[]{TENANT_ID_FIELD});
            }
        } else {
            // 保存模型时，校验若模型未开启多租户，字段不应包含 tenant_id 字段
            if (fieldNames.contains(TENANT_ID_FIELD)) {
                throw new ModelMetaException(ErrorType.MODEL_INVALID_PARAM, String.format(ErrorType.MODEL_INVALID_PARAM.getMessage(), "tenant_id 为系统保留字段"),
                        new Object[]{"tenant_id 为系统保留字段"});
            }
        }
    }

    private void validateFieldName(DataStructNode dataStructNode) {
        if (dataStructNode == null || CollectionUtils.isEmpty(dataStructNode.getChildren())) {
            return;
        }

        Pattern fieldPattern;
        if (StringUtils.isEmpty(modelManagementProperties.getModelRegex())) {
            // 若用户未指定正则表达式，采用系统默认校验规则
            fieldPattern = MODEL_FIELD_REGEX_PATTERN;
        } else {
            // 若用户指定正则表达式，采用用户校验规则
            fieldPattern = Pattern.compile(modelManagementProperties.getModelRegex());
        }

        dataStructNode.getChildren().forEach(fieldNode -> {
            // 统计模型放开 field key 校验，允许用户指定中文
            if (dataStructNode.getProps().getType() != DataStructType.STATISTIC && !matches(fieldPattern, fieldNode.getKey())) {
                throw new ModelMetaException(
                    ErrorType.DATA_STRUCT_FIELD_ALIAS_ILLEGAL,
                    String.format(ErrorType.DATA_STRUCT_FIELD_ALIAS_ILLEGAL.getMessage(), fieldNode.getAlias()),
                    new Object[]{fieldNode.getAlias()});
            }

            // DDL 的字段名称不可为系统关键字，否则根据字段名称生成 class 时编译失败
            if (SystemReserved.containsKeyword(fieldNode.getKey())) {
                log.error("fields of model: {} contains system reserved keyword: {}", dataStructNode.getKey(), fieldNode.getKey());
                throw new ModelMetaException(
                    ErrorType.DATA_STRUCT_FIELD_ALIAS_SYSTEM_KEYWORD,
                    String.format(ErrorType.DATA_STRUCT_FIELD_ALIAS_SYSTEM_KEYWORD.getMessage(), fieldNode.getKey()),
                    new Object[]{fieldNode.getKey()});
            }

            if (fieldNode.getProps().getRelationMeta() == null
                    && dataStructNode.getProps().getType() != DataStructType.STATISTIC
                    && !fieldNode.getKey().equalsIgnoreCase(fieldNode.getProps().getColumnName())) {
                throw new ModelMetaException(
                    ErrorType.MODEL_COLUMN_KEY_INCONSISTENT,
                    String.format(ErrorType.MODEL_COLUMN_KEY_INCONSISTENT.getMessage(), fieldNode.getKey()),
                    new Object[]{fieldNode.getKey()});
            }
        });
    }

    /**
     * 字段正则匹配校验
     *
     * @param pattern
     * @param needMatchStr
     * @return true:是；false:否
     */
    private static boolean matches(Pattern pattern, String needMatchStr) {
        Matcher matcher = pattern.matcher(needMatchStr);
        return matcher.matches();
    }

    private void validationRelationModelRequire(List<DataStructFieldNode> children, Long teamId) {
        List<String> relationMetaAlias = children.stream()
            .filter(field -> Objects.nonNull(field.getProps().getRelationMeta()))
            .map(field -> field.getProps().getRelationMeta())
            .map(relation -> relation.getRelationModelAlias()).collect(Collectors.toList());
        Map<String, DataStructNode> dataStructNodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(relationMetaAlias)) {
            QueryByAliasRequest aliasRequest = new QueryByAliasRequest();
            aliasRequest.setAliases(relationMetaAlias);
            aliasRequest.setTeamId(teamId);
            List<DataStructNode> dataStructNodes = queryByAlias(aliasRequest);
            dataStructNodeMap = dataStructNodes.stream()
                .collect(Collectors.toMap(DataStructNode::getAlias, Function.identity()));
        }
        // 校验字段类型构成参数
        for (DataStructFieldNode field : children) {
            if (Objects.nonNull(field.getProps().getRelationMeta())) {
                RelationMeta relationMeta = field.getProps().getRelationMeta();
                DataStructNode dataStructNode = dataStructNodeMap.get(relationMeta.getRelationModelAlias());
                if (Objects.isNull(dataStructNode)) {
                    throw new ModelMetaException(ErrorType.MODEL_FIELD_CONFIG_DEFINE_ERROR,
                        String.format(ErrorType.MODEL_FIELD_CONFIG_DEFINE_ERROR.getMessage(),
                            relationMeta.getCurrentModelFieldAlias(), relationMeta.getRelationType(),
                            "模型关联字段类型中定义的关联模型不存在"),
                        new Object[]{relationMeta.getCurrentModelFieldAlias(), relationMeta.getRelationType(),
                            "模型关联字段类型中定义的关联模型不存在"});
                } else if (relationMeta.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD && dataStructNode.isSetting()) {
                    // 业务模型只能引用业务配置，不可与业务配置存在主子关系
                    throw new BusinessException("不可一对多关联业务配置");
                } else if (relationMeta.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD
                        && !relationMeta.isSync()
                        && dataStructNode.getProps().isRelationModel()) {
                    throw new ModelMetaException(
                            ErrorType.MODEL_INVALID_STATE,
                            String.format(ErrorType.MODEL_INVALID_STATE.getMessage(), "不可引用多条关联关系模型"),
                            new Object[] {"不可引用多条关联关系模型"});
                }
            }
        }
    }

    private void validateModelI18nEnabled(DataStructNode dataStructNode) {
        if (!dataStructNode.getProps().isI18nEnabled()) {
            return;
        }

        // 模型开启国际化时至少有一个字段需开启国际化
        if (dataStructNode.getChildren().stream().noneMatch(field -> field.getProps().isI18nEnabled())) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE,
                String.format(ErrorType.MODEL_INVALID_STATE.getMessage(), "模型开启国际化时至少有一个字段需开启国际化"),
                new Object[]{"模型开启国际化时至少有一个字段需开启国际化"});
        }
    }

    /**
     * 校验模型内是否存在同名字段
     *
     * @param children
     */
    private void validationModelFieldUnique(List<DataStructFieldNode> children) {
        if (CollectionUtils.isEmpty(children)) {
            // 不存在字段为空的模型
            throw new ModelMetaException(ErrorType.MODEL_MAIN_FIELD_SIZE_ERROR,
                String.format(ErrorType.MODEL_MAIN_FIELD_SIZE_ERROR.getMessage(), 0),
                new Object[]{0});
        }

        Map<String, List<DataStructFieldNode>> fieldMap = children.stream()
            .collect(Collectors.groupingBy(DataStructFieldNode::getKey));
        if (fieldMap.size() != children.size()) {
            fieldMap.forEach((fieldKey, fieldNodes) -> {
                if (!CollectionUtils.isEmpty(fieldNodes) && fieldNodes.size() > 1) {
                    throw new ModelMetaException(
                        ErrorType.MODEL_FIELD_ALIAS_NOT_UNIQUE,
                        String.format(ErrorType.MODEL_FIELD_ALIAS_NOT_UNIQUE.getMessage(), fieldKey),
                        new Object[]{fieldKey});
                }
            });
        }
    }

    private void validationAliasUnique(DataStructNode request) {
        DataStructNode exist = dataStructNodeRepo
            .findByAppIdAndTeamIdAndAlias(request.getAppId(), request.getTeamId(), request.getAlias());
        if (exist != null) {
            throw new ModelMetaException(ErrorType.MODEL_ALREADY_EXIST_ERROR,
                String.format(ErrorType.MODEL_ALREADY_EXIST_ERROR.getMessage(), request.getAlias()),
                new Object[]{request.getAlias()});
        }
    }

    /**
     * 校验模型的 table_name 是否为 sql 关键字
     * @param modelMeta
     */
    private void validateSqlKeyword(DataStructNode modelMeta) {
        if (SqlKeywordUtil.containsKeyword(modelMeta.getProps().getTableName())) {
            log.error("model: {} can't be reserved sql keyword", modelMeta.getProps().getTableName());
            throw new ModelMetaException(
                ErrorType.MODEL_TABLE_NAME_IS_SQL,
                String.format(ErrorType.MODEL_TABLE_NAME_IS_SQL.getMessage(),  modelMeta.getProps().getTableName()),
                new Object[]{modelMeta.getProps().getTableName()});
        }
    }

    private void cascadeDeleteRelation(DataStructNode dataStructNode) {
        DataStructType type = dataStructNode.getProps().getType();
        if (type != DataStructType.PERSIST) {
            return;
        }
        // 获取旧版本定义
        DataStructNode oldDefinition = cache.getModelMeta(dataStructNode.getTeamId(), dataStructNode.getAppId(), dataStructNode.getAlias());
        if (oldDefinition == null) {
            return;
        }
        List<DataStructFieldNode> oneToMany = oldDefinition.getChildren().stream().filter(it -> {
            RelationMeta relationMeta = it.getProps().getRelationMeta();
            return relationMeta != null && relationMeta.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD;
        }).collect(Collectors.toList());
        Map<String, RelationMeta> relationMetaMap = dataStructNode.getChildren().stream().filter(it -> {
            RelationMeta relationMeta = it.getProps().getRelationMeta();
            return relationMeta != null && relationMeta.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD;
        }).collect(Collectors.toMap(DataStructFieldNode::getAlias, it -> it.getProps().getRelationMeta()));
        for (DataStructFieldNode relationField : oneToMany) {
            String alias = relationField.getAlias();
            RelationMeta relationMeta = relationMetaMap.get(alias);
            RelationMeta oldRelationMeta = relationField.getProps().getRelationMeta();
            if (relationMeta == null) {
                // 一对多字段被删除了
                tryDeleteOneToOneField(dataStructNode, oldRelationMeta);
            }
        }
    }

    private void tryDeleteOneToOneField(DataStructNode dataStructNode, RelationMeta oldRelationMeta) {
        String relationModelAlias = oldRelationMeta.getRelationModelAlias();
        DataStructNode relatedModelDefinition = dataStructNodeRepo.findByAppIdAndTeamIdAndAlias(dataStructNode.getAppId(), dataStructNode.getTeamId(), relationModelAlias);
        if (relatedModelDefinition == null) {
            return;
        }
        List<DataStructFieldNode> children = relatedModelDefinition.getChildren();
        Optional<DataStructFieldNode> oneToOneField = children.stream().filter(it -> it.getAlias().equals(oldRelationMeta.getLinkModelFieldAlias())).findFirst();
        if (oneToOneField.isPresent() && oneToOneField.get().getProps().isAutoGenerated()) {
            // 删除自动创建的一对一字段
            children.remove(oneToOneField.get());
            ModuleDataSourceDTO config = dataSourceConfigService.queryModuleDataSourceConfig(dataStructNode.getTeamId(), KeyUtil.moduleKey(relationModelAlias));
            if (config != null) {
                ddlGenerator.update(relatedModelDefinition, false, config.getAutoDDL());
            }
            dataStructNodeRepo.save(relatedModelDefinition);
        }
    }

    private void validationUpdateParams(DataStructNode updateStructNodeRequest) {
        // 校验模型名称
        validateModelAlias(updateStructNodeRequest.getAlias());

        // 模型为关联模型时，校验关联模型参数合法性
        validateRelationModelParamsWhenCreating(updateStructNodeRequest);

        // 校验字段名称
        validateFieldName(updateStructNodeRequest);

        // 校验模型别名唯一
        validationUpdateAliasUnique(updateStructNodeRequest);

        // 校验单表字段名称唯一
        validationModelFieldUnique(updateStructNodeRequest.getChildren());

        // 校验关联模型必须存在
        validationRelationModelRequire(updateStructNodeRequest.getChildren(), updateStructNodeRequest.getTeamId());

        // 校验依赖模型同步方式唯一性
        validationDependModelSyncUnique(updateStructNodeRequest.getKey(), updateStructNodeRequest.getChildren());

        // 校验关联关系是否变更
        validateRelation(updateStructNodeRequest);
    }

    private void validateRelationModelParamsWhenCreating(DataStructNode dataStructNode) {
        if (dataStructNode.getId() != null || !dataStructNode.getProps().isRelationModel()) {
            return;
        }

        if (CollectionUtils.isEmpty(dataStructNode.getProps().getRelationModelKeys())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, "relationModelKeys", new Object[]{"relationModelKeys"});
        }
        // 关系模型只能声明两个模型的关联
        if (dataStructNode.getProps().getRelationModelKeys().size() != 2) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_PARAM, "relationModelKeys", new Object[]{"relationModelKeys"});
        }

        if (CollectionUtils.isEmpty(dataStructNode.getChildren())) {
            return;
        }

        // 新增关系模型时，关系模型的两个对象字段由后端生成，前端传过来的参数字段列表中不允许添加对象字段
        if(dataStructNode.getChildren()
                .stream()
                .filter(it -> BooleanUtils.isFalse(it.getProps().getIsSystemField()))
                .anyMatch(it -> it.getProps().getRelationMeta() != null)) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_PARAM, "不可添加对象字段", new Object[]{"不可添加对象字段"});
        }
    }

    /**
     * 模型关联关系不支持变更， 存在两种场景：
     * 1. 关联对象变更，如: 字段从对象类型 A 改为对象类型 B
     * 2. 同步方式变更, 如: 字段的关联对象关系从一对一同步改为一对一不同步等
     *
     * @param dataStructNode 待更新模型元数据
     */
    private void validateRelation(DataStructNode dataStructNode) {
        if (dataStructNode.getProps().getType() != DataStructType.PERSIST || dataStructNode.getId() == null) {
            return;
        }

        // 获取旧版本定义
        DataStructNode oldMetaInfo = cache.getModelMeta(dataStructNode.getTeamId(), dataStructNode.getAppId(), dataStructNode.getAlias());
        if (oldMetaInfo == null) {
            return;
        }

        // 老关联关系
        List<DataStructFieldNode> oldRelationNodes = oldMetaInfo.getChildren()
            .stream()
            .filter(it -> {
                RelationMeta relationMeta = it.getProps().getRelationMeta();
                return relationMeta != null;
            }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldRelationNodes)) {
            return;
        }

        // 新关联关系
        Map<String, RelationMeta> relationMap = dataStructNode.getChildren()
            .stream()
            .filter(it -> {
                RelationMeta relationMeta = it.getProps().getRelationMeta();
                return relationMeta != null;
            })
            .collect(Collectors.toMap(DataStructFieldNode::getAlias, it -> it.getProps().getRelationMeta()));

        oldRelationNodes.forEach(oldRelationNode -> {
            RelationMeta relationMeta = relationMap.get(oldRelationNode.getAlias());
            if (relationMeta == null) {
                // 老的关联关系删除情况下，忽略
                return;
            }
            RelationMeta oldRelationMeta = oldRelationNode.getProps().getRelationMeta();
            // 关联对象不可变更
            if (!oldRelationMeta.getRelationModelAlias().equals(relationMeta.getRelationModelAlias())) {
                throw new ModelMetaException(ErrorType.MODEL_RELATION_CANNOT_CHANGE,
                    String.format(ErrorType.MODEL_RELATION_CANNOT_CHANGE.getMessage(), relationMeta.getCurrentModelFieldAlias()),
                    new Object[]{relationMeta.getCurrentModelFieldAlias()});
            }

            // 关联对象关联关系类型不可变更，如：一对一改为一对多
            if (oldRelationMeta.getRelationType() != relationMeta.getRelationType()) {
                throw new ModelMetaException(ErrorType.MODEL_RELATION_CANNOT_CHANGE,
                    String.format(ErrorType.MODEL_RELATION_CANNOT_CHANGE.getMessage(), relationMeta.getCurrentModelFieldAlias()),
                    new Object[]{relationMeta.getCurrentModelFieldAlias()});
            }

            // 关联对象的同步方式不可变更，如: 一对一同步改为一对一不同步
            if (oldRelationMeta.isSync() != relationMeta.isSync()) {
                throw new ModelMetaException(ErrorType.MODEL_RELATION_CANNOT_CHANGE,
                    String.format(ErrorType.MODEL_RELATION_CANNOT_CHANGE.getMessage(), relationMeta.getCurrentModelFieldAlias()),
                    new Object[]{relationMeta.getCurrentModelFieldAlias()});
            }
        });
    }

    private void validationRelationModelUnique(DataStructNode dataStructNode, List<DataStructFieldNode> children) {
        if (DataStructType.PERSIST.equals(dataStructNode.getProps().getType())) {
            List<String> relationNames = children.stream()
                .filter(item -> Objects.nonNull(item.getProps().getRelationMeta()) &&
                    StringUtils.isNotEmpty(item.getProps()
                        .getRelationMeta()
                        .getRelationModelAlias()) &&
                    ModelRelationTypeEnum.PARENT_CHILD.equals(item.getProps()
                        .getRelationMeta()
                        .getRelationType()))
                .map(item -> item.getProps().getRelationMeta().getRelationModelAlias())
                .collect(Collectors.toList());
            // 如果有出现重复的模型名则经过去重之后数量一定会变化
            if (relationNames.size() != relationNames.stream().distinct().count()) {
                throw new ModelMetaException(ErrorType.PERSIST_MODEL_RELATION_MODEL_DUPLICATE,
                    ErrorType.PERSIST_MODEL_RELATION_MODEL_DUPLICATE.getMessage());
            }
        }
    }

    /**
     * 校验模型向量数据库属性合法性
     * @param request
     */
    private void validateVectorDatabaseEnabled(DataStructNode request) {
        if (request.getProps().getType() != DataStructType.PERSIST && request.getProps().isVectorDatabaseEnabled()) {
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, "仅持久模型支持开启向量库", new Object[]{"仅持久模型支持开启向量库"});
        }
    }

    private void validationDependModelSyncUnique(String currModuleKey, List<DataStructFieldNode> children) {
        List<DataStructFieldNode> fieldsStream = children.stream()
            .filter(item -> Objects.nonNull(item.getProps().getRelationMeta()) &&
                !Objects.equals(KeyUtil.moduleKey(item.getProps()
                        .getRelationMeta()
                        .getRelationModelAlias()),
                    KeyUtil.moduleKey(currModuleKey)))
            .collect(Collectors.toList());
        // 判断是否有模块的依赖
        if (!fieldsStream.isEmpty()) {
            // 一对一同步、一对多同步的字段名
            List<String> columnNames = fieldsStream.stream()
                .filter(item -> item.getProps().getRelationMeta().isSync())
                .map(DataStructFieldNode::getKey)
                .collect(Collectors.toList());
            if (!columnNames.isEmpty()) {
                throw new ModelMetaException(ErrorType.MODEL_RELATION_ERROR,
                    String.format("关联关系错误: %s，请修改[%s]字段的关联关系", "关联模型是跨模块的模型时，其关联关系不能选择一对一同步、一对多同步", StringUtils.join(columnNames, ",")),
                    new Object[]{"关联模型是跨模块的模型时，其关联关系不能选择一对一同步、一对多同步", StringUtils.join(columnNames, ",")});
            }
        }
    }

    /**
     * 系统模型不允许更新，校验当前操作的模型是否为系统模型
     *
     * @param key
     */
    private void validateSystemModel(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }

        if (isSystemModel(key)) {
            throw new ModelMetaException(ErrorType.SYSTEM_MODEL_CAN_NOT_CHANGE,
                ErrorType.SYSTEM_MODEL_CAN_NOT_CHANGE.getMessage());
        }
    }

    private void validationUpdateAliasUnique(DataStructNode request) {
        DataStructNode exist = dataStructNodeRepo
            .findByAppIdAndTeamIdAndAlias(request.getAppId(), request.getTeamId(), request.getAlias());
        if (exist != null && !exist.getKey().equals(request.getKey())) {
            throw new ModelMetaException(ErrorType.MODEL_ALREADY_EXIST_ERROR,
                String.format(ErrorType.MODEL_ALREADY_EXIST_ERROR.getMessage(), request.getAlias()),
                new Object[]{request.getAlias()});
        }
    }


    public DataStructNode findById(Long id) {
        Optional<DataStructNode> dataStructNodeOpt = dataStructNodeRepo.findById(id);
        return dataStructNodeOpt.get();
    }

    public Schema findSchemaByKey(String modelKey) {
        DataStructNode dataStructNode = findModel(TrantorContext.getTeamId(), modelKey);
        if (dataStructNode == null) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS,
                    String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), modelKey), new Object[]{modelKey});
        }

        TableStructure tableStructure = ddlGenerator.getTableStructure(dataStructNode);
        if (tableStructure == null) {
            log.info("model: {} table schema does not exist", modelKey);
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS,
                    String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), modelKey), new Object[]{modelKey});
        }

        List<Column> columns = new ArrayList<>();
        if (MapUtils.isNotEmpty(tableStructure.getColumns())) {
            tableStructure.getColumns().values().forEach(item -> {
                Column column = new Column();
                column.setColumnName(item.getColumnName());
                column.setColumnType(item.getType());
                column.setColumnLength(item.getOneLength());
                column.setNonNull(item.isNonNull());
                column.setDefaultValue(item.getDefaultValue());
                column.setColumnComment(item.getComment());
                if (column.getColumnName().equals("id")) {
                    column.setAutoIncrement(true);
                }

                columns.add(column);
            });
        }

        List<Index> indexes = new ArrayList<>();
        if (MapUtils.isNotEmpty(tableStructure.getIndexes())) {
            tableStructure.getIndexes().values().forEach(item -> {
                Index index = new Index();
                index.setName(item.getName());
                if (item.getType() == IndexTypeEnum.PRIMARY || item.getType() == IndexTypeEnum.UNIQUE) {
                    index.setNonUnique(false);
                } else {
                    index.setNonUnique(true);
                }
                index.setColumns(item.getColumns());
                indexes.add(index);
            });
        }

        Schema schema = new Schema();
        schema.setTableName(tableStructure.getTableName());
        schema.setComment(tableStructure.getComment());
        schema.setColumns(columns);
        schema.setIndexes(indexes);

        return schema;
    }

    public void saveIndex(SaveIndexRequest saveIndexRequest) {
        assert saveIndexRequest != null;
        if (StringUtils.isEmpty(saveIndexRequest.getModelKey())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "modelKey"), new Object[]{"modelKey"});
        }
        if (CollectionUtils.isEmpty(saveIndexRequest.getIndexes())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "indexes"), new Object[]{"indexes"});
        }

        DataStructNode dataStructNode = findModel(TrantorContext.getTeamId(), saveIndexRequest.getModelKey());
        if (dataStructNode == null) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS,
                    String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), saveIndexRequest.getModelKey()), new Object[]{saveIndexRequest.getModelKey()});
        }

        ddlGenerator.saveIndex(saveIndexRequest, dataStructNode);
    }

    public void deleteIndex(DeleteIndexRequest deleteIndexRequest) {
        if (StringUtils.isEmpty(deleteIndexRequest.getModelKey())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "modelKey"), new Object[]{"modelKey"});
        }
        if (CollectionUtils.isEmpty(deleteIndexRequest.getIndexNames())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "indexNames"), new Object[]{"indexNames"});
        }

        DataStructNode dataStructNode = findModel(TrantorContext.getTeamId(), deleteIndexRequest.getModelKey());
        if (dataStructNode == null) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS,
                    String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(), deleteIndexRequest.getModelKey()), new Object[]{deleteIndexRequest.getModelKey()});
        }

        ddlGenerator.deleteIndex(deleteIndexRequest, dataStructNode);
    }

    public <T extends Serializable> DataStructNode queryByKey(String key, T team) {
        return Optional.ofNullable(cache.queryByKeys(team, Collections.singleton(key)))
            .flatMap(nodes -> nodes.stream().findFirst())
            .orElse(null);
    }

    /**
     * 获取缓存中的数据元信息
     */
    public List<DataStructNode> queryByAlias(QueryByAliasRequest request) {
        return cache.queryByKeys(request.getTeamId(), request.getAliases());
    }

    public List<DataStructNode> findByAlias(QueryByAliasRequest request) {
        checkFindByAliasParam(request);

        List<DataStructNode> result = new ArrayList<>();
        Set<String> set = new HashSet<>(request.getAliases());
        set.stream()
                .filter(systemModelInstanceHolder::isSystemModel)
                .forEach(alias -> result.add(systemModelInstanceHolder.getByAlias(alias)));
        Set<String> modelKeys = set.stream()
                .filter(alias -> !systemModelInstanceHolder.isSystemModel(alias))
                .collect(Collectors.toSet());
        result.addAll(dataStructNodeRepo.findAllByKeys(request.getTeamId(), new ArrayList<>(modelKeys)));
        return result;
    }

    private void checkFindByAliasParam(QueryByAliasRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getAliases())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "aliases"), new Object[]{"aliases"});
        }

        if (request.getTeamId() == null) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM,
                    String.format(ErrorType.MODEL_MISSING_PARAM.getMessage(), "teamId"), new Object[]{"teamId"});
        }
    }

    /**
     * 获取缓存中的数据元信息
     */
    public Collection<DataStructNode> queryByKeys(QueryByKeysRequest<?> request) {
        return cache.queryByKeys(request.getTeam(), request.getKeys());
    }


    public Boolean deleteCache() {
        cache.batchRemoveDataStructCache();
        return Boolean.TRUE;
    }

    private boolean isSystemModel(String key) {
        return systemModelInstanceHolder.isSystemModel(key);
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    /**
     * 更新搜索模型搜索相关配置信息
     *
     * @param request request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSearchModel(SaveStructNodeRequest request) {
        DataStructNode dataStructNodeInDb = findById(request.getId());
        if (null == dataStructNodeInDb) {
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS,
                String.format(ErrorType.MODEL_NOT_EXISTS.getMessage(),
                    "request:" + request), new Object[]{"request:" + request});
        }

        // 判断是否需要删除搜索模型
        boolean needDeleteSearchModel = checkNeedDeleteSearchModel(dataStructNodeInDb, request);

        // 只更新数据库中对应的搜索模型相关信息
        mergeSearchInfo(dataStructNodeInDb, request);

        // 必填参数校验
        paramCheck(dataStructNodeInDb);

        // 索引结构合法性校验
        searchModelDumpService.indexCheck(dataStructNodeInDb);

        // 模型元信息保存
        doSave(dataStructNodeInDb);

        // 搜索模型更新
        searchModelDumpService.doSaveSearchModel(dataStructNodeInDb,
            needDeleteSearchModel ? SearchModelSyncTypeEnum.DELETE : SearchModelSyncTypeEnum.UPDATE);
    }

    /**
     * 校验是否需要删除改搜索模型
     * 判断依据：数据库中为搜索模型，请求入参为非搜索模型
     *
     * @param dataStructNodeInDb
     * @param request
     * @return
     */
    private boolean checkNeedDeleteSearchModel(DataStructNode dataStructNodeInDb, SaveStructNodeRequest request) {
        return dataStructNodeInDb.getProps().isSearchModel() && !request.getProps().isSearchModel();
    }

    /**
     * 搜索模型必填参数校验
     *
     * @param dataStructNodeInDb
     */
    private void paramCheck(DataStructNode dataStructNodeInDb) {
        boolean isSearchModel = dataStructNodeInDb.getProps().isSearchModel();
        // 非持久化搜索模型要求SearchModelConfigMeta.persistModel2TopicNameMap必填
        if (isSearchModel
            && !Objects.equals(dataStructNodeInDb.getProps().getType(), DataStructType.PERSIST)
            && MapUtils.isEmpty(dataStructNodeInDb.getProps().getSearchModelConfigMeta().getPersistModel2TopicNameMap())) {
            throw new ModelMetaException(ErrorType.MODEL_FIELD_NOT_EXISTS,
                String.format(ErrorType.MODEL_FIELD_NOT_EXISTS.getMessage(),
                    "SearchModelConfigMeta.persistModel2TopicNameMap"), new Object[]{"SearchModelConfigMeta.persistModel2TopicNameMap"});
        }
    }

    private void mergeSearchInfo(DataStructNode dataStructNodeInDb, SaveStructNodeRequest request) {
        mergeSearchPropsInfo(dataStructNodeInDb, request.getProps());
        mergeSearchFieldPropsInfo(dataStructNodeInDb, request.getChildren());
    }

    private void mergeSearchFieldPropsInfo(DataStructNode dataStructNodeInDb, List<DataStructFieldNode> children) {
        if (null == children) {
            return;
        }

        boolean isSearchModel = dataStructNodeInDb.getProps().isSearchModel();

        Map<String, DataStructFieldNode> fieldNodeMap = dataStructNodeInDb.getChildren().stream()
            .collect(Collectors.toMap(DataStructFieldNode::getAlias, Function.identity()));
        children.forEach(field -> {
            DataStructFieldProperties fProps = field.getProps();
            SearchModelFieldConfigMeta searchModelFieldConfigMeta = fProps.getSearchModelFieldConfigMeta();
            if (null == searchModelFieldConfigMeta && isSearchModel) {
                searchModelFieldConfigMeta = new SearchModelFieldConfigMeta();
            }
            if (null != searchModelFieldConfigMeta) {
                searchModelFieldConfigMeta.setSearchFieldType(getSearchFieldType(fProps, searchModelFieldConfigMeta.getMapping()));
                fieldNodeMap.get(field.getAlias()).getProps().setSearchModelFieldConfigMeta(searchModelFieldConfigMeta);
            }
        });

        // 针对request中没有入参的字段，补充默认搜索配置信息
        fieldNodeMap.forEach((key, fieldNode) -> {
            if (null != fieldNode.getProps().getSearchModelFieldConfigMeta()) {
                return;
            }
            SearchModelFieldConfigMeta searchModelFieldConfigMeta = new SearchModelFieldConfigMeta();
            fieldNode.getProps().setSearchModelFieldConfigMeta(searchModelFieldConfigMeta);
        });
    }

    private SearchFieldType getSearchFieldType(DataStructFieldProperties fProps, JSONObject userDefineMapping) {
        if (null != userDefineMapping && !userDefineMapping.isEmpty()) {
            return SearchFieldType.USER_DEFINE;
        }
        return fProps.getFieldType().getSearchFieldType(fProps.getLength(),
            fProps.getIntLength(), fProps.getScale());
    }

    private void mergeSearchPropsInfo(DataStructNode dataStructNodeInDb, DataStructProperties props) {
        if (null == props) {
            return;
        }
        boolean isSearchModel = props.isSearchModel();
        dataStructNodeInDb.getProps().setSearchModel(isSearchModel);

        SearchModelConfigMeta searchModelConfigMeta = props.getSearchModelConfigMeta();
        if (null == searchModelConfigMeta && isSearchModel) {
            searchModelConfigMeta = new SearchModelConfigMeta();
        }
        if (null != searchModelConfigMeta) {
            String searchAlias = searchModelConfigMeta.generateAlias(dataStructNodeInDb.getTeamCode(),
                dataStructNodeInDb.getAlias());
            searchModelConfigMeta.setAlias(searchAlias);
            // 当入参为空时，可以保留原来的搜索结构，方便切换，通过props.isSearchModel()来关闭搜索
            dataStructNodeInDb.getProps().setSearchModelConfigMeta(searchModelConfigMeta);
        }
    }

    /**
     * 触发搜索模型全量同步
     *
     * @param idRequest id
     */
    public void fullSyncSearchModel(SearchFullSyncRequest idRequest) {
        DataStructNode dataStructNodeInDb = findById(idRequest.getId());
        if (null == dataStructNodeInDb) {
            String errMsg = "id:" + idRequest + "can not find model info";
            throw new ModelMetaException(ErrorType.MODEL_NOT_EXISTS, new Object[]{ errMsg });
        }
        searchModelDumpService.doSaveSearchModel(dataStructNodeInDb, SearchModelSyncTypeEnum.FULL, idRequest.getReason());
    }

    public DataStructNode querySearchModelConfig(IdRequest idRequest) {
        DataStructNode dataStructNodeInDb = findById(idRequest.getId());
        if (null == dataStructNodeInDb) {
            String errMsg = "id:" + idRequest + "can not find model info";
            throw new ModelMetaException(ErrorType.SERVER_ERROR, new Object[]{ errMsg });
        }
        boolean isSearchModelInDb = dataStructNodeInDb.getProps().isSearchModel();

        SaveStructNodeRequest request = new SaveStructNodeRequest();
        request.setProps(dataStructNodeInDb.getProps());
        request.setChildren(dataStructNodeInDb.getChildren());
        request.getProps().setSearchModel(true);
        mergeSearchInfo(dataStructNodeInDb, request);

        dataStructNodeInDb.getProps().setSearchModel(isSearchModelInDb);
        return dataStructNodeInDb;
    }

    public List<SearchModelSyncTaskDto> querySearchModelSyncTasks(SyncTaskQueryRequest syncTaskQueryRequest) {
        return searchModelDumpService.querySearchModelSyncTasks(syncTaskQueryRequest);
    }

    public Paging<ModelShardingConfigDTO> queryModelShardingConfig(Long teamId, String teamCode, String moduleKey, int pageNo, int pageSize) {

        Map<String, ModelShardingConfig> configMap = configurationService.queryAll(teamId, ConfigType.Model_Sharding_Config);
        Paging<DataStructNode> page = dataStructNodeRepo.findAllShardingModelByTeamCodeAndModuleKey(teamCode, moduleKey, pageNo, pageSize);
        List<ModelShardingConfigDTO> collect = page.getData().stream().map(model -> {
            ModelShardingConfigDTO shardingConfigDTO = new ModelShardingConfigDTO();
            shardingConfigDTO.setModelKey(model.getKey());
            shardingConfigDTO.setModelName(model.getName());
            shardingConfigDTO.setShardingEnable(Optional.ofNullable(configMap.get(model.getKey())).map(ModelShardingConfig::getShardingEnable).orElse(null));
            shardingConfigDTO.setShardingNum(Optional.ofNullable(configMap.get(model.getKey())).map(ModelShardingConfig::getShardingNum).orElse(null));
            return shardingConfigDTO;
        }).collect(Collectors.toList());

        return new Paging<>(page.getTotal(), collect);

    }


    public void updateModelShardingConfig(Long teamId, String teamCode, ModelShardingConfigDTO request) {
        request.check();
        ModelShardingConfig config = new ModelShardingConfig();
        config.setShardingEnable(request.getShardingEnable());
        config.setShardingNum(request.getShardingNum());

        configurationService.save(config, teamId, request.getModelKey(), ConfigType.Model_Sharding_Config);
        ModuleDatasourceConfig moduleDatasourceConfig = configurationService.query(teamId, KeyUtil.moduleKey(request.getModelKey()), ConfigType.Module_Datasource);

        DataStructNode dataStruct = dataStructNodeRepo.findByAppIdAndTeamIdAndAlias(null, teamId, request.getModelKey());

        ddlGenerator.update(dataStruct, true, moduleDatasourceConfig == null || moduleDatasourceConfig.getAutoDDL());

        metaEventPublisher.publish(ModelShardingConfigUpdateEvent.of(teamId, teamCode, request.getModelKey(), request.getShardingEnable(), request.getShardingNum()));
    }


    public void updateModelShardingConfigBatch(Long teamId, String teamCode, List<ModelShardingConfigDTO> request) {
        request.forEach(dto -> updateModelShardingConfig(teamId, teamCode, dto));
    }

    /**
     * 根据 table schema 反向生成模型
     * @param request
     */
    public TaskOutputResult batchCreate(ModelBatchCreateRequest request) {
        // 1. 根据 module 获取数据源 2. 遍历 table schema 列表，获取 table schema 信息 3. 根据 schema 信息反向生成模型元数据 4. 持久化模型元数据
        checkBatchCreateParam(request);

        List<TaskOutputResult.ResultDetail> failDetails = new ArrayList<>();
        request.getModels().forEach(item -> {
            TableStructure tableStructure = ddlGenerator.getTableStructure(request.getTeamId(), AliasUtil.moduleKey(item.getModelKey()), item.getTableName());
            if (tableStructure == null) {
                log.error("failed to fetch table info for table: {}", item.getTableName());
                return;
            }

            DataStructNode exist = this.findModel(request.getTeamId(), item.getModelKey());
            if (exist != null) {
                log.info("model {} already exists", item.getModelKey());
                failDetails.add(TaskOutputResult.ResultDetail.failed(item.getModelKey(), "模型已存在"));
                return;
            }

            // 根据 table schema 构建模型元数据数据结构
            DataStructNode dataStructNode = buildModelMeta(request, item, tableStructure);

            // 校验表中是否存在必须的系统字段
            boolean systemFieldMissing = false;
            Map<String, DataStructFieldNode> fieldMap = dataStructNode.toFieldMap();
            List<String> systemFields = Arrays.asList("id", "createdBy", "updatedBy", "createdAt", "updatedAt", "version", "deleted", "originOrgId", "tenantId");
            List<String> missingFields = new ArrayList<>();
            for (String field : systemFields) {
                if (!item.isOriginOrgIdEnabled() && field.equals("originOrgId")) {
                    // 若模型未开启多组织过滤，忽略检查 originOrgId 字段
                    continue;
                }

                if (!item.isTenantIdEnabled() && field.equals("tenantId")) {
                    // 若模型未开启多租户过滤，忽略检查 tenantId 字段
                    continue;
                }

                if (!fieldMap.containsKey(field)) {
                    missingFields.add("缺少系统字段: " + field);
                    systemFieldMissing = true;
                }
            }

            if (systemFieldMissing) {
                // 若系统字段缺失, 去除最后一个多余的逗号
                failDetails.add(TaskOutputResult.ResultDetail.failed(item.getModelKey(), missingFields));

                return;
            }

            dataStructNodeRepo.save(dataStructNode);
        });

        TaskOutputResult result = new TaskOutputResult();

        TaskOutputResult.ResultSummary summary = new TaskOutputResult.ResultSummary();
        summary.setTotal(request.getModels().size());
        summary.setFailed(failDetails.size());
        summary.setSuccess(request.getModels().size() - failDetails.size());
        result.setSummary(summary);

        result.setDetails(failDetails);

        return result;
    }

    public <T extends Serializable> Collection<SimpleDataStructNode> listSimpleByKeys(SimpleModelInfoRequest<T> request) {
        String teamCode;
        if (NumberUtil.isLong(request.getTeam().toString())) {
            teamCode = teamService.getTeamCode(Long.valueOf(request.getTeam().toString()));
        } else {
            teamCode = request.getTeam().toString();
        }
        Collection<DataStructNode> models = dataStructNodeRepo.findAll(teamCode, request.getKeywords(), request.isExactMatch());
        List<SimpleDataStructNode> simpleModels = new ArrayList<>();
        if (CollectionUtils.isEmpty(models)) {
            return simpleModels;
        }

        for (DataStructNode model : models) {
            SimpleDataStructNode simpleModel = convertToSimpleDataStructNode(model, request.isIncludeSystemFields());

            // 递归填充关联模型元数据
            List<SimpleDataStructNode> relationModels = new ArrayList<>();
            Map<String, SimpleDataStructNode> queriedModelMap = new HashMap<>();
            queriedModelMap.put(model.getKey(), simpleModel);
            recursiveFillRelationModel(teamCode, simpleModel, relationModels, queriedModelMap, request.isIncludeSystemFields(), request.getCascadeDepth() - 1);
            simpleModel.setRelationModels(relationModels);

            simpleModels.add(simpleModel);
        }

        return simpleModels;
    }

    private SimpleDataStructNode convertToSimpleDataStructNode(DataStructNode model, boolean includeSystemFields) {
        SimpleDataStructNode simpleModel = new SimpleDataStructNode();
        simpleModel.setKey(model.getKey());
        simpleModel.setName(model.getName());
        simpleModel.setMainField(model.getProps().getMainFieldAlias());

        List<SimpleDataStructNode.SimpleDataStructFieldNode> simpleFields = new ArrayList<>();
        for (DataStructFieldNode field : model.getChildren()) {
            if (!includeSystemFields && BooleanUtils.isTrue(field.getProps().getIsSystemField())) {
                // 不包含系统字段时，忽略系统字段
                continue;
            }

            SimpleDataStructNode.SimpleDataStructFieldNode simpleField = getSimpleDataStructFieldNode(model.getTeamCode(), field);
            simpleFields.add(simpleField);
        }
        simpleModel.setFields(simpleFields);

        return simpleModel;
    }

    private void recursiveFillRelationModel(String teamCode, SimpleDataStructNode parentModel, List<SimpleDataStructNode> childrenModels, Map<String, SimpleDataStructNode> queriedModelMap, boolean includeSystemFields, int depth) {
        if (parentModel == null || depth <= 0) {
            return;
        }

        List<SimpleDataStructNode.SimpleDataStructFieldNode> fields = parentModel.getFields();
        if (CollectionUtils.isEmpty(fields)) {
            return;
        }

        // 获取当前模型的关联模型 alias 列表
        Set<String> relationModelKeys = new HashSet<>();
        List<SimpleDataStructNode> queriedChildren = new ArrayList<>();
        fields.forEach(field -> {
            if (StringUtils.isEmpty(field.getRelKey())) {
                return;
            }

            // 关联模型已经获取过元数据，不再重复获取，防止自关联时的无限递归
            if (queriedModelMap.containsKey(field.getRelKey())) {
                SimpleDataStructNode copied = new SimpleDataStructNode();
                SimpleDataStructNode queried = queriedModelMap.get(field.getRelKey());
                copied.setKey(queried.getKey());
                copied.setName(queried.getName());
                copied.setMainField(queried.getMainField());
                copied.setFields(queried.getFields());
                queriedChildren.add(copied);

                return;
            }

            relationModelKeys.add(field.getRelKey());
        });

        if (CollectionUtils.isNotEmpty(relationModelKeys)) {
            QueryByKeysRequest<String> queryByKeysRequest = new QueryByKeysRequest<>();
            queryByKeysRequest.setTeam(teamCode);
            queryByKeysRequest.setKeys(new ArrayList<>(relationModelKeys));
            // 获取当前模型的关联模型元信息
            Collection<DataStructNode> relationModels = this.queryByKeys(queryByKeysRequest);
            List<SimpleDataStructNode> simpleRelationModels = relationModels.stream()
                    .map(it -> convertToSimpleDataStructNode(it, includeSystemFields))
                    .collect(Collectors.toList());
            simpleRelationModels.forEach(it -> queriedModelMap.put(it.getKey(), it));

            queriedChildren.addAll(simpleRelationModels);
        }

        if (CollectionUtils.isEmpty(queriedChildren)) {
            return;
        }

        for (SimpleDataStructNode childModel : queriedChildren) {
            List<SimpleDataStructNode> children = new ArrayList<>();
            recursiveFillRelationModel(teamCode, childModel, children, queriedModelMap, includeSystemFields, depth-1);

            childModel.setRelationModels(children);
        }
        childrenModels.addAll(queriedChildren);
    }

    @NotNull
    private SimpleDataStructNode.SimpleDataStructFieldNode getSimpleDataStructFieldNode(String teamCode, DataStructFieldNode field) {
        SimpleDataStructNode.SimpleDataStructFieldNode simpleField = new SimpleDataStructNode.SimpleDataStructFieldNode();
        simpleField.setKey(field.getKey());
        simpleField.setName(field.getName());
        simpleField.setAlias(field.getAlias());
        simpleField.setType(field.getProps().getFieldType().name());
        simpleField.setRequired(field.getProps().isRequired());
        if (field.getProps().getDictPros() != null) {
            simpleField.setDictProps(field.getProps().getDictPros());
        }

        if (field.getProps().getRelationMeta() != null) {
            simpleField.setRelKey(field.getProps().getRelationMeta().getRelationModelAlias());
            DataStructNode relationModel = cache.getModelMeta(teamCode, field.getProps().getRelationMeta().getRelationModelAlias());
            if (relationModel != null) {
                simpleField.setRelName(relationModel.getName());
            }
        }
        return simpleField;
    }

    private DataStructNode buildModelMeta(ModelBatchCreateRequest request, ImportModelInfo importModelInfo, TableStructure tableStructure) {
        DataStructNode dataStructNode = new DataStructNode();

        dataStructNode.setTeamId(request.getTeamId());
        dataStructNode.setKey(importModelInfo.getModelKey());
        dataStructNode.setAlias(importModelInfo.getModelKey());
        dataStructNode.setName(importModelInfo.getModelName());
        dataStructNode.setParentKey(request.getParentKey());
        dataStructNode.setProps(buildModelProps(importModelInfo, tableStructure));
        dataStructNode.setChildren(buildModelFields(importModelInfo.getModelKey(), tableStructure));

        return dataStructNode;
    }

    /**
     * 构建模型属性
     *
     * @param importModelInfo
     * @param tableStructure
     * @return
     */
    private DataStructProperties buildModelProps(ImportModelInfo importModelInfo, TableStructure tableStructure) {
        DataStructProperties props = new DataStructProperties();
        props.setType(DataStructType.PERSIST);
        props.setOriginOrgIdEnabled(importModelInfo.isOriginOrgIdEnabled());
        props.setTenantIdEnabled(importModelInfo.isTenantIdEnabled());
        String mainField = tableStructure.getColumns().containsKey("name") ? "name" : "id";
        props.setMainField(mainField);
        props.setTableName(tableStructure.getTableName());

        return props;
    }

    /**
     * 构建模型字段元数据
     * @param tableStructure
     * @return
     */
    private List<DataStructFieldNode> buildModelFields(String modelKey, TableStructure tableStructure) {
        List<DataStructFieldNode> dataStructFieldNodes = new ArrayList<>();

        tableStructure.getColumns().values().forEach(column -> {

            DataStructFieldNode dataStructFieldNode = new DataStructFieldNode();
            dataStructFieldNode.setKey(column.getColumnName());
            dataStructFieldNode.setAlias(AliasUtil.fieldKeyToAlias(column.getColumnName()));
            if (StringUtils.isNotEmpty(column.getComment())) {
                dataStructFieldNode.setName(column.getComment());
            } else {
                dataStructFieldNode.setName(column.getColumnName());
            }

            DataStructFieldProperties props = new DataStructFieldProperties();
            props.setColumnName(column.getColumnName());
            if (column.getColumnName().equals("id")) {
                props.setAutoGenerated(true);
            }
            if (StringUtils.isNotEmpty(column.getComment())) {
                props.setComment(column.getComment());
            }
            props.setFieldType(inferType(column.getType()));
            props.setRequired(column.isNonNull());
            props.setIsSystemField(isSystemField(column.getColumnName()));
            if (column.getDefaultValue() != null) {
                props.setDefaultValue(column.getDefaultValue());
            }
            // 设置 createdBy/updatedBy 对象
            if (column.getColumnName().equals("created_by")
                    || column.getColumnName().equals("updated_by")
                    || column.getColumnName().equals("createdBy")
                    || column.getColumnName().equals("updatedBy")) {
                RelationMeta relationMeta = getRelationMeta(modelKey, column.getColumnName());
                props.setRelationMeta(relationMeta);
                props.setFieldType(FieldType.OBJECT);
            }
            fillFieldLengthProperty(props, column);

            dataStructFieldNode.setProps(props);
            dataStructFieldNodes.add(dataStructFieldNode);
        });

        return dataStructFieldNodes;
    }

    @NotNull
    private static RelationMeta getRelationMeta(String modelKey, String fieldName) {
        RelationMeta relationMeta = new RelationMeta();

        relationMeta.setCurrentModelAlias(modelKey);
        relationMeta.setRelationType(ModelRelationTypeEnum.LINK);
        relationMeta.setSync(false);

        switch (fieldName) {
            case "created_by":
            case "createdBy":
                relationMeta.setCurrentModelFieldAlias("createdBy");
                break;
            case "updated_by":
            case "updatedBy":
                relationMeta.setCurrentModelFieldAlias("updatedBy");
                break;
        }
        relationMeta.setRelationModelAlias(AliasUtil.moduleKey(modelKey) + "$user");
        relationMeta.setRelationModelKey(AliasUtil.moduleKey(modelKey) + "$user");

        return relationMeta;
    }

    private boolean isSystemField(String columnName) {
        return "id".equals(columnName)
                || "created_at".equals(columnName)
                || "created_by".equals(columnName)
                || "updated_at".equals(columnName)
                || "updated_by".equals(columnName)
                || "origin_org_id".equals(columnName)
                || "tenant_id".equals(columnName)
                || "version".equals(columnName)
                || "deleted".equals(columnName)
                || "createdAt".equals(columnName)
                || "updatedAt".equals(columnName)
                || "createdBy".equals(columnName)
                || "updatedBy".equals(columnName)
                || "originOrgId".equals(columnName);

    }

    /**
     * 根据 table schema 字段类型反向推模型元数据字段类型
     *
     * @param type
     * @return
     */
    private FieldType inferType(String type) {
        switch (type) {
            case "tinyint":
                return FieldType.BOOL;
            case "int":
            case "decimal":
            case "bigint":
                return FieldType.NUMBER;
            case "varchar":
                return FieldType.TEXT;
            case "text":
            case "mediumtext":
            case "json":
                return FieldType.MULTI_TEXT;
            case "datetime":
                return FieldType.DATE;
            default:
                return FieldType.TEXT;
        }
    }

    /**
     * 填充字段长度属性
     *
     * @param props
     * @param column
     */
    private void fillFieldLengthProperty(DataStructFieldProperties props, ColumnStructure column) {
        switch (column.getType()) {
            case "tinyint":
                props.setLength(1);
                break;
            case "int":
            case "bigint":
                if (column.getOneLength() == null) {
                    props.setLength(20);
                } else {
                    props.setLength(column.getOneLength());
                }
            case "varchar":
                props.setLength(column.getOneLength());
                break;
            case "text":
            case "json":
            case "mediumtext":
            case "datetime":
                break;
            case "decimal":
                props.setIntLength(column.getOneLength());
                props.setScale(column.getTwoLength());
                break;
            default:
                if (column.getOneLength() == null) {
                    props.setLength(20);
                } else {
                    props.setLength(column.getOneLength());
                }
                break;
        }
    }

    private void checkBatchCreateParam(ModelBatchCreateRequest request) {
        if (request.getTeamId() == null) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "teamId" });
        }
        if (CollectionUtils.isEmpty(request.getModels())) {
            throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "models" });
        }

        request.getModels().forEach(item -> {
            if (StringUtils.isEmpty(item.getModelKey())) {
                throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "modelKey" });
            }
            if (StringUtils.isEmpty(item.getTableName())) {
                throw new ModelMetaException(ErrorType.MODEL_MISSING_PARAM, new Object[]{ "tableName" });
            }
        });
    }

    /**
     * 创建向量数据库集合
     *
     * @param dataStructNode 数据模型节点
     */
    private void createVectorCollection(DataStructNode dataStructNode) {
        log.info("Creating vector collection for model: {}", dataStructNode.getKey());

        // 获取向量数据源配置
        ModuleDataSourceDTO config = getVectorDataSourceConfig(dataStructNode);
        if (config == null || StringUtils.isEmpty(config.getVectorDatasource())) {
            log.warn("No vector datasource configured for model {}, team: {}, module: {}",
                    dataStructNode.getKey(), dataStructNode.getTeamId(), KeyUtil.moduleKey(dataStructNode.getKey()));
            throw new ModelMetaException(ErrorType.MODEL_DATASOURCE_NOT_EXIST, "No vector datasource configured for model " + dataStructNode.getKey());
        }

        // 创建 Milvus 客户端
        MilvusClientV2 milvusClient = milvusClientFactory.getClient(dataStructNode.getTeamCode(), KeyUtil.moduleKey(dataStructNode.getKey()));

        // 构建集合名称
        String collectionName = generateVectorCollectionName(dataStructNode);

        // 检查 collection 是否已存在
        if (isVectorCollectionExists(milvusClient, collectionName)) {
            log.info("Vector collection {} already exists for model: {}, skipping creation",
                    collectionName, dataStructNode.getKey());
            return;
        }

        try {
            // 构建集合schema
            CreateCollectionReq.CollectionSchema schema = buildVectorCollectionSchema(dataStructNode);

            // 构建索引
            List<IndexParam> indexParams = buildVectorIndexParams();

            // 创建集合
            CreateCollectionReq createCollectionReq = CreateCollectionReq.builder()
                    .collectionName(collectionName)
                    .collectionSchema(schema)
                    .indexParams(indexParams)
                    .build();

            milvusClient.createCollection(createCollectionReq);
            log.info("Successfully created vector collection: {} for model: {}", collectionName, dataStructNode.getKey());
        } catch (Exception e) {
            log.error("Failed to create vector collection for model: {}, error: {}", dataStructNode.getKey(), e.getMessage(), e);
            throw new ModelMetaException(ErrorType.MODEL_INVALID_STATE, new Object[]{"Failed to create vector collection, error: " + e.getMessage()});
        }
    }

    /**
     * 删除向量库 collection
     *
     * @param dataStructNode
     */
    private void deleteVectorCollection(DataStructNode dataStructNode) {
        log.info("Deleting vector collection for model: {}", dataStructNode.getKey());

        // 获取向量数据源配置
        ModuleDataSourceDTO config = getVectorDataSourceConfig(dataStructNode);
        if (config == null || StringUtils.isEmpty(config.getVectorDatasource())) {
            log.warn("No vector datasource configured for model {}, team: {}, module: {}",
                    dataStructNode.getKey(), dataStructNode.getTeamId(), KeyUtil.moduleKey(dataStructNode.getKey()));
            throw new ModelMetaException(ErrorType.MODEL_DATASOURCE_NOT_EXIST, "No vector datasource configured for model " + dataStructNode.getKey());
        }

        // 获取 Milvus 客户端
        MilvusClientV2 milvusClient = milvusClientFactory.getClient(dataStructNode.getTeamCode(), config.getModuleKey());

        // 构建集合名称
        String collectionName = generateVectorCollectionName(dataStructNode);

        // 检查 collection 是否已存在
        if (!isVectorCollectionExists(milvusClient, collectionName)) {
            log.info("Vector collection {} doesn't exist for model: {}, skipping deletion",
                    collectionName, dataStructNode.getKey());
            return;
        }

        // 删除集合
        DropCollectionReq dropCollectionReq = DropCollectionReq.builder()
                .collectionName(collectionName)
                .build();

        milvusClient.dropCollection(dropCollectionReq);
        log.info("Successfully deleted vector collection: {} for model: {}", collectionName, dataStructNode.getKey());
    }

    /**
     * 检查向量集合是否已存在
     *
     * @param milvusClient Milvus客户端
     * @param collectionName 集合名称
     * @return 如果集合存在返回true，否则返回false
     */
    private boolean isVectorCollectionExists(MilvusClientV2 milvusClient, String collectionName) {
        HasCollectionReq hasCollectionReq = HasCollectionReq.builder()
                .collectionName(collectionName)
                .build();

        Boolean exists = milvusClient.hasCollection(hasCollectionReq);
        return BooleanUtils.isTrue(exists);
    }

    /**
     * 获取向量数据源配置
     *
     * @param dataStructNode 数据模型节点
     * @return 模块数据源配置
     */
    private ModuleDataSourceDTO getVectorDataSourceConfig(DataStructNode dataStructNode) {
        return dataSourceConfigService.queryModuleDataSourceConfig(
                dataStructNode.getTeamId(),
                KeyUtil.moduleKey(dataStructNode.getKey())
        );
    }

    /**
     * 构建向量集合schema
     *
     * @param dataStructNode 数据模型节点
     * @return 集合schema
     */
    private CreateCollectionReq.CollectionSchema buildVectorCollectionSchema(DataStructNode dataStructNode) {
        CreateCollectionReq.CollectionSchema schema = CreateCollectionReq.CollectionSchema.builder()
                .fieldSchemaList(new ArrayList<>())
                .build();

        // 添加主键字段
        schema.addField(buildPrimaryKeyField(dataStructNode));

        // 添加向量字段
        schema.addField(buildVectorField());

        // 添加模型字段
        for (DataStructFieldNode field : dataStructNode.getChildren()) {
            if (shouldIncludeFieldInVectorCollection(field)) {
                AddFieldReq fieldReq = mapToMilvusField(field);
                if (fieldReq != null) {
                    schema.addField(fieldReq);
                }
            }
        }

        return schema;
    }

    private List<IndexParam> buildVectorIndexParams() {
        // 添加索引
        List<IndexParam> indexParams = new ArrayList<>();
        IndexParam vectorIndexParam = IndexParam.builder()
                .fieldName("vector")
                .indexType(IndexParam.IndexType.AUTOINDEX)
                .metricType(IndexParam.MetricType.COSINE)
                .build();
        indexParams.add(vectorIndexParam);

        return indexParams;
    }

    /**
     * 构建主键字段
     *
     * @param dataStructNode 数据模型节点
     * @return 主键字段请求
     */
    private AddFieldReq buildPrimaryKeyField(DataStructNode dataStructNode) {
        return AddFieldReq.builder()
                .fieldName("id")
                .dataType(DataType.Int64)
                .isPrimaryKey(true)
                .autoID(false)
                .description("Primary key field")
                .build();
    }

    /**
     * 构建向量字段
     *
     * @return 向量字段请求
     */
    private AddFieldReq buildVectorField() {
        return AddFieldReq.builder()
                .fieldName("vector")
                .dataType(DataType.FloatVector)
                .dimension(2048)
                .description("Vector embedding field")
                .build();
    }

    /**
     * 判断字段是否应该包含在向量集合中
     *
     * @param field 字段节点
     * @return 是否包含
     */
    private boolean shouldIncludeFieldInVectorCollection(DataStructFieldNode field) {
        if (field.getKey().equalsIgnoreCase(SystemFieldGeneratorV2.ID.getFieldAlias())) {
            return false; // 排除 id
        }

        return field.getProps().getRelationMeta() == null || field.getProps().getRelationMeta().getRelationType() != ModelRelationTypeEnum.PARENT_CHILD; // 排除引用多条字段
    }

    /**
     * 将模型字段映射为Milvus字段
     *
     * @param field 模型字段
     * @return Milvus字段请求，如果不支持则返回null
     */
    private AddFieldReq mapToMilvusField(DataStructFieldNode field) {
        FieldType fieldType = field.getProps().getFieldType();
        DataType milvusDataType = mapFieldTypeToMilvusDataType(fieldType);

        if (milvusDataType == null) {
            log.debug("Unsupported field type for vector collection: {} ({})", field.getKey(), fieldType);
            return null;
        }

        AddFieldReq.AddFieldReqBuilder builder = AddFieldReq.builder()
                .fieldName(field.getKey())
                .dataType(milvusDataType)
                .description(field.getName());

        // 设置字段特定参数
        switch (milvusDataType) {
            case VarChar:
                builder.maxLength(getVarCharMaxLength(field));
                break;
            default:
                // 其他类型无需特殊处理
                break;
        }

        return builder.build();
    }

    /**
     * 将FieldType映射为Milvus DataType
     *
     * @param fieldType 字段类型
     * @return Milvus数据类型，如果不支持则返回null
     */
    private DataType mapFieldTypeToMilvusDataType(FieldType fieldType) {
        return switch (fieldType) {
            case TEXT, MULTI_TEXT, EMAIL, ENUM, TIME -> DataType.VarChar;
            case NUMBER, OBJECT -> DataType.Double; // 使用Double以支持小数
            case BOOL -> DataType.Bool;
            case DATE -> DataType.VarChar; // 日期转为字符串存储
            case ATTACHMENT -> DataType.VarChar; // 附件信息转为字符串存储
            default -> null;
        };
    }

    /**
     * 获取VarChar字段的最大长度
     *
     * @param field 字段节点
     * @return 最大长度
     */
    private Integer getVarCharMaxLength(DataStructFieldNode field) {
        Integer length = field.getProps().getLength();
        if (length != null && length > 0) {
            return Math.min(length, 65535); // Milvus VarChar最大长度限制
        }

        // 根据字段类型设置默认长度
        FieldType fieldType = field.getProps().getFieldType();
        return switch (fieldType) {
            case TEXT, EMAIL, ENUM, OBJECT -> 256;
            case MULTI_TEXT, ATTACHMENT -> 65535;
            case TIME, DATE -> 64;
            default -> 20;
        };
    }

    /**
     * 生成向量集合名称
     *
     * @param dataStructNode 数据模型节点
     * @return 持久模型对应的向量集合名称
     */
    private String generateVectorCollectionName(DataStructNode dataStructNode) {
        return KeyUtil.shortKey(dataStructNode.getKey()) + "_collection";
    }
}
