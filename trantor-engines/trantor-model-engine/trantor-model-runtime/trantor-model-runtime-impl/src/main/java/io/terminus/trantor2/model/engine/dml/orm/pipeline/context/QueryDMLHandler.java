package io.terminus.trantor2.model.engine.dml.orm.pipeline.context;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.common.utils.MapUtil;
import io.terminus.trantor2.condition.ConditionItem;
import io.terminus.trantor2.condition.ConditionItems;
import io.terminus.trantor2.condition.enums.LogicOperator;
import io.terminus.trantor2.condition.enums.Operator;
import io.terminus.trantor2.model.common.consts.ConditionLogicalOperator;
import io.terminus.trantor2.model.common.consts.ConditionType;
import io.terminus.trantor2.model.common.consts.DataQueryType;
import io.terminus.trantor2.model.common.model.BasicObject;
import io.terminus.trantor2.model.common.model.condition.*;
import io.terminus.trantor2.model.common.model.request.*;
import io.terminus.trantor2.model.engine.dml.orm.exception.ModelDataException;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.Repository;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.CondQuery;
import io.terminus.trantor2.model.engine.dml.orm.pipeline.repository.model.ModelQuery;
import io.terminus.trantor2.model.engine.dml.orm.util.LongUtil;
import io.terminus.trantor2.model.management.meta.cache.DataStructMetaCache;
import io.terminus.trantor2.model.management.meta.consts.SystemFieldGeneratorV2;
import io.terminus.trantor2.model.management.meta.domain.DataStructFieldNode;
import io.terminus.trantor2.model.management.meta.domain.DataStructNode;
import io.terminus.trantor2.model.management.meta.domain.RelationMeta;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.system.SystemModelInstanceHolder;
import io.terminus.trantor2.model.management.meta.system.SystemUser;
import io.terminus.trantor2.model.management.meta.util.AliasUtil;
import io.terminus.trantor2.model.management.meta.util.ModelUtil;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.setting.BusinessSettingConst;
import io.terminus.trantor2.setting.BusinessSettingUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static io.terminus.trantor2.common.exception.ErrorType.MODEL_FIND_ONE_BUT_HAS_MULTI_RECORD;
import static io.terminus.trantor2.common.exception.ErrorType.MODEL_RELATION_NOT_EXITS;

/**
 * 级联查询树
 *
 * <AUTHOR>
 * @since 2022/9/7
 */
@SuppressWarnings({"rawtypes"})
public class QueryDMLHandler extends AbstractDMLHandler {
    private final ModuleQueryService moduleQueryService;

    private final Cache<String, Object> caffeineCache;

    public QueryDMLHandler(Repository repository, DataStructMetaCache modelMetaCache, ModuleQueryService moduleQueryService, Cache<String, Object> caffeineCache) {
        super(repository, modelMetaCache);
        this.moduleQueryService = moduleQueryService;
        this.caffeineCache = caffeineCache;
    }

    private SystemModelInstanceHolder systemModelInstanceHolder;

    /**
     * 主模型记录
     */
    private List<Map<String, Object>> mainModelResult;

    /**
     * 系统模型记录
     */
    private Object systemModelResult;

    /**
     * 主模型记录是否为空
     */
    private boolean emptyResult;

    /**
     * 是否findOne
     */
    private boolean findOne;

    /**
     * 子模型关联查询对象
     */
    private final List<ChildModelQueryObject> childModelQueryObjects = new ArrayList<>();

    private final Map<String, ModuleMeta> moduleMetaMap = new ConcurrentHashMap<>();

    /**
     * 用户缓存单次查询中的用户信息，避免递归查询用户信息时重复调用 iam sdk, key: userId value: 用户信息
     */
    private final Map<Long, Map<String, Object>> userMap = new HashMap<>();

    private void init() {
        mainModelResult = new ArrayList<>();
        emptyResult = false;
        systemModelResult = null;
        findOne = false;
    }

    public void doExecute() {
        if (isSetting()) {
            // 若为业务配置模型查询
            settingQuery(obj);
            return;
        }

        // 重新初始化变量清空上次执行流程的结果数据
        init();

        setSystemModelInstanceHolder(modelMetaCache.getSystemModelInstanceHolder());
        if (obj.getType() == DataQueryType.FIND_ONE || obj.getType() == DataQueryType.FIND_BY_ID) {
            this.findOne = true;
        }

        queryMainModelData();
        recursiveQuery(childModelQueryObjects, mainModelResult);
        Object result = getResponse();

        if (SystemUser.isSystemUser(obj.getRequest().getModelAlias())) {
            // 主模型为 user
            obj.setResponseData(result);
        } else if (obj.getType() == DataQueryType.PAGING) {
            // 分页查询时，额外请求总量
            PagingRequest pagingRequest = (PagingRequest) obj.getRequest();
            List<Map<String, Object>> data = (List<Map<String, Object>>) result;

            Paging<Map<String, Object>> pagingResult = new Paging<>();
            pagingResult.setData(data);
            if (pagingRequest.getPage().isCount() && CollectionUtils.isNotEmpty(data)) {
                // 分页总数
                CondQuery condQuery = new CondQuery(teamId, pagingRequest.getModelAlias(), pagingRequest.getExtractRelation(), pagingRequest.getConditionGroup(), pagingRequest.getConditionItems());
                Long total = repository.count(condQuery);
                pagingResult.setTotal(total);
            }
            obj.setResponseData(pagingResult);
        } else {
            // id 查询
            obj.setResponseData(result);
        }
    }

    /**
     * 查询出来所有主模型数据
     */
    private void queryMainModelData() {
        BasicObject request = obj.getRequest();
        if (systemModelInstanceHolder.isSystemModel(request.getModelAlias())) {
            queryUser(request);
            return;
        }

        if (obj.getRequest() instanceof CountRequest && !containsShardingField((CountRequest) obj.getRequest())) {
            throw new BusinessException("开启分表模型操作时需包含分表字段条件");
        }

        doExtractRelation(request);
        // 所有Link类型的字段定义
        List<RelationMeta> linkRelationFields = getLinkRelationFields(request.getModelAlias());
        if (obj.getType() == DataQueryType.FIND_BY_ID) {
            IdQueryRequest idQueryRequest = (IdQueryRequest) request;
            Map<String, Object> mainModelData = repository.findOne(setDesensitization(buildQueryModel(idQueryRequest.getModelAlias(), idQueryRequest.getId(), idQueryRequest.getSelect())));
            if (mainModelData == null) {
                emptyResult = true;
                return;
            }
            formatModelData(mainModelData, linkRelationFields);
            if (idQueryRequest.isDataI18n()) {
                // 覆盖 i18n 数据
                mergeI18nData(idQueryRequest.getTeamId(), idQueryRequest.getModelAlias(), mainModelData);
            }
            mainModelResult.add(mainModelData);
        } else {
            QueryRequest queryRequest = (QueryRequest) request;
            List<Map<String, Object>> execute = repository.findAll(setDesensitization(buildQueryModel(queryRequest)));
            if (execute == null || execute.isEmpty()) {
                emptyResult = true;
                return;
            }
            if (findOne && execute.size() > 1) {
                throw new ModelDataException(MODEL_FIND_ONE_BUT_HAS_MULTI_RECORD,
                    String.format(MODEL_FIND_ONE_BUT_HAS_MULTI_RECORD.getMessage(), execute.size()), new Object[]{execute.size()});
            }
            mainModelResult = execute.stream().map(it -> {
                formatModelData(it, linkRelationFields);
                if (queryRequest.isDataI18n()) {
                    // 覆盖 i18n 数据
                    mergeI18nData(queryRequest.getTeamId(), queryRequest.getModelAlias(), it);
                }

                return it;
            }).collect(Collectors.toList());
        }
        //dealWithUser(request);
    }

    private List<RelationMeta> getLinkRelationFields(String modelAlias) {
        return getModelRelationMeta(modelAlias).stream().filter(r -> r.getRelationType() == ModelRelationTypeEnum.LINK).collect(Collectors.toList());
    }

    private void queryUser(BasicObject request) {
        Set<Long> ids = new HashSet<>();
        if (request.getConditionGroup() != null) {
            extractIds(ids, request.getConditionGroup());
        }

        if (request.getConditionItems() != null) {
            // 虽然 conditionGroup 与 conditionItems 对用户条件互斥，但 conditionGroup 默认会填充 deleted=0 的条件
            extractIdsFromConditionItems(ids, request.getConditionItems());
        }

        if (request instanceof PagingRequest) {
            if (ids.isEmpty()) {
                Map<String, Object> conditions = new HashMap<>();
                if (request.getConditionGroup() != null) {
                    extractUserConditions(conditions, request.getConditionGroup());
                }
                if (request.getConditionItems() != null) {
                    extractUserConditionsFromConditionItems(conditions, request.getConditionItems());
                }
                systemModelResult = systemModelInstanceHolder.paging(conditions, "createdBy", request.getPage().getNo(), request.getPage().getSize(), !request.isSkipDesensitization());
            } else {
                systemModelResult = new Paging<>(systemModelInstanceHolder.queryAll("createdBy", ids, !request.isSkipDesensitization()));
            }
        } else if (request instanceof IdQueryRequest) {
            IdQueryRequest idQueryRequest = (IdQueryRequest) request;
            ids.add(Long.valueOf(idQueryRequest.getId().toString()));
            systemModelResult = systemModelInstanceHolder.query("createdBy", ids, !request.isSkipDesensitization());
        } else {
            List<Map<String, Object>> lst = systemModelInstanceHolder.queryAll("createdBy", ids, !request.isSkipDesensitization());
            if (CollectionUtils.isEmpty(lst)) {
                emptyResult = true;
            } else {
                systemModelResult = lst;
            }
        }
    }

    /**
     * 提取用户模型查询条件
     *
     * @param conditions
     * @param conditionGroup
     */
    private void extractUserConditions(Map<String, Object> conditions, Condition conditionGroup) {
        if (conditionGroup == null) {
            return;
        }

        for (Condition cond : ((ConditionGroup) conditionGroup).getConditions()) {
            if (cond instanceof SingleCondition) {
                SingleCondition singleCondition = (SingleCondition) cond;
                if (singleCondition.getValue().getValue() != null) {
                    conditions.put(singleCondition.getField(), singleCondition.getValue().getValue());
                } else {
                    conditions.put(singleCondition.getField(), singleCondition.getValue().getValues());
                }
            } else {
                extractUserConditions(conditions, cond);
            }
        }
    }

    private void extractUserConditionsFromConditionItems(Map<String, Object> conditions, ConditionItems conditionItems) {
        if (conditionItems == null || MapUtils.isEmpty(conditionItems.getConditions())) {
            return;
        }

        conditionItems.getConditions().forEach((k, v) -> {
            conditions.put(k, v.getValue());
        });
    }

    private void extractIds(Set<Long> ids, ConditionGroup conditionGroup) {
        if (conditionGroup == null) {
            return;
        }
        for (Condition cond : conditionGroup.getConditions()) {
            if (cond instanceof SingleCondition) {
                extractIds(ids, (SingleCondition) cond);
            } else {
                extractIds(ids, (ConditionGroup) cond);
            }
        }
    }

    private void extractIdsFromConditionItems(Set<Long> ids, ConditionItems conditionItems) {
        if (conditionItems == null || MapUtils.isEmpty(conditionItems.getConditions())) {
            return;
        }

        for (String key : conditionItems.getConditions().keySet()) {
            if ("id".equals(key)) {
                if (conditionItems.getConditions().get(key).getOperator() == Operator.EQ) {
                    ids.add(Long.valueOf(conditionItems.getConditions().get(key).getValue().toString()));
                } else if (conditionItems.getConditions().get(key).getOperator() == Operator.IN) {
                    List<Object> values = (List<Object>) conditionItems.getConditions().get(key).getValue();
                    ids.addAll(values.stream().map(this::getLongId).collect(Collectors.toList()));
                }
                break;
            }
        }
    }

    private void extractIds(Set<Long> ids, SingleCondition singleCondition) {
        String field = singleCondition.getField();
        if ("id".equals(field)) {
            ConditionType type = singleCondition.getType();
            if (type == ConditionType.EQ) {
                Long idValue = getLongId(singleCondition.getValue().getValue());
                ids.add(idValue);
            } else if (type == ConditionType.IN) {
                List<Object> values = singleCondition.getValue().getValues();
                ids.addAll(values.stream().map(this::getLongId).collect(Collectors.toList()));
            }
        }
    }

    private long getLongId(Object it) {
        try {
            BigDecimal decimal = new BigDecimal(it.toString());
            return decimal.longValue();
        } catch (Exception e) {
            throw new UnsupportedOperationException("id type error", e);
        }
    }

    private void dealWithUser(BasicObject request) {
        // 创建人修改人
        boolean selectCreatedBy = false;
        boolean selectUpdatedBy = false;
        for (Select it : request.getSelect()) {
            String field = it.getField();
            if ("createdBy".equals(field) || "updatedBy".equals(field)) {
                List<Select> select = it.getSelect();
                if (CollectionUtils.isEmpty(select)) {
                    continue;
                }
                selectCreatedBy = selectCreatedBy || "createdBy".equals(field);
                selectUpdatedBy = selectUpdatedBy || "updatedBy".equals(field);
            }
        }
        if (!(selectCreatedBy || selectUpdatedBy)) {
            return;
        }
        Set<Long> queryIds = new HashSet<>();
        for (Map<String, Object> map : mainModelResult) {
            if (selectCreatedBy) {
                if (map.get("createdBy") != null) {
                    queryIds.add(LongUtil.getLong(((Map) map.get("createdBy")).get("id")));
                }
            }
            if (selectUpdatedBy) {
                if (map.get("updatedBy") != null) {
                    queryIds.add(LongUtil.getLong(((Map) map.get("updatedBy")).get("id")));
                }
            }
        }
        if (queryIds.isEmpty()) {
            return;
        }
        Map<Long, Map<String, Object>> users = systemModelInstanceHolder.query("createdBy", queryIds, true);
        for (Map<String, Object> map : mainModelResult) {
            if (selectCreatedBy && map.get("createdBy") != null) {
                map.put("createdBy", users.get(LongUtil.getLong(((Map) map.get("createdBy")).get("id"))));
            }
            if (selectUpdatedBy && map.get("updatedBy") != null) {
                map.put("updatedBy", users.get(LongUtil.getLong(((Map) map.get("updatedBy")).get("id"))));
            }
        }
    }

    /**
     * {"field":"invOrgId","select":[{"field":"id"},{"field":"orgName"}]}, {"field":"name","select":null}
     * @param children
     * @param parentModelData
     */
    private void recursiveQuery(List<ChildModelQueryObject> children, List<Map<String, Object>> parentModelData) {
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        if (CollectionUtils.isEmpty(parentModelData)) {
            return;
        }
        // 根据主模型ids循环查子模型数据
        for (ChildModelQueryObject child : children) {
            ModelRelationTypeEnum relationType = child.getRelationType();
            List<Object> relationIds;
            if (relationType == ModelRelationTypeEnum.PARENT_CHILD) {
                relationIds = parentModelData.stream().map(it -> it.get("id")).collect(Collectors.toList());
            } else {
                relationIds = parentModelData.stream().map(it -> getChildId(child, it)).filter(Objects::nonNull).collect(Collectors.toList());
            }
            if (relationIds.stream().allMatch(Objects::isNull)) {
                continue;
            }

            String childModelAlias = child.getChildModelAlias();
            if (systemModelInstanceHolder.isSystemModel(childModelAlias)) {
                // 避免一次操作中重复查询用户信息
                Set<Long> toQueryIds = new HashSet<>();
                List<Map<String, Object>> users = new ArrayList<>();
                relationIds.forEach(item -> {
                    Long id = (Long) item;
                    if (userMap.containsKey(id)) {
                        // 若缓存存在用户信息，从缓存读取
                        users.add(userMap.get(id));
                    } else {
                        toQueryIds.add(id);
                    }
                });

                if (CollectionUtils.isNotEmpty(toQueryIds)) {
                    // 通过 IAM SDK 查询用户信息
                    List<Map<String, Object>> queriedUsers = systemModelInstanceHolder.queryAll("createdBy", toQueryIds, !obj.getRequest().isSkipDesensitization());
                    queriedUsers.forEach(user -> userMap.put((Long) user.get("id"), user));
                    users.addAll(queriedUsers);
                }
                backFillChildModelData0(child, users, parentModelData);
                continue;
            }

            List<Select> selects = child.selectFields.stream().map(Select::new).collect(Collectors.toList());
            addSelect(selects, child.getGroupByField());

            List<Map<String, Object>> childModelDataMap = repository.findRelationInfo(setDesensitization(buildQueryModel(childModelAlias, relationIds, child.getGroupByField(), selects, child.getOrderBy(), child.getConditionGroup())), relationIds, child.getGroupByField());
            DataStructNode childModel = modelMetaCache.getModelMeta(teamId, null, childModelAlias);
            if (childModel.getProps().isI18nEnabled()) {
                // 若模型开启了 i18n 开关，将 i18n 数据覆盖至业务数据中
                mergeI18nDataWithChildModelData(teamId, childModel, relationIds, childModelDataMap);
            }

            if (childModelDataMap != null) {
                // 把关联的字段展开成Map
                List<RelationMeta> linkRelationFields = getLinkRelationFields(childModelAlias);
                childModelDataMap.forEach(it -> formatModelData(it, linkRelationFields));
                childModelDataMap.forEach(it -> convertPropertiesWithoutRelation(childModelAlias, it));
                // 递归下沉查询
                recursiveQuery(child.getChild(), childModelDataMap);
                // 回填字段
                backFillChildModelData0(child, childModelDataMap, parentModelData);
            }
        }
    }

    /**
     * 若业务数据存在 i18n 数据，优先采用 i18n 数据
     * @param teamId
     * @param childModel
     * @param relationIds
     * @param childModelDataMap
     */
    private void mergeI18nDataWithChildModelData(Long teamId, DataStructNode childModel, List<Object> relationIds, List<Map<String, Object>> childModelDataMap) {
        if (CollectionUtils.isEmpty(childModelDataMap)) {
            return;
        }

        Map<String, ConditionItem> conditions = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();
        idCondition.setValue(relationIds);
        idCondition.setOperator(Operator.IN);
        conditions.put("sourceId", idCondition);

        ConditionItem localeCondition = new ConditionItem();
        localeCondition.setValue(TrantorContext.getLang());
        localeCondition.setOperator(Operator.EQ);
        conditions.put("locale", localeCondition);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);
        conditionItems.setConditions(conditions);

        ModelQuery modelQuery = new ModelQuery(teamId, childModel.getKey() + "_i18n");
        modelQuery.setConditionItems(conditionItems);
        List<Map<String, Object>> i18nDataMap = repository.findAll(modelQuery);
        if (CollectionUtils.isEmpty(i18nDataMap)) {
            return;
        }

        Map<String, Map<String, Object>> i18nMap = i18nDataMap.stream().collect(Collectors.toMap(it -> it.get("sourceId").toString(), it -> it));

        for (Map<String, Object> childModelData : childModelDataMap) {
            if (!i18nMap.containsKey(childModelData.get("id").toString())) {
                continue;
            }

            Map<String, Object> i18nData = i18nMap.get(childModelData.get("id").toString());
            List<String> i18nFields = childModel.getChildren().stream().filter(it -> it.getProps().isI18nEnabled()).map(it -> it.getAlias()).collect(Collectors.toList());
            i18nData.forEach((key, value) -> {
                if (!i18nFields.contains(key)) {
                    return;
                }

                if (childModelData.containsKey(key)) {
                    if ((value instanceof String) && StringUtils.isNotEmpty((String) value)) {
                        childModelData.put(key, value);

                    }
                }
            });
        }
    }

    private ModelQuery buildQueryModel(String childModelAlias, List<Object> relationIds, String conditionField, List<Select> selects, List<Order> orderBy, ConditionGroup conditionGroup) {
        // 根据子模型 moduleKey 获取 moduleId, 跨模块后这里不能再用主模型的 moduleId 查询子模型数据
        String moduleKey = AliasUtil.moduleKey(childModelAlias);
        ModuleMeta currentModule = moduleMetaMap.get(moduleKey);
        if (currentModule == null) {
            currentModule = moduleQueryService.findByKey(moduleKey);
            if (currentModule == null) {
                throw new ModelDataException(ErrorType.MODULE_META_INFO_LACK, String.format("module %s meta info doesn't exist", currentModule), new Object[]{currentModule});
            }
            moduleMetaMap.put(moduleKey, currentModule);
        }

        DataStructNode childModel = modelMetaCache.getModelMeta(teamId, null, childModelAlias);
        ModelQuery queryModel;
        if (ModelUtil.isSetting(childModel)) {
            // 若子模型为业务配置，需转化为查业务配置底层存储模型
            queryModel = new ModelQuery(teamId, BusinessSettingUtil.composeSettingKey(currentModule.getKey()));
        } else {
            queryModel = new ModelQuery(teamId, childModelAlias);
        }

        queryModel.setSelectFields(selects);

        Map<String, DataStructFieldNode> fieldMap = childModel.toFieldMap();
        // 优先以用户指定的 order 排序为准；若未指定，检查模型定义是否有配置排序字段，若有，则按照模型定义的排序字段排序，若无则按照更新时间排序
        if (CollectionUtils.isNotEmpty(orderBy)) {
            queryModel.setOrders(orderBy);
        } else if (!fieldMap.containsKey(SystemFieldGeneratorV2.ORDER.getFieldAlias())) {
            queryModel.setOrders(Lists.newArrayList(new Order(SystemFieldGeneratorV2.ID.getFieldAlias(), true)));
        } else {
            queryModel.setOrders(Lists.newArrayList(new Order(SystemFieldGeneratorV2.ORDER.getFieldAlias(), true), new Order(SystemFieldGeneratorV2.UPDATED_AT.getFieldAlias(), false)));
        }

        SingleCondition condition = SingleCondition.builder()
            .field(conditionField)
            .type(ConditionType.IN)
            .value(ConditionValue.builder().values(relationIds).type(ConditionValueType.VALUE).build())
            .build();
        List<Condition> conditions = addDeletedConditionIfNecessary(childModelAlias, condition);

        queryModel.setConditionGroup(addChildrenCondition(conditionGroup, conditions));

        return queryModel;
    }

    private ConditionGroup addChildrenCondition(ConditionGroup userConditionGroup,  List<Condition> conditions) {
        ConditionGroup result = new ConditionGroup(conditions);
        if (userConditionGroup != null) {
            ConditionGroup group = new ConditionGroup(userConditionGroup.getConditions(), ConditionLogicalOperator.AND);
            result.setConditions(Lists.newArrayList(group, new ConditionGroup(conditions)));
        }
        return result;
    }

    @Nullable
    private static Object getChildId(ChildModelQueryObject child, Map<String, Object> value) {
        Object o = value.get(child.getMainModelDependencyField());
        if (o == null) {
            return null;
        }
        return ((Map) o).get("id");
    }

    private void addSelect(List<Select> selects, String groupByField) {
        if (selects.stream().noneMatch(s -> s.getField().equals(groupByField))) {
            selects.add(new Select(groupByField));
        }
    }

    private void doExtractRelation(BasicObject request) {
        List<ChildModelQueryObject> childModelQueryObjects = extractRelationQuery(request.getModelAlias(), request.getSelect());
        if (CollectionUtil.isNotEmpty(childModelQueryObjects)) {
            this.childModelQueryObjects.addAll(childModelQueryObjects);
        }
    }

    /**
     * 解析关联子模型查询
     */
    private List<ChildModelQueryObject> extractRelationQuery(String modelAlias, List<Select> select) {
        List<ChildModelQueryObject> children = new ArrayList<>();
        if (CollectionUtils.isEmpty(select)) {
            return children;
        }

        Map<String, List<Order>> orderMap = select.stream()
                .filter(it -> it.getOrderBy() != null)
                .collect(Collectors.toMap(Select::getField, Select::getOrderBy));

        Map<String, ConditionGroup> conditionGroupMap = select.stream()
            .filter(it -> it.getConditionGroup() != null)
            .collect(Collectors.toMap(Select::getField, Select::getConditionGroup));

        Map<String, List<Select>> relationSelects = select.stream()
            .filter(it -> it.getSelect() != null)
            .collect(Collectors.toMap(Select::getField, it -> new ArrayList<>(it.getSelect())));
        for (Map.Entry<String, List<Select>> entry : relationSelects.entrySet()) {
            String relationFieldAlias = entry.getKey();
            RelationMeta relation = modelMetaCache
                .getRelationByRelationAlias(teamId, modelAlias, relationFieldAlias);
            if (relation == null) {
                throw new ModelDataException(MODEL_RELATION_NOT_EXITS,
                    String.format(MODEL_RELATION_NOT_EXITS.getMessage(), modelAlias, relationFieldAlias),
                    new Object[]{modelAlias, relationFieldAlias});
            }
            ChildModelQueryObject child = ChildModelQueryObject.builder()
                .relationAlias(relationFieldAlias)
                .selectFields(entry.getValue().stream().map(Select::getField).collect(Collectors.toList()))
                .conditionGroup(conditionGroupMap.get(relationFieldAlias))
                .orderBy(orderMap.get(relationFieldAlias))
                .build();
            if (relation.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD) {
                child.setRelationType(ModelRelationTypeEnum.PARENT_CHILD);
                child.setChildModelAlias(relation.getRelationModelAlias());
                child.setMainModelDependencyField(SystemFieldGeneratorV2.ID.getFieldAlias());
                child.setGroupByField(relation.getLinkModelFieldAlias());
            } else if (relation.getRelationType() == ModelRelationTypeEnum.LINK) {
                child.setRelationType(ModelRelationTypeEnum.LINK);
                child.setChildModelAlias(relation.getRelationModelAlias());
                child.setMainModelDependencyField(relation.getCurrentModelFieldAlias());
                child.setGroupByField(SystemFieldGeneratorV2.ID.getFieldAlias());
            }
            List<ChildModelQueryObject> recursive = extractRelationQuery(relation.getRelationModelAlias(), entry.getValue());
            if (!recursive.isEmpty()) {
                child.setChild(recursive);
            }
            children.add(child);
        }
        return children;
    }

    /**
     * 根据子模型上记录的主模型依赖字段值进行分组，数据回填到主模型记录上
     *
     * @param childModelQueryObject 子模型信息
     * @param childModelDataMap     子模型数据
     * @param parentModelData       父模型数据
     */
    private void backFillChildModelData0(ChildModelQueryObject childModelQueryObject, List<Map<String, Object>> childModelDataMap, List<Map<String, Object>> parentModelData) {
        if (CollectionUtils.isEmpty(childModelDataMap)) {
            return;
        }

        Map<String, List<Integer>> idIndex = buildIndex(parentModelData, childModelQueryObject.getMainModelDependencyField());
        Map<Object, List<Map<String, Object>>> groupByParent = childModelDataMap.stream()
            .collect(Collectors.groupingBy(it -> {
                Object group = it.get(childModelQueryObject.getGroupByField());
                if (group instanceof Map) {
                    return ((Map<?, ?>) group).get("id");
                }
                return group;
            }));

        for (Map.Entry<Object, List<Map<String, Object>>> entry : groupByParent.entrySet()) {
            Object parentDependencyValue = entry.getKey();
            List<Integer> indexes = idIndex.get(String.valueOf(parentDependencyValue));
            if (indexes == null) {
                continue;
            }
            for (Integer index : indexes) {
                Map<String, Object> relatedParentModelData = parentModelData.get(index);
                Object childData = childModelQueryObject.getRelationType() == ModelRelationTypeEnum.LINK
                    ? entry.getValue().get(0)
                    : entry.getValue();
                relatedParentModelData.put(childModelQueryObject.getRelationAlias(), childData);
            }
        }
    }

    private Map<String, List<Integer>> buildIndex(List<Map<String, Object>> parentModelData, String dependency) {
        Map<String, List<Integer>> idIndex = new HashMap<>();
        for (int i = 0; i < parentModelData.size(); i++) {
            Object o = parentModelData.get(i).get(dependency);
            if (o != null) {
                String parentValue;
                if ("id".equals(dependency)) {
                    parentValue = String.valueOf(o);
                } else {
                    parentValue = String.valueOf(((Map) o).get("id"));
                }
                // 因为存在多对一，所以不能只存一个parent id index
                List<Integer> integers = idIndex.get(parentValue);
                if (integers == null) {
                    integers = Lists.newArrayList();
                }
                integers.add(i);
                idIndex.put(parentValue, integers);
            }
        }
        return idIndex;
    }

    /**
     * 对于关联字段，需要展开成Map
     */
    public static Map<String, Object> formatModelData(Map<String, Object> data, List<RelationMeta> linkRelationFields) {
        if (CollectionUtil.isEmpty(linkRelationFields)) {
            return data;
        }

        for (RelationMeta relationField : linkRelationFields) {
            Object value = data.get(relationField.getCurrentModelFieldAlias());
            if (value != null) {
                data.put(relationField.getCurrentModelFieldAlias(), MapUtil.of("id", value));
            }
        }
        return data;
    }

    private void mergeI18nData(Long teamId, String modelAlias, Map<String, Object> data) {
        if (MapUtils.isEmpty(data)) {
            return;
        }

        DataStructNode model = modelMetaCache.getModelMeta(teamId, null, modelAlias);
        assert model != null;

        Map<String, ConditionItem> conditions = new HashMap<>();
        ConditionItem idCondition = new ConditionItem();
        idCondition.setValue(data.get("id"));
        idCondition.setOperator(Operator.EQ);
        conditions.put("sourceId", idCondition);

        ConditionItem localeCondition = new ConditionItem();
        localeCondition.setValue(TrantorContext.getLang());
        localeCondition.setOperator(Operator.EQ);
        conditions.put("locale", localeCondition);

        ConditionItems conditionItems = new ConditionItems();
        conditionItems.setLogicOperator(LogicOperator.AND);
        conditionItems.setConditions(conditions);

        ModelQuery modelQuery = new ModelQuery(teamId, model.getKey() + "_i18n");
        modelQuery.setConditionItems(conditionItems);
        Map<String, Object> i18nData = repository.findOne(modelQuery);
        if (MapUtils.isEmpty(i18nData)) {
            return;
        }

        List<String> i18nFields = model.getChildren().stream().filter(it -> it.getProps().isI18nEnabled()).map(it -> it.getAlias()).collect(Collectors.toList());
        i18nData.forEach((key, value) -> {
            if (!i18nFields.contains(key)) {
                return;
            }

            if (data.containsKey(key)) {
                if ((value instanceof String) && StringUtils.isNotEmpty((String) value)) {
                    data.put(key, value);
                }
            }
        });
    }

    private Object getResponse() {
        if (emptyResult) {
            return null;
        }
        // 优先返回系统模型结果
        if (systemModelResult != null) {
            if (findOne) {
                if (systemModelResult instanceof List) {
                    return ((List) systemModelResult).get(0);
                } else {
                    return systemModelResult;
                }
            } else {
                return systemModelResult;
            }
        }
        // 返回主模型结果
        if (findOne) {
            return mainModelResult.get(0);
        } else {
            return mainModelResult;
        }
    }

    private ModelQuery buildQueryModel(QueryRequest request) {
        ModelQuery queryModel = new ModelQuery(teamId, request.getModelAlias());
        queryModel.setConditionGroup(request.getConditionGroup());
        queryModel.setConditionItems(request.getConditionItems());
        queryModel.setPageable(request.getPage());
        queryModel.setSelectFields(request.getSelect());
        queryModel.setOrders(request.getOrderBy());
        queryModel.setExtractRelation(request.getExtractRelation());
        queryModel.setDefaultLocale(request.getDefaultLocale());
        queryModel.setDataI18n(request.isDataI18n());
        return queryModel;
    }

    private ModelQuery setDesensitization(ModelQuery queryModel) {
        queryModel.setSkipDesensitization(obj.getRequest().isSkipDesensitization());
        return queryModel;
    }

    private void settingQuery(DataObject obj) {
        // 指定额外的 properties select
        List<Select> selects = obj.getRequest().getSelect();
        selects.add(new Select(BusinessSettingConst.FIELD_PROPERTIES));
        selects.add(new Select(BusinessSettingConst.FIELD_DELETED));

        String moduleKey = AliasUtil.moduleKey(obj.getRequest().getModelAlias());

        if (obj.getRequest() instanceof IdQueryRequest) {
            // id 详情查询
            IdQueryRequest idRequest = (IdQueryRequest) obj.getRequest();

            Map<String, Object> result = null;
            Object cacheValue = caffeineCache.getIfPresent(obj.getRequest().calculateObjectKey());
            if (cacheValue != null) {
               result = (Map<String, Object>) cacheValue;
            }
            if (result == null) {
                result = repository.findOne(buildQueryModel(BusinessSettingUtil.composeSettingKey(moduleKey), idRequest.getId(), idRequest.getSelect()));
                if (MapUtils.isNotEmpty(result)) {
                    // 提取业务配置扩展配置项
                    convertProperties(result);
                    caffeineCache.put(obj.getRequest().calculateObjectKey(), result);
                }
            }

            obj.setResponseData(result);

        } else if (obj.getRequest() instanceof PagingRequest) {
            // 分页查询
            Paging<Map<String, Object>> result = new Paging<>();

            PagingRequest pagingRequest = (PagingRequest) obj.getRequest();

            // 添加 deleted=0 条件
            ConditionGroup conditionGroup = pagingRequest.getConditionGroup();
            if (conditionGroup == null) {
                conditionGroup = new ConditionGroup();
            }
            addGroupKeyCondition(pagingRequest.getModelAlias(), conditionGroup);
            addDeletedCondition(conditionGroup);
            pagingRequest.setConditionGroup(conditionGroup);

            String originalModelKey = pagingRequest.getModelAlias();
            pagingRequest.setModelAlias(BusinessSettingUtil.composeSettingKey(moduleKey));

            List<Map<String, Object>> data = repository.findAll(setDesensitization(buildQueryModel(pagingRequest)));
            pagingRequest.setModelAlias(originalModelKey);
            if (CollectionUtils.isNotEmpty(data)) {
                for (Map<String, Object> item : data) {
                    // 提取业务配置扩展配置项
                    convertProperties(item);
                }
            }
            result.setData(data);

            if (pagingRequest.getPage().isCount() && CollectionUtils.isNotEmpty(data)) {
                CondQuery condQuery = new CondQuery(teamId, BusinessSettingUtil.composeSettingKey(moduleKey), null, pagingRequest.getConditionGroup(), pagingRequest.getConditionItems());
                Long total = repository.count(condQuery);
                result.setTotal(total);
            }

            obj.setResponseData(result);
        }
    }

    private void addDeletedCondition(ConditionGroup conditionGroup) {
        List<Condition> conditions = conditionGroup.getConditions();
        if (conditions == null) {
            conditions = new ArrayList<>();
            conditions.add(new SingleCondition("deleted", ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null));
            conditionGroup.setConditions(conditions);
        } else {
            // 这里在组装成一个group，是担心conditions是2个或的组，这时在拼deleted，会在或之外，导致查询错误
            ConditionGroup group = new ConditionGroup(conditions, ConditionLogicalOperator.AND);
            ConditionGroup deletedCond = new ConditionGroup(Lists.newArrayList(new SingleCondition("deleted", ConditionType.EQ, new ConditionValue(0, null, null, ConditionValueType.VALUE), null)));
            conditionGroup.setConditions(Lists.newArrayList(group, deletedCond));
        }
    }

    private void addGroupKeyCondition(String modelKey, ConditionGroup conditionGroup) {
        List<Condition> conditions = conditionGroup.getConditions();
        if (conditions == null) {
            List<Condition> arrayList = new ArrayList<>();
            arrayList.add(new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(modelKey, null, null, ConditionValueType.VALUE), null));
            conditionGroup.setConditions(arrayList);
        } else {
            ConditionGroup group = new ConditionGroup(conditionGroup.getConditions(), ConditionLogicalOperator.AND);
            ConditionGroup groupKeyCond = new ConditionGroup(Lists.newArrayList(new SingleCondition(BusinessSettingConst.FIELD_GROUP_KEY, ConditionType.EQ, new ConditionValue(modelKey, null, null, ConditionValueType.VALUE), null)));
            conditionGroup.setConditions(Lists.newArrayList(group, groupKeyCond));
        }
    }

    @Data
    @Builder
    public static class ChildModelQueryObject {

        /**
         * 关联关系标识
         */
        private String relationAlias;

        /**
         * 子模型别名
         */
        private String childModelAlias;

        /**
         * 中间模型别名
         */
        private String middleModelAlias;

        /**
         * 关联关系类型
         */
        private ModelRelationTypeEnum relationType;

        /**
         * 主模型上的关联字段名，主子关系下固定为id，引用关系下为存储的子表id字段
         */
        private String mainModelDependencyField;

        /**
         * 子模型数据回填时候根据什么字段进行分组
         */
        private String groupByField;

        /**
         * 中间表join字段别名
         */
        private String joinField;

        /**
         * 查询字段
         */
        private List<String> selectFields;

        /**
         * 排序字段
         */
        private List<Order> orderBy;

        private ConditionGroup conditionGroup;

        /**
         * 下一层子模型查询
         */
        private List<ChildModelQueryObject> child;

    }

    public void setSystemModelInstanceHolder(SystemModelInstanceHolder systemModelInstanceHolder) {
        this.systemModelInstanceHolder = systemModelInstanceHolder;
    }

    /**
     * 业务配置的扩展配置项底层存储在 properties 字段中，数据返回时需提取出来
     * @param result
     */
    private void convertProperties(Map<String, Object> result) {
        if (result.get(BusinessSettingConst.FIELD_PROPERTIES) == null) {
            return;
        }

        Map<String, Object> properties = JsonUtil.fromJson(result.get(BusinessSettingConst.FIELD_PROPERTIES).toString(), new TypeReference<Map<String, Object>>(){});

        DataStructNode dataStructNode = modelMetaCache.getModelMeta(teamId, null, obj.getRequest().getModelAlias());
        dataStructNode.getChildren().forEach(field -> {
            if (!properties.containsKey(field.getAlias())) {
                return;
            }

            if (field.getProps().getRelationMeta() == null) {
                result.put(field.getAlias(), properties.get(field.getAlias()));
            } else {
                // 关联类型字段需处理成 map 类型: {id: 关联 id}
                result.put(field.getAlias(), MapUtil.of(field.getProps().getRelationMeta().getCurrentModelFieldAlias(), properties.get(field.getAlias())));
            }
        });
        result.remove(BusinessSettingConst.FIELD_PROPERTIES);
    }

    private void convertPropertiesWithoutRelation(String modelAlias, Map<String, Object> result) {
        if (result.get(BusinessSettingConst.FIELD_PROPERTIES) == null) {
            return;
        }

        Map<String, Object> properties = JsonUtil.toMap(result.get(BusinessSettingConst.FIELD_PROPERTIES));

        // 获取子模型定义
        String moduleKey = AliasUtil.moduleKey(modelAlias);
        ModuleMeta currentModule = moduleMetaMap.get(moduleKey);
        if (currentModule == null) {
            currentModule = moduleQueryService.findByKey(moduleKey);
            moduleMetaMap.put(moduleKey, currentModule);
        }
        DataStructNode dataStructNode = modelMetaCache.getModelMeta(teamId, currentModule.getId(), modelAlias);
        dataStructNode.getChildren().forEach(field -> {
            if (!properties.containsKey(field.getAlias())) {
                return;
            }

            if (field.getProps().getRelationMeta() == null) {
                result.put(field.getAlias(), properties.get(field.getAlias()));
            }
        });
        result.remove(BusinessSettingConst.FIELD_PROPERTIES);
    }
}
