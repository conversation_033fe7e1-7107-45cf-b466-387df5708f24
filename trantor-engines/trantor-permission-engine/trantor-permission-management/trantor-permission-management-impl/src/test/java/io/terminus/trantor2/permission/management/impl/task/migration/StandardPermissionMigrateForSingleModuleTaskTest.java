package io.terminus.trantor2.permission.management.impl.task.migration;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.hash.Hashing;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.permission.management.api.service.PermissionKeyInitializer;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.meta.DataManagerViewMeta;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.common.utils.DataLoader;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2025/4/23 14:11
 **/
@ExtendWith(MockitoExtension.class)
public class StandardPermissionMigrateForSingleModuleTaskTest {

    @Mock
    private MetaIndexAssetRepo metaIndexAssetRepo;
    @Mock
    private ViewRepo viewRepo;
    @Mock
    private ServiceRepo serviceRepo;
    @Mock
    private MenuConsoleQueryService menuConsoleQueryService;
    @Spy
    private PermissionKeyInitializer permissionKeyInitializer;
    @Mock
    private ModuleRepo moduleRepo;

    @InjectMocks
    @Spy
    private StandardPermissionMigrateForSingleModuleTask standardPermissionMigrateForSingleModuleTask;

//    @BeforeEach
//    public void init() {
//        standardPermissionMigrateForSingleModuleTask = new StandardPermissionMigrateForSingleModuleTask(null, null, null, null, moduleRepo, permissionKeyInitializer);
//    }

    @Test
    public void testHandleSingleView() {
        String viewSchemaFilePath = "QA$test212233:edit.json";
        DataManagerView dataManagerView = DataLoader.loadByTypeReference(viewSchemaFilePath, new TypeReference<DataManagerView>() {
        });
        DataManagerViewMeta dataManagerViewMeta = new DataManagerViewMeta();
        dataManagerViewMeta.setResourceProps(dataManagerView);
        Collection<StandardPermissionMigrateForSingleModuleTask.Result> results = standardPermissionMigrateForSingleModuleTask.handleSingleView(dataManagerViewMeta);
        System.out.println(JsonUtil.toIndentJson(results));
        Mockito.verify(standardPermissionMigrateForSingleModuleTask, Mockito.atLeastOnce()).handleSingleView(Mockito.any(DataManagerView.class));
        Assertions.assertTrue(CollectionUtils.isNotEmpty(results));
    }

    @SneakyThrows
    @Test
    public void testRemoveSubLineButton() {
        String buttonSchemaFilePath = "scene/view/button/remove-subline-button.json";
        Resource resource = new ClassPathResource(buttonSchemaFilePath);
        ArrayNode arrayNode = (ArrayNode) JsonUtil.INDENT.getObjectMapper().readTree(resource.getInputStream());
        JsonNode jsonNode = arrayNode.get(0);
        JsonNode props = jsonNode.get("props");
        JsonNode at = props.at("/eventActions/0/actions/0/type");
        Assertions.assertFalse(at.isMissingNode());
    }

    @SneakyThrows
    @Test
    public void testButton() {
        String buttonSchemaFilePath = "scene/view/button/buttons.json";
        Resource resource = new ClassPathResource(buttonSchemaFilePath);
        ArrayNode arrayNode = (ArrayNode) JsonUtil.INDENT.getObjectMapper().readTree(resource.getInputStream());
        List<JsonNode> parents = arrayNode.findParents("name");
        Map<String, String> keyToPermKey = new HashMap<>();
        for (JsonNode parent : parents) {
            if (!"Button".equalsIgnoreCase(parent.get("name").textValue())) {
                continue;
            }
            keyToPermKey.put(parent.get("key").textValue(), parent.at("/props/permissionKey").textValue());
        }
        System.out.println(keyToPermKey);
    }

    @SneakyThrows
    @Test
    public void testHandleSingleView_2() {
        String filePath = "scene/view/SCM_DEL$DEL_DN_PUR_VIEW:IfFU4pNtPHpckeUcyPRAU.json";
        Resource resource = new ClassPathResource(filePath);
        JsonNode viewContent = JsonUtil.INDENT.getObjectMapper().readTree(resource.getInputStream());

        DataManagerView dataManagerView = new DataManagerView();
        dataManagerView.setKey("SCM_DEL$DEL_DN_PUR_VIEW-a0aqP5Uhu3wEg5yNM9lew");
        dataManagerView.setTitle("基于DN退货选择交货单");
        dataManagerView.setType(DataManagerView.ViewType.CUSTOM);
        dataManagerView.setContent((ObjectNode) viewContent);
        Collection<StandardPermissionMigrateForSingleModuleTask.Result> results = standardPermissionMigrateForSingleModuleTask.handleSingleView(dataManagerView);
        System.out.println(JsonUtil.toIndentJson(results));
        Assertions.assertTrue(CollectionUtils.isNotEmpty(results));
    }

    @SneakyThrows
    @Test
    public void testHandleSingleView_3() {
        String filePath = "scene/view/ERP_SCM$sls_so_copy:Y5hOrMAQVYNkMNbRz_J37.json";
        DataManagerView dataManagerView = DataLoader.loadByTypeReference(filePath, new TypeReference<>() {
        });

        Mockito.when(permissionKeyInitializer.createSemanticPermission(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenAnswer(invocationOnMock -> {
                    String modelKey = invocationOnMock.getArgument(0, String.class);
                    String label = invocationOnMock.getArgument(2, String.class);
                    String parentKey = invocationOnMock.getArgument(3, String.class);
                    String keyHash = Hashing.sha256().hashUnencodedChars(modelKey + ":" + label).toString();
                    return KeyUtil.newKeyUnderModule(KeyUtil.moduleKey(parentKey), StringUtils.substring(keyHash, 0, 100));
                });

        Collection<StandardPermissionMigrateForSingleModuleTask.Result> results = standardPermissionMigrateForSingleModuleTask.handleSingleView(dataManagerView);

        System.out.println(JsonUtil.toIndentJson(results));
        Assertions.assertTrue(CollectionUtils.isNotEmpty(results));

        ArgumentCaptor<String> modelKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        Mockito.verify(permissionKeyInitializer, Mockito.atLeastOnce()).createSemanticPermission(modelKeyCaptor.capture(), keyCaptor.capture(), Mockito.anyString(), Mockito.anyString());

        List<String> keys = keyCaptor.getAllValues();
        Assertions.assertEquals("ERP_SCM$sls_so_head_tr", modelKeyCaptor.getAllValues().get(0));
        Assertions.assertEquals(dataManagerView.getKey(), keys.get(0));

        String keyHash = Hashing.sha256().hashUnencodedChars("ERP_SCM$sls_so_head_tr:订单选择").toString();
        String expectedViewPermKey = KeyUtil.newKeyUnderModule(KeyUtil.moduleKey("ERP_SCM$sls_so_head_tr"), StringUtils.substring(keyHash, 0, 100));
        Assertions.assertEquals(expectedViewPermKey, results.iterator().next().getNewPermKey());

        List<String> bindServiceOrOpenViewButtonKeys = Arrays.asList(
                "ERP_SCM$sls_so_copy_rev-uUblspC48MdZ57ZWBhKCo",
                "ERP_SCM$sls_so_copy-9kpfcxXqpezBy-AHszevV",
                "ERP_SCM$sls_so_copy_rev-mf3BZ7Jk7TdcRI8epyFB_",
                "ERP_SCM$sls_so_copy-TzFRko4NDe6Zr45wE0Qhj",
                "ERP_SCM$sls_so_copy-FV9U1-SoMDzpntWseaTFh",
                "ERP_SCM$sls_so_copy_rev-EoIIO2oCgH8aIGvYpjgbG"
        );
        keys.remove("ERP_SCM$sls_so_copy:Y5hOrMAQVYNkMNbRz_J37");
        Assertions.assertLinesMatch(bindServiceOrOpenViewButtonKeys, keys);
    }
}
