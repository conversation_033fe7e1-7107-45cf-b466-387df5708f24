package io.terminus.trantor2.permission.management.impl.task.analysis;

import io.terminus.trantor2.common.utils.StandardPermissionKeyHelper;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.index.MetaIndexRef;
import io.terminus.trantor2.meta.index.MetaIndexRefRepo;
import io.terminus.trantor2.meta.index.RefCond;
import io.terminus.trantor2.meta.management.task.BaseTask;
import io.terminus.trantor2.meta.management.task.TaskContext;
import io.terminus.trantor2.meta.management.task.TaskService;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.service.MenuConsoleQueryService;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import io.terminus.trantor2.task.TaskOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/8/28 17:14
 **/
@RequiredArgsConstructor
@TaskService(displayName = "权限分析报告")
public class PermissionAnalysisReportTask extends BaseTask<PermissionAnalysisReportTask.Options> {

    private final MetaIndexAssetRepo metaIndexAssetRepo;
    private final MetaIndexRefRepo metaIndexRefRepo;
    protected final ViewRepo viewRepo;
    protected final ServiceRepo serviceRepo;
    protected final MenuConsoleQueryService menuConsoleQueryService;

    @Override
    public void exec(Options opts, TaskOutput output, TaskContext ctx) {
        Statistics report = new Statistics();

        // 统计权限项总数
        List<MetaIndexAsset.Key> allPermissionList = metaIndexAssetRepo.find(ctx.getTeamId(), MetaIndexAsset.Key.class, Field.type().equal(MetaType.Permission.name()));
        report.setTotal(allPermissionList.size());
        Set<String> allPermKeys = allPermissionList.stream().map(MetaIndexAsset.Key::getKey).collect(Collectors.toSet());

        // 统计标准化权限总数
        Set<String> stdPermKeys = allPermKeys.stream().filter(StandardPermissionKeyHelper::isStandard).collect(Collectors.toSet());
        generateStatistic(ctx, report.getStandard(), stdPermKeys);

        // 统计语义化权限总数
        List<String> semanticPermKeys = findAllSemanticPermissionKey(ctx);
        generateStatistic(ctx, report.getSemantic(), semanticPermKeys);

        // 非语义化权限数据统计
        Collection<String> permKeysWithoutSemantic = CollectionUtils.subtract(allPermKeys, semanticPermKeys);
        generateStatistic(ctx, report.getNonSemantic(), permKeysWithoutSemantic);

        output.resultData(report);
    }

    private <T extends Statistics.Base> void generateStatistic(TaskContext ctx, T count, Collection<String> permissionKeys) {
        if (CollectionUtils.isEmpty(permissionKeys)) {
            return;
        }
        count.setTotal(permissionKeys.size());

        for (String permKey : permissionKeys) {
            count.getModuleDistributed().compute(KeyUtil.moduleKey(permKey), (key, oldValue) -> Objects.isNull(oldValue) ? 1 : oldValue + 1);
        }

        List<MetaIndexRef> refs = metaIndexRefRepo.find(ctx.getTeamId(), RefCond.builder().targetKey(permissionKeys).build());
        for (MetaIndexRef ref : refs) {
            count.getMetaRefDistributed().compute(ref.getSourceType(), (key, oldValue) -> Objects.isNull(oldValue) ? 1 : oldValue + 1);
            if (ref.getSourceType().equals(MetaType.ServiceDefinition.name())) {
                if (KeyUtil.isSysShortKey(ref.getSourceKey())) {
                    count.getServiceRefDistributed().compute("systemService", (key, oldValue) -> Objects.isNull(oldValue) ? 1 : oldValue + 1);
                } else if (StandardPermissionKeyHelper.ServiceKey.isStandard(ref.getSourceKey())) {
                    count.getServiceRefDistributed().compute("standardService", (key, oldValue) -> Objects.isNull(oldValue) ? 1 : oldValue + 1);
                } else {
                    count.getServiceRefDistributed().compute("customizedService", (key, oldValue) -> Objects.isNull(oldValue) ? 1 : oldValue + 1);
                }
            }
        }
    }

    private List<String> findAllSemanticPermissionKey(TaskContext ctx) {
        List<MetaIndexAsset.Key> keys = metaIndexAssetRepo.find(
                ctx.getTeamId(),
                MetaIndexAsset.Key.class,
                Field.type().equal(MetaType.Permission.name())
                        .and(Field.props(String.class, "sourceModelKey").isNotNull())
        );
        return keys.stream().map(MetaIndexAsset.Key::getKey).collect(Collectors.toList());
    }

    @Data
    class Statistics {

        private long total;
        // 标准化权限数据统计
        private Standard standard = new Standard();
        // 语义化权限数据统计（标准化属于语义化子类）
        private Semantic semantic = new Semantic();
        // 非语义化权限数据统计
        private NonSemantic nonSemantic = new NonSemantic();

        @Data
        static class Base {
            private long total;
            // 模块分布情况
            private Map<String, Integer> moduleDistributed = new HashMap<>();
            // 元数据对象引用分布情况
            private Map<String, Integer> metaRefDistributed = new HashMap<>();
            // 服务对象引用分布情况
            private Map<String, Integer> serviceRefDistributed = new HashMap<>();
        }

        static class Standard extends Base {
        }

        static class Semantic extends Base {
        }

        static class NonSemantic extends Base {
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static final class Options extends BaseTask.Options {
    }
}
