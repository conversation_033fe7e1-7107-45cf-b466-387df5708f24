package io.terminus.trantor2.permission.management.impl.util;

import com.fasterxml.jackson.core.JsonPointer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.utils.StandardPermissionKeyHelper;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 前端按钮组件的actions解析工具
 *
 * <AUTHOR>
 * 2025/7/30 10:38
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ViewJsonExtractor {

    public static class ViewContent {

        public static Map<String, Collection<String>> getModelKeyToButtonKeys(@NotNull JsonNode viewContent) {
            List<String> supportCompTypes = Arrays.asList("Button", "ImportButton", "ExportButton", "Print", "ApprovalInstanceBtn");
            Map<String, Collection<String>> modelKeyToButtonKey = new HashMap<>();
            List<JsonNode> parents = viewContent.findParents("name");
            for (JsonNode parent : parents) {
                if (isDataContainer(parent)) {
                    String modelKey = getModelKey(parent.path("props"));
                    if (StringUtils.isBlank(modelKey)) {
                        continue;
                    }
                    modelKeyToButtonKey.putIfAbsent(modelKey, new HashSet<>());
                    List<JsonNode> children = parent.findParents("name");
                    for (JsonNode child : children) {
                        if (supportCompTypes.contains(child.path("name").textValue())) {
                            modelKeyToButtonKey.get(modelKey).add(child.path("key").textValue());
                        }
                    }
                }
            }
            return modelKeyToButtonKey;
        }

        public static Map<String, String> getButtonKeyToModelKey(@NotNull JsonNode viewContent) {
            Map<String, Collection<String>> modelKeyToButtonKeys = getModelKeyToButtonKeys(viewContent);
            Map<String, String> buttonKeyToModelKey = new HashMap<>();
            modelKeyToButtonKeys.forEach((modelKey, buttonKeys) -> {
                if (CollectionUtils.isEmpty(buttonKeys)) {
                    return;
                }
                for (String buttonKey : buttonKeys) {
                    buttonKeyToModelKey.put(buttonKey, modelKey);
                }
            });
            return buttonKeyToModelKey;
        }

        public static String getViewMainModelKey(@NotNull JsonNode viewContent) {
            Map<String, Collection<String>> modelKeyToButtonKeys = getModelKeyToButtonKeys(viewContent);
            Set<String> modelKeys = modelKeyToButtonKeys.keySet();
            if (CollectionUtils.isEmpty(modelKeys)) {
                return null;
            }
            if (modelKeys.size() == 1) {
                return modelKeys.iterator().next();
            }
            return modelKeyToButtonKeys.entrySet()
                    .stream()
                    .max(Comparator.comparingInt(e -> CollectionUtils.size(e.getValue())))
                    .map(Map.Entry::getKey)
                    .orElse(null);
        }

        private static List<String> getContainerModelKeys(@NotNull JsonNode viewContent) {
            List<String> modelKeys = new ArrayList<>();
            List<JsonNode> parents = viewContent.findParents("name");
            for (JsonNode parent : parents) {
                String modelKey = getContainerModelKey(parent);
                if (StringUtils.isNotBlank(modelKey)) {
                    modelKeys.add(modelKey);
                }
            }
            return modelKeys;
        }

        private static String getContainerModelKey(@NotNull JsonNode container) {
            if (container.isMissingNode()) {
                return null;
            }
            if (isDataContainer(container)) {
                return getModelKey(container.path("props"));
            }
            return null;
        }

        private static boolean isDataContainer(JsonNode node) {
            String name = node.path("name").textValue();
            return "Table".equalsIgnoreCase(name)
                    || "Detail".equalsIgnoreCase(name)
                    || "FormGroup".equalsIgnoreCase(name)
                    || "FormList".equalsIgnoreCase(name)
                    || "Tree".equalsIgnoreCase(name)
                    || "CardList".equalsIgnoreCase(name)
                    || "ReportPage".equalsIgnoreCase(name);
        }
    }

    public static class Button {

        private static final JsonPointer actionConfigTargetJsonPointer = JsonPointer.compile("/config/target");
        private static final JsonPointer actionOpenViewConfigPageKeyJsonPointer = JsonPointer.compile("/page/key");
        private static final Set<String> PAGE_JUMP_TARGETS = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("list", "new", "show", "edit")));

        public static boolean isBindServiceOrOpenView(JsonNode buttonJsonNode) {
            if (buttonJsonNode == null || buttonJsonNode.isMissingNode()) {
                return false;
            }
            return hasServiceOrServiceKey(buttonJsonNode)       // 1. 检查 service 和 serviceKey
                    || hasValidOpenViewConfig(buttonJsonNode)   // 2. 检查 openViewConfig
                    || hasValidPageJumpAction(buttonJsonNode);   // 3. 检查 actions/action
        }

        private static boolean hasServiceOrServiceKey(JsonNode buttonJsonNode) {
            // 使用 findPath 替代 findValues，更高效
            JsonNode serviceNode = buttonJsonNode.findPath("service");
            if (!serviceNode.isMissingNode()) {
                return true;
            }
            JsonNode serviceKeyNode = buttonJsonNode.findPath("serviceKey");
            return !serviceKeyNode.isMissingNode();
        }

        private static boolean hasValidOpenViewConfig(JsonNode buttonJsonNode) {
            List<JsonNode> openViewConfigList = buttonJsonNode.findValues("openViewConfig");
            for (JsonNode openViewConfig : openViewConfigList) {
                JsonNode pageKey = openViewConfig.at(actionOpenViewConfigPageKeyJsonPointer);
                if (!pageKey.isMissingNode() && pageKey.isTextual() && StringUtils.isNotBlank(pageKey.textValue())) {
                    return true;
                }
            }
            return false;
        }

        private static boolean hasValidPageJumpAction(JsonNode buttonJsonNode) {
            JsonNode actions = buttonJsonNode.findPath("actions");
            if (!actions.isMissingNode() && actions.isArray()) {
                for (JsonNode action : actions) {
                    JsonNode actionType = action.path("type");
                    if (actionType.isMissingNode() || !actionType.isTextual() || !"PageJump".equalsIgnoreCase(actionType.textValue())) {
                        continue;
                    }
                    JsonNode actionConfigTarget = action.at(actionConfigTargetJsonPointer);
                    if (!actionConfigTarget.isMissingNode() && actionConfigTarget.isTextual()) {
                        String targetValue = actionConfigTarget.textValue().toLowerCase();
                        if (PAGE_JUMP_TARGETS.contains(targetValue)) {
                            return true;
                        }
                    }
                }
            }

            List<JsonNode> actionParentList = buttonJsonNode.findParents("action");
            for (JsonNode actionParent : actionParentList) {
                JsonNode action = actionParent.path("action");
                if (action.isMissingNode() || !action.isTextual() || !"PageJump".equalsIgnoreCase(action.textValue())) {
                    continue;
                }
                JsonNode target = actionParent.path("target");
                if (!target.isMissingNode() && target.isTextual()) {
                    String targetValue = target.textValue().toLowerCase();
                    if (PAGE_JUMP_TARGETS.contains(targetValue)) {
                        return true;
                    }
                }
            }
            return false;
        }

        public static String getStdPermKeyByBtnKeyOrLabel(String modelKey, String btnKey, String btnLabel) {
            if (StringUtils.isBlank(modelKey)) {
                return null;
            }
            StandardPermissionKeyHelper.Operate operate = BtnToOperate.getOperate(btnKey, btnLabel);
            return Objects.isNull(operate) ? null : operate.getPermissionKey(modelKey);
        }

        @AllArgsConstructor
        @Getter
        enum BtnToOperate {
            VIEW("(.+)-button-view", Collections.singletonList("查看"), StandardPermissionKeyHelper.Operate.VIEW),
            CREATE("(.+)-(button-add|new)", Arrays.asList("新建", "新增", "创建", "参考创建"), StandardPermissionKeyHelper.Operate.CREATE),
            COPY("(.+)-(action|actions|header|footer)-copy", Collections.singletonList("复制"), StandardPermissionKeyHelper.Operate.CREATE),
            DELETE("(.+)-(action|actions|header|footer|batch)-delete", Collections.singletonList("删除"), StandardPermissionKeyHelper.Operate.DELETE),
            BATCH_DELETE("(.+)-multi-delete", Collections.singletonList("批量删除"), StandardPermissionKeyHelper.Operate.DELETE),
            EDIT("(.+)-(action|actions|header|footer)-edit", Collections.singletonList("编辑"), StandardPermissionKeyHelper.Operate.MODIFY),
            MODIFY("(.+)-(action|actions|header|footer)-edit", Collections.singletonList("修改"), StandardPermissionKeyHelper.Operate.MODIFY),
            SAVE("(.+)-(action|actions|header|footer)-save", Collections.singletonList("保存"), StandardPermissionKeyHelper.Operate.MODIFY),
            AUDIT("", Collections.singletonList("审核"), StandardPermissionKeyHelper.Operate.AUDIT),
            UNAUDIT("", Collections.singletonList("反审核"), StandardPermissionKeyHelper.Operate.UNAUDIT),
            ENABLE("(.+)-(action|actions|header|footer|batch)-enable", Collections.singletonList("启用"), StandardPermissionKeyHelper.Operate.ENABLE),
            BATCH_ENABLE("(.+)-multi-start", Collections.singletonList("批量启用"), StandardPermissionKeyHelper.Operate.ENABLE),
            DISABLE("(.+)-(action|actions|header|footer|batch)-(disable|inactive)", Collections.singletonList("停用"), StandardPermissionKeyHelper.Operate.DISABLE),
            BATCH_DISABLE("(.+)-multi-stop", Collections.singletonList("批量停用"), StandardPermissionKeyHelper.Operate.DISABLE),
            ;

            private final String keyRegex;
            private final Collection<String> buttonLabels;
            private final StandardPermissionKeyHelper.Operate operate;

            public static StandardPermissionKeyHelper.Operate getOperate(String btnKey, String btnLabel) {
                // 优先尝试匹配按钮key，再尝试匹配按钮name
                return Arrays.stream(BtnToOperate.values())
                        .filter(btnToOperate -> StringUtils.isNotBlank(btnToOperate.getKeyRegex()) && Pattern.matches(btnToOperate.getKeyRegex(), btnKey))
                        .findFirst()
                        .map(BtnToOperate::getOperate)
                        .orElseGet(() -> Arrays.stream(BtnToOperate.values())
                                .filter(btnToOperate -> btnToOperate.getButtonLabels().contains(btnLabel))
                                .findFirst()
                                .map(BtnToOperate::getOperate)
                                .orElse(null));
            }
        }
    }

    public static String getModelKeyOrDefault(JsonNode props, String defaultModelKey) {
        String modelKey;
        return Objects.nonNull(modelKey = getModelKey(props)) ? modelKey : defaultModelKey;
    }

    public static String getModelKey(JsonNode props) {
        if (Objects.isNull(props)) {
            return null;
        }
        if (props.hasNonNull("modelAlias")) {
            return props.path("modelAlias").textValue();
        } else if (props.hasNonNull("modelKey")) {
            return props.path("modelKey").textValue();
        }
        return null;
    }

    public static List<String> extractActionTypes(JsonNode root) {
        return findActionTypesRecursive(root);
    }

    private static List<String> findActionTypesRecursive(JsonNode jsonNode) {
        if (Objects.isNull(jsonNode) || jsonNode.isMissingNode()) return Collections.emptyList();
        List<String> actionTypes = new ArrayList<>();
        if (jsonNode.isArray()) {
            for (JsonNode element : jsonNode) {
                actionTypes.addAll(findActionTypesRecursive(element));
            }
        } else if (jsonNode.isObject()) {
            actionTypes.addAll(findActionTypesRecursive((ObjectNode) jsonNode));
        }
        return actionTypes;
    }

    private static List<String> findActionTypesRecursive(ObjectNode objectNode) {
        List<String> actionTypes = new ArrayList<>();
        // 如果是 actions 数组，提取每项中的 type
        JsonNode actionsNode = objectNode.findPath("actions");
        if (!actionsNode.isMissingNode() && actionsNode.isArray()) {
            for (JsonNode action : actionsNode) {
                JsonNode typeNode = action.path("type");
                if (!typeNode.isMissingNode() && typeNode.isTextual()) {
                    if (typeNode.textValue().equalsIgnoreCase("OpenView")) {
                        if (!action.at("/config/openViewConfig/page").isMissingNode()) {
                            actionTypes.add(typeNode.textValue());
                        }
                    } else {
                        actionTypes.add(typeNode.textValue());
                    }
                }
            }

            // props.actionConfig.endLogicOtherConfig
            JsonNode endLogicOtherConfig = objectNode.findPath("endLogicOtherConfig");
            if (!endLogicOtherConfig.isMissingNode() && endLogicOtherConfig.isArray()) {
                for (JsonNode config : endLogicOtherConfig) {
                    JsonNode action = config.path("action");
                    if (!action.isMissingNode() && action.isTextual()) {
                        if (action.textValue().equalsIgnoreCase("OpenView")) {
                            if (!config.at("/openViewConfig/page").isMissingNode()) {
                                actionTypes.add(action.textValue());
                            }
                        } else {
                            actionTypes.add(action.textValue());
                        }
                    }
                }
            }
        }
        return actionTypes;
    }

    private static void findActionTypesRecursive(JsonNode objectNode, Collection<String> foundSoFar) {
        if (objectNode == null || objectNode.isMissingNode()) return;

        if (objectNode.isArray()) {
            for (JsonNode child : objectNode) {
                findActionTypesRecursive(child, foundSoFar);
            }
        } else if (objectNode.isObject()) {
            // 如果是 actions 数组，提取每项中的 type
            JsonNode actionsNode = objectNode.path("actions");
            if (!actionsNode.isMissingNode() && actionsNode.isArray()) {
                for (JsonNode action : actionsNode) {
                    JsonNode typeNode = action.path("type");
                    if (!typeNode.isMissingNode() && typeNode.isTextual()) {
                        if (typeNode.textValue().equalsIgnoreCase("OpenView")) {
                            if (!action.at("/config/openViewConfig/page").isMissingNode()) {
                                foundSoFar.add(typeNode.textValue());
                            }
                        } else {
                            foundSoFar.add(typeNode.textValue());
                        }
                    }
                    // 继续递归提取嵌套的 actions
                    findActionTypesRecursive(action, foundSoFar);
                }
            }

            // 遍历所有字段继续递归
            Iterator<String> fieldNames = objectNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                findActionTypesRecursive(objectNode.get(fieldName), foundSoFar);
            }
        }
    }
}
