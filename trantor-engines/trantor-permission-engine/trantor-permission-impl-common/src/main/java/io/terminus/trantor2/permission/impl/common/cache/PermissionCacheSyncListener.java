package io.terminus.trantor2.permission.impl.common.cache;

import io.terminus.trantor2.module.adapter.PortalPermissionCache;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheEvent;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.cache.ResourcePermissionCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * 2023/7/6 8:07 下午
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionCacheSyncListener implements MessageListener<PermissionCacheEvent> {

    private final ResourcePermissionCache resourcePermissionCache;
    private final PortalToIamAppConverter portalToIamAppConverter;

    @Override
    public void onMessage(CharSequence charSequence, PermissionCacheEvent event) {
        log.info("received authorization change event [{}]", event);
        if (Objects.nonNull(event)) {
            if (Boolean.TRUE.equals(event.getClearAll())) {
                resourcePermissionCache.invalidateAll();
                PortalPermissionCache.invalidateAllPortalPermissionCache();
            } else {
                Long userId = event.getUserId();
                Long portalId = event.getPortalId();
                if (Objects.nonNull(userId) && Objects.nonNull(portalId)) {
                    resourcePermissionCache.invalidateByUserAndPortal(userId, portalId);
                    PortalPermissionCache.invalidatePortalPermissionCacheByEndpoint(portalToIamAppConverter.getIamAppIdByPortalId(portalId));
                } else if (Objects.nonNull(portalId)) {
                    resourcePermissionCache.invalidatePortalCache(portalId);
                    PortalPermissionCache.invalidatePortalPermissionCacheByEndpoint(portalToIamAppConverter.getIamAppIdByPortalId(portalId));
                } else if (Objects.nonNull(userId)) {
                    resourcePermissionCache.invalidateUserCache(userId);
                }
            }
        }
    }
}
