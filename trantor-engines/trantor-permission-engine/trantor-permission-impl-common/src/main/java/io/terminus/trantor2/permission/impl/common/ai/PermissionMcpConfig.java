package io.terminus.trantor2.permission.impl.common.ai;

import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.impl.common.ai.mcp.tools.PermissionMcpTools;
import io.terminus.trantor2.permission.impl.common.ai.mcp.tools.PortalMcpTools;
import io.terminus.trantor2.permission.impl.common.ai.mcp.tools.UserMcpTools;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 权限引擎MCP配置类
 *
 * <AUTHOR>
 * 2025/6/19 16:40
 **/
@Configuration
public class PermissionMcpConfig {

    @Bean
    public ToolCallbackProvider permissionMcpToolCallbackProvider(PermissionMcpTools permissionMcpTools,
                                                                  UserMcpTools userMcpTools,
                                                                  PortalMcpTools portalMcpTools) {
        return MethodToolCallbackProvider.builder().toolObjects(permissionMcpTools, userMcpTools, portalMcpTools).build();
    }

    @Bean
    public PermissionMcpTools permissionMcpTools(MetaIndexAssetRepo metaIndexAssetRepo,
                                                 ServiceRepo serviceRepo,
                                                 ViewRepo viewRepo,
                                                 MenuQueryService menuQueryService,
                                                 MetaQueryService metaQueryService,
                                                 PortalToIamAppConverter portalToIamAppConverter,
                                                 TrantorIAMPermissionAssignService iamPermissionAssignService) {
        return new PermissionMcpTools(metaIndexAssetRepo, serviceRepo, viewRepo, menuQueryService, metaQueryService, portalToIamAppConverter, iamPermissionAssignService);
    }

    @Bean
    public UserMcpTools userMcpTools(TrantorIAMUserService iamUserService) {
        return new UserMcpTools(iamUserService);
    }

    @Bean
    public PortalMcpTools portalMcpTools(ModuleRepo moduleRepo,
                                         ConfigurationService configurationService,
                                         MenuQueryService menuQueryService) {
        return new PortalMcpTools(moduleRepo, configurationService, menuQueryService);
    }
}
