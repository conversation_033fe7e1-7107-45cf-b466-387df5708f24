package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.enums.permission.AuthorizationEffect;
import io.terminus.iam.api.response.permission.Permission;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.module.util.ModuleUtil;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import org.apache.commons.collections4.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 权限工具类
 * <p>
 * 该类处于 trantor-xxx-impl-common 模块下，所以类中方法必须是 management 和 runtime 都要用到的，
 * 仅有一方使用的类或方法，应该在各自的 trantor-xxx-management-impl 或 trantor-xxx-runtime-impl 模块下单独定义。
 */
public class PermissionUtils {

    public static final String PERMISSION_GROUP_KEY_CONNECT = "_";

    private PermissionUtils() {
    }

    public static String buildPermissionGroupKey(String... keys) {
        StringBuilder groupKey = new StringBuilder();
        for (int i = 0; i < keys.length; i++) {
            if (i != keys.length - 1) {
                groupKey.append(keys[i]).append(PERMISSION_GROUP_KEY_CONNECT);
                continue;
            }
            groupKey.append(keys[i]);
        }
        return groupKey.toString();
    }

    /**
     * 构建数据权限PermissionGroupKey
     *
     * @param moduleKey 模块key
     * @param menuKey   菜单key
     * @param viewKey   视图key
     * @return 数据权限PermissionGroupKey
     */
    public static String buildDataPermissionGroupKey(String moduleKey, String menuKey, String viewKey) {
        StringJoiner stringJoiner = new StringJoiner(PERMISSION_GROUP_KEY_CONNECT);
        if (Objects.nonNull(moduleKey)) {
            stringJoiner.add(moduleKey);
        }
        if (Objects.nonNull(menuKey)) {
            stringJoiner.add(menuKey);
        }
        if (Objects.nonNull(viewKey)) {
            stringJoiner.add(viewKey);
        }
        return stringJoiner.toString();
    }

    /**
     * 构建数据权限的鉴权范围key
     *
     * @param moduleKey 模块key
     * @param menuKey   菜单key
     * @param viewKey   视图key
     * @return 鉴权范围key
     */
    public static String buildDataPermissionAuthScopeKey(String moduleKey, String menuKey, String viewKey) {
        return "/moduleKey/" + moduleKey + "/menuKey/" + menuKey + "/viewKey/" + viewKey;
    }

    public static String buildFunctionPermissionGroup(String portalCode, String teamCode) {
        return ModuleUtil.jointKey(portalCode, teamCode);
    }

    /**
     * 判断功能权限点是否从元数据创建
     * <p>
     * Note: 目前 Trantor 权限点存在两种创建途径：
     * 1. 基于菜单、场景元数据解析自动创建
     * 2. 基于门户下的接口访问控制功能入口手动创建
     * </p>
     *
     * @param permission 功能权限对象
     * @return 功能权限点是否从元数据创建，true：是，false：否
     */
    public static boolean isPermissionCreatedByMetadata(@NotNull Permission permission) {
        return PermissionConstants.SOURCE_TYPE_META.equals(permission.getSourceType());
    }

    /**
     * 批量查询视图引用权限项
     *
     * @param viewKeyToOwnPermissionKeyMap  视图key -> 视图自己的权限项
     * @param viewKeyToRefPermissionKeysMap 视图key -> 视图引用服务的权限项集合
     */
    public static void getViewRefPermissionKeys(List<ViewPermissionDTO> viewPermissions,
                                                Map<String, String> viewKeyToOwnPermissionKeyMap,
                                                Map<String, Set<String>> viewKeyToRefPermissionKeysMap) {
        for (ViewPermissionDTO viewPermission : viewPermissions) {
            if (Objects.isNull(viewPermission)) continue;
            // 视图自身权限
            viewKeyToOwnPermissionKeyMap.put(viewPermission.getViewKey(), viewPermission.getPermissionKey());
            // 视图引用服务/接口的权限项
            Set<String> refPermissionKeys = new HashSet<>();
            viewKeyToRefPermissionKeysMap.put(viewPermission.getViewKey(), refPermissionKeys); // quick init
            List<ViewPermissionDTO.ComponentPermissionDTO> comps = viewPermission.getComponents();
            if (CollectionUtils.isEmpty(comps)) {
                continue;
            }
            // 视图内部非按钮权限
            for (ViewPermissionDTO.ComponentPermissionDTO comp : comps) {
                if (Objects.isNull(comp)) continue;
                if (!ACLResourceType.Button.name().equals(comp.getResourceType()) && CollectionUtils.isNotEmpty(comp.getAllPermissionKeys())) {
                    refPermissionKeys.addAll(comp.getAllPermissionKeys());
                }
                // virtual components
                for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComp : comp.getVirtualComponents()) {
                    if (Objects.isNull(virtualComp)) continue;
                    if (!ACLResourceType.Button.name().equals(virtualComp.getResourceType()) && CollectionUtils.isNotEmpty(virtualComp.getAllPermissionKeys())) {
                        refPermissionKeys.addAll(virtualComp.getAllPermissionKeys());
                    }
                }
            }
        }
    }

    /**
     * 批量查询视图所有按钮引用权限项
     *
     * @param viewKeyToButtonKeyToOwnPermissionKeyMap  按钮key -> 按钮自己的权限项
     * @param viewKeyToButtonKeyToRefPermissionKeysMap 按钮key -> 按钮引用服务的权限项集合
     */
    public static void getButtonRefPermissionKeys(List<ViewPermissionDTO> viewPermissions,
                                                  Map<String, Map<String, String>> viewKeyToButtonKeyToOwnPermissionKeyMap,
                                                  Map<String, Map<String, Set<String>>> viewKeyToButtonKeyToRefPermissionKeysMap) {
        for (ViewPermissionDTO viewPermission : viewPermissions) {
            if (Objects.isNull(viewPermission)) continue;
            List<ViewPermissionDTO.ComponentPermissionDTO> comps = viewPermission.getComponents();
            if (CollectionUtils.isEmpty(comps)) {
                continue;
            }
            String viewKey = viewPermission.getViewKey();
            viewKeyToButtonKeyToOwnPermissionKeyMap.putIfAbsent(viewKey, new HashMap<>());
            viewKeyToButtonKeyToRefPermissionKeysMap.putIfAbsent(viewKey, new HashMap<>());
            // 视图内部非按钮权限
            for (ViewPermissionDTO.ComponentPermissionDTO comp : comps) {
                if (Objects.isNull(comp)) continue;
                if (ACLResourceType.Button.name().equals(comp.getResourceType())) {
                    // 构建组件自身的权限项映射关系
                    viewKeyToButtonKeyToOwnPermissionKeyMap.get(viewKey).put(comp.getCompKey(), comp.getCompPermissionKey());
                    if (CollectionUtils.isNotEmpty(comp.getAllPermissionKeys())) {
                        // 构建组件引用服务的权限项映射关系
                        comp.getAllPermissionKeys().remove(comp.getCompPermissionKey());    // 移除组件自身的权限项
                        if (CollectionUtils.isNotEmpty(comp.getAllPermissionKeys())) {
                            viewKeyToButtonKeyToRefPermissionKeysMap.get(viewKey).put(comp.getCompKey(), new HashSet<>(comp.getAllPermissionKeys()));
                        }
                    }
                }
                // virtual components
                for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComp : comp.getVirtualComponents()) {
                    if (Objects.isNull(virtualComp)) continue;
                    if (ACLResourceType.Button.name().equals(virtualComp.getResourceType())) {
                        viewKeyToButtonKeyToOwnPermissionKeyMap.get(viewKey).put(virtualComp.getVirtualCompFullKey(), virtualComp.getCompPermissionKey());
                        if (CollectionUtils.isNotEmpty(virtualComp.getAllPermissionKeys())) {
                            virtualComp.getAllPermissionKeys().remove(virtualComp.getCompPermissionKey());  // 移除组件自身的权限项
                            if (CollectionUtils.isNotEmpty(virtualComp.getAllPermissionKeys())) {
                                viewKeyToButtonKeyToRefPermissionKeysMap.get(viewKey).put(virtualComp.getVirtualCompFullKey(), new HashSet<>(virtualComp.getAllPermissionKeys()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 单个角色的多权限授权状态决策
     * <p>
     * 1. 当存在至少一个DENIED，则最终结果为DENIED
     * 2. 当存在至少一个NONE，则最终结果为NONE
     * 3. 当且仅当所有都为ALLOW，则最终结果为ALLOW
     */
    public static AuthorizationEffect determineAuthorizationEffectWithinSingleRole(Collection<AuthorizationEffect> authorizationEffects) {
        Set<AuthorizationEffect> effects = authorizationEffects.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(effects)) {
            return AuthorizationEffect.NONE;
        }
        for (AuthorizationEffect effect : effects) {
            if (AuthorizationEffect.DENIED.equals(effect)) {
                return AuthorizationEffect.DENIED;
            }
            if (AuthorizationEffect.NONE.equals(effect)) {
                return AuthorizationEffect.NONE;
            }
        }
        return AuthorizationEffect.ALLOW;
    }

    /**
     * 多个角色的权限授权状态决策
     * <p>
     * 1. 当存在至少一个DENIED，则最终结果为DENIED
     * 2. 当存在至少一个ALLOW，则最终结果为ALLOW
     * 3. 当且仅当所有都为NONE，则最终结果为NONE
     */
    public static AuthorizationEffect determineAuthorizationEffectBetweenMultiRole(Collection<AuthorizationEffect> authorizationEffects) {
        Set<AuthorizationEffect> effects = authorizationEffects.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(effects)) {
            return AuthorizationEffect.NONE;
        }
        for (AuthorizationEffect effect : effects) {
            if (AuthorizationEffect.DENIED.equals(effect)) {
                return AuthorizationEffect.DENIED;
            }
            if (AuthorizationEffect.ALLOW.equals(effect)) {
                return AuthorizationEffect.ALLOW;
            }
        }
        return AuthorizationEffect.NONE;
    }
}

