package io.terminus.trantor2.permission.impl.common.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.trantor2.module.dto.ApplicationMenuDTO;
import io.terminus.trantor2.permission.api.common.cache.ResourcePermissionCache;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.properties.PermissionProperties;
import lombok.Getter;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 资源权限缓存
 *
 * <AUTHOR>
 * 2023/5/24 4:42 下午
 **/
@Getter
@Component
public class ResourcePermissionCacheImpl implements ResourcePermissionCache {

    // 带有授权标识的场景鉴权缓存，最大存活数默认2000条「100（用户）* 10（门户）* 20（常用菜单数）」
    private final Cache<CacheKey, List<PermissionResourceDTO>> sceneResourcePermissionCache;
    // 带有授权标识的完整门户应用菜单资源树缓存，最大存活数2000条（统一配置项）
    private final Cache<CacheKey, List<ApplicationMenuDTO>> allAppMenuPermissionCache;

    public ResourcePermissionCacheImpl(PermissionProperties permissionProperties) {
        sceneResourcePermissionCache = Caffeine.newBuilder()
            .maximumSize(permissionProperties.getPermissionCacheMaxEntrySize())
            .expireAfterAccess(permissionProperties.getPermissionCacheExpire(), TimeUnit.SECONDS).build();
        allAppMenuPermissionCache = Caffeine.newBuilder()
            .maximumSize(permissionProperties.getPermissionCacheMaxEntrySize())
            .expireAfterAccess(permissionProperties.getPermissionCacheExpire(), TimeUnit.SECONDS).build();
    }

    /**
     * 失效所有缓存
     */
    @Override
    public void invalidateAll() {
        sceneResourcePermissionCache.invalidateAll();
        allAppMenuPermissionCache.invalidateAll();
    }

    /**
     * 主动失效目标用户在指定门户下的缓存
     *
     * @param userId   用户id
     * @param portalId 门户id
     */
    @Override
    public void invalidateByUserAndPortal(@NotNull Long userId, @NotNull Long portalId) {
        List<CacheKey> cacheKeys = sceneResourcePermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_SCENE_RESOURCE, key.getCacheType())
                    && Objects.equals(portalId, key.getPortalId())
                    && Objects.equals(userId, key.getUserId());
            })
            .collect(Collectors.toList());
        sceneResourcePermissionCache.invalidateAll(cacheKeys);

        cacheKeys = allAppMenuPermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_ALL_APP_MENU, key.getCacheType())
                    && Objects.equals(portalId, key.getPortalId())
                    && Objects.equals(userId, key.getUserId());
            })
            .collect(Collectors.toList());
        allAppMenuPermissionCache.invalidateAll(cacheKeys);
    }

    /**
     * 主动失效指定工作台下指定门户的数据缓存
     *
     * @param portalId 门户id
     */
    @Override
    public void invalidatePortalCache(@NotNull Long portalId) {
        List<CacheKey> cacheKeys = sceneResourcePermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_SCENE_RESOURCE, key.getCacheType())
                    && Objects.equals(portalId, key.getPortalId());
            })
            .collect(Collectors.toList());
        sceneResourcePermissionCache.invalidateAll(cacheKeys);

        cacheKeys = allAppMenuPermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_ALL_APP_MENU, key.getCacheType())
                    && Objects.equals(portalId, key.getPortalId());
            })
            .collect(Collectors.toList());
        allAppMenuPermissionCache.invalidateAll(cacheKeys);
    }

    /**
     * 主动失效指定用户的数据缓存
     *
     * @param userId 用户id
     */
    @Override
    public void invalidateUserCache(@NotNull Long userId) {
        List<CacheKey> cacheKeys = sceneResourcePermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_SCENE_RESOURCE, key.getCacheType())
                    && Objects.equals(userId, key.getUserId());
            })
            .collect(Collectors.toList());
        sceneResourcePermissionCache.invalidateAll(cacheKeys);

        cacheKeys = allAppMenuPermissionCache.asMap().keySet().stream()
            .filter(key -> {
                return Objects.equals(CacheType.USER_ACCESSIBLE_ALL_APP_MENU, key.getCacheType())
                    && Objects.equals(userId, key.getUserId());
            })
            .collect(Collectors.toList());
        allAppMenuPermissionCache.invalidateAll(cacheKeys);
    }
}
