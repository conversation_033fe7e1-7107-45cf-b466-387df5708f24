package io.terminus.trantor2.permission.impl.common.util;

import cn.hutool.core.util.NumberUtil;
import io.terminus.iam.api.dto.condition.Condition;
import io.terminus.iam.api.dto.condition.ConditionGroup;
import io.terminus.iam.api.dto.condition.ConditionLogicalOperator;
import io.terminus.iam.api.dto.condition.ConditionOperator;
import io.terminus.iam.api.dto.condition.ConditionValue;
import io.terminus.iam.api.dto.condition.ConditionValueType;
import io.terminus.iam.api.dto.condition.SingleCondition;
import io.terminus.iam.api.dto.condition.VariableValue;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.permission.api.common.dto.SelectedModelColumn;
import io.terminus.trantor2.service.dsl.enums.FieldType;
import io.terminus.trantor2.service.dsl.enums.LogicOperator;
import io.terminus.trantor2.service.dsl.enums.Operator;
import io.terminus.trantor2.service.dsl.enums.ValueType;
import io.terminus.trantor2.service.dsl.properties.ConditionLeaf;
import io.terminus.trantor2.service.dsl.properties.DataControlDimensionValue;
import io.terminus.trantor2.service.dsl.properties.RelatedModel;
import io.terminus.trantor2.service.dsl.properties.Value;
import io.terminus.trantor2.service.dsl.properties.VarValue;
import org.apache.commons.collections4.CollectionUtils;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * trantor2 service condition 和 iam condition 转换工具
 *
 * <AUTHOR>
 * 2023/6/21 1:53 下午
 **/
public class IamConditionUtils {

    /**
     * 区间数值文本的正则表达式
     */
    private static final Pattern RANGE_NUMBER_REGEX = Pattern.compile("\\[([-+]?\\d+(\\.\\d+)?),([-+]?\\d+(\\.\\d+)?)\\]");

    private IamConditionUtils() {
    }

    public static void markConditionToIgnored(Collection<? extends Condition> conditions,
                                              Collection<String> ignoredConditionKeys) {
        if (CollectionUtils.isEmpty(conditions) || CollectionUtils.isEmpty(ignoredConditionKeys)) {
            return;
        }
        for (Condition condition : conditions) {
            if (Objects.nonNull(condition.getKey()) && ignoredConditionKeys.contains(condition.getKey())) {
                condition.setActive(Boolean.FALSE);
            }
            if (condition instanceof ConditionGroup) {
                ConditionGroup conditionGroup = (ConditionGroup) condition;
                if (CollectionUtils.isNotEmpty(conditionGroup.getConditions())) {
                    markConditionToIgnored(conditionGroup.getConditions(), ignoredConditionKeys);
                }
            }
        }
    }

    /**
     * 获取已忽略的条件对象标识
     */
    public static void loadIgnoredConditionKeys(@Nullable Collection<? extends Condition> conditions,
                                                @NotEmpty Collection<String> ignoredConditionKeys) {
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }
        for (Condition condition : conditions) {
            if (Boolean.FALSE.equals(condition.getActive()) && Objects.nonNull(condition.getKey())) {
                ignoredConditionKeys.add(condition.getKey());
            }
            if (condition instanceof ConditionGroup) {
                ConditionGroup conditionGroup = (ConditionGroup) condition;
                if (CollectionUtils.isNotEmpty(conditionGroup.getConditions())) {
                    loadIgnoredConditionKeys(conditionGroup.getConditions(), ignoredConditionKeys);
                }
            }
        }
    }

    public static void loadKeyToConditionLeaf(@NotNull io.terminus.trantor2.service.dsl.properties.Condition condition,
                                              @NotNull Map<String, ConditionLeaf> conditionLeafMap) {
        if (condition instanceof io.terminus.trantor2.service.dsl.properties.ConditionGroup) {
            io.terminus.trantor2.service.dsl.properties.ConditionGroup conditionGroup = (io.terminus.trantor2.service.dsl.properties.ConditionGroup) condition;
            if (CollectionUtils.isNotEmpty(conditionGroup.getConditions())) {
                conditionGroup.getConditions().forEach(cond -> loadKeyToConditionLeaf(cond, conditionLeafMap));
            }
        }
        if (condition instanceof ConditionLeaf) {
            conditionLeafMap.put(condition.getId(), (ConditionLeaf) condition);
        }
    }

    public static Condition toCondition(io.terminus.trantor2.service.dsl.properties.Condition condition) {
        if (condition instanceof io.terminus.trantor2.service.dsl.properties.ConditionGroup) {
            return toConditionGroup((io.terminus.trantor2.service.dsl.properties.ConditionGroup) condition);
        } else if (condition instanceof ConditionLeaf) {
            return toSingleCondition((ConditionLeaf) condition);
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal condition type「" + condition.getClass().getName() + "」");
        }
    }

    public static ConditionGroup toConditionGroup(io.terminus.trantor2.service.dsl.properties.ConditionGroup conditionGroup) {
        if (Objects.isNull(conditionGroup) || CollectionUtils.isEmpty(conditionGroup.getConditions())) {
            return null;
        }
        ConditionLogicalOperator conditionLogicalOperator = toConditionLogicalOperator(conditionGroup.getLogicOperator());
        List<Condition> conditionList = conditionGroup.getConditions().stream()
                .map(IamConditionUtils::toCondition)
                .peek(condition -> condition.setNextLogicalOperator(conditionLogicalOperator))
                .collect(Collectors.toList());
        // 最后一个condition的逻辑运算符置空
        if (!conditionList.isEmpty()) {
            conditionList.get(conditionList.size() - 1).setNextLogicalOperator(null);
        }
        ConditionGroup group = new ConditionGroup();
        group.setKey(conditionGroup.getId());
        group.setConditions(conditionList);
        return group;
    }

    public static SingleCondition toSingleCondition(ConditionLeaf conditionLeaf) {
        if (Objects.isNull(conditionLeaf)) {
            return null;
        }
        SingleCondition singleCondition = new SingleCondition();
        singleCondition.setKey(conditionLeaf.getId());
        // 条件左值
        VarValue leftValue = conditionLeaf.getLeftValue();
        List<SelectedModelColumn> selectedModelColumnList = leftValue.getVarValue().stream().map(varStage -> {
            SelectedModelColumn selectedModelColumn = new SelectedModelColumn();
            Optional<RelatedModel> relatedModelOptional = Optional.ofNullable(varStage.getRelatedModel());
            selectedModelColumn.setModelKey(relatedModelOptional.map(RelatedModel::getModelKey).orElse(varStage.getModelAlias()));
            selectedModelColumn.setModelName(relatedModelOptional.map(RelatedModel::getModelName).orElse(null));
            selectedModelColumn.setColumnKey(varStage.getValueKey());
            selectedModelColumn.setColumnName(varStage.getValueName());
            selectedModelColumn.setColumnType(leftValue.getFieldType().name());
            return selectedModelColumn;
        }).collect(Collectors.toList());
        SelectedModelColumn selectedModelColumn = SelectedModelColumn.fromList(selectedModelColumnList);
        selectedModelColumn.setLeftValue(leftValue);
        singleCondition.setParam(JsonUtil.toJson(selectedModelColumn));
        // 关系运算符
        singleCondition.setOperator(toRelationalOperator(conditionLeaf.getOperator()));
        // 条件右值
        if (CollectionUtils.isNotEmpty(conditionLeaf.getRightValues()) || Objects.nonNull(conditionLeaf.getRightValue())) {
            if (CollectionUtils.isNotEmpty(conditionLeaf.getRightValues())) {
                List<ConditionValue> conditionValues = conditionLeaf.getRightValues().stream()
                        .map(varValue -> toConditionValue(leftValue.getFieldType(), varValue))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(conditionValues)) {
                    conditionValues.forEach(conditionValue -> parseRangeText(conditionLeaf.getOperator(), conditionValue));
                    singleCondition.setConditionValues(conditionValues);
                }
            } else if (Objects.nonNull(conditionLeaf.getRightValue())) {
                ConditionValue conditionValue = toConditionValue(leftValue.getFieldType(), conditionLeaf.getRightValue());
                if (Objects.nonNull(conditionValue)) {
                    parseRangeText(conditionLeaf.getOperator(), conditionValue);
                    singleCondition.setConditionValues(Collections.singletonList(conditionValue));
                }
            }
        }
        return singleCondition;
    }

    public static io.terminus.trantor2.service.dsl.properties.Condition toCondition(Condition condition) {
        if (condition.conditionType().equals(Condition.ConditionType.CONDITION_GROUP)) {
            return toConditionGroup((ConditionGroup) condition);
        } else if (condition.conditionType().equals(Condition.ConditionType.SINGLE_CONDITION)) {
            return toConditionLeaf((SingleCondition) condition);
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal conditionType「" + condition.conditionType() + "」");
        }
    }

    public static io.terminus.trantor2.service.dsl.properties.ConditionGroup toConditionGroup(ConditionGroup conditionGroup) {
        if (Objects.isNull(conditionGroup) || CollectionUtils.isEmpty(conditionGroup.getConditions())) {
            return null;
        }
        io.terminus.trantor2.service.dsl.properties.ConditionGroup group = new io.terminus.trantor2.service.dsl.properties.ConditionGroup();
        group.setId(conditionGroup.getKey());
        List<io.terminus.trantor2.service.dsl.properties.Condition> conditionList = conditionGroup.getConditions().stream()
                .map(IamConditionUtils::toCondition)
                .collect(Collectors.toList());
        group.setConditions(conditionList);
        LogicOperator logicOperator = toConditionLogicalOperator(conditionGroup.getConditions().get(0).getNextLogicalOperator());
        group.setLogicOperator(logicOperator);
        return group;
    }

    public static ConditionLeaf toConditionLeaf(SingleCondition singleCondition) {
        if (Objects.isNull(singleCondition)) {
            return null;
        }
        ConditionLeaf conditionLeaf = new ConditionLeaf();
        conditionLeaf.setId(singleCondition.getKey());
        // 条件左值
        String param = singleCondition.getParam();
        SelectedModelColumn selectedModelColumn = JsonUtil.fromJson(param, SelectedModelColumn.class);
        if (Objects.nonNull(selectedModelColumn.getLeftValue())) {
            conditionLeaf.setLeftValue(selectedModelColumn.getLeftValue());
        } else {
            List<VarValue.VarStage> varStages = SelectedModelColumn.toList(selectedModelColumn).stream().map(column -> {
                VarValue.VarStage varStage = new VarValue.VarStage();
                varStage.setModelAlias(column.getModelKey());
                varStage.setRelatedModel(new RelatedModel(column.getModelKey(), column.getModelName()));
                varStage.setValueKey(column.getColumnKey());
                varStage.setValueName(column.getColumnName());
                return varStage;
            }).collect(Collectors.toList());
            VarValue varValue = new VarValue();
            varValue.setValueType(ValueType.MODEL);
            varValue.setVarValue(varStages);
            if (Objects.nonNull(selectedModelColumn.getColumnType())) {
                varValue.setFieldType(FieldType.valueOf(selectedModelColumn.getColumnType()));
            }
            conditionLeaf.setLeftValue(varValue);
        }
        // 关系运算符
        conditionLeaf.setOperator(toRelationalOperator(singleCondition.getOperator()));
        // 条件右值
        List<ConditionValue> conditionValues = singleCondition.getConditionValues();
        if (CollectionUtils.isNotEmpty(conditionValues)) {
            List<Value> values = conditionValues.stream()
                    .map(conditionValue -> {
                        reverseParseRangeText(conditionLeaf.getOperator(), conditionValue);
                        if (conditionValue.getValueType().equals(ConditionValueType.VALUE_RANGE)) {
                            DataControlDimensionValue dataControlDimensionValue = toDataControlDimensionValue(conditionValue);
                            if (Objects.nonNull(dataControlDimensionValue)) {
                                dataControlDimensionValue.setFieldType(conditionLeaf.getLeftValue().getFieldType());
                            }
                            return dataControlDimensionValue;
                        } else {
                            VarValue varValue = toVarValue(conditionValue);
                            if (Objects.nonNull(varValue)) {
                                varValue.setFieldType(conditionLeaf.getLeftValue().getFieldType());
                            }
                            return varValue;
                        }
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(values)) {
                conditionLeaf.setRightValue(values.get(0));
            }
            conditionLeaf.setRightValues(values);
        }
        return conditionLeaf;
    }

    /**
     * 转换区间文本，由于前端条件组组件使用constValue="[x,y]"存储BETWEEN_AND区间常量值，为了方便使用，我们将文本转换为集合
     *
     * @param operator       关系运算符
     * @param conditionValue 条件值对象
     */
    public static void parseRangeText(Operator operator, ConditionValue conditionValue) {
        if (Operator.BETWEEN_AND.equals(operator)
                && conditionValue.getValueType().equals(ConditionValueType.SPECIFY_VALUE)
                && Boolean.TRUE.equals(conditionValue.getIsSingleValue())
                && CollectionUtils.isNotEmpty(conditionValue.getValues())
                && conditionValue.getValue() instanceof CharSequence) {
            Matcher matcher = RANGE_NUMBER_REGEX.matcher((CharSequence) conditionValue.getValue());
            if (matcher.matches()) {
                List<Object> values = new ArrayList<>();
                values.add(matcher.group(1));
                values.add(matcher.group(3));
                conditionValue.setValues(values);
            }
        }
    }

    /**
     * 反向解析，将区间值集合转换为"[x,y]"文本
     *
     * @param operator       关系运算符
     * @param conditionValue 条件值对象
     */
    private static void reverseParseRangeText(Operator operator, ConditionValue conditionValue) {
        if (Operator.BETWEEN_AND.equals(operator)
                && (ConditionValueType.SPECIFY_VALUE.equals(conditionValue.getValueType()))
                && CollectionUtils.isNotEmpty(conditionValue.getValues())
                && conditionValue.getValues().size() > 1) {
            List<Object> collect = conditionValue.getValues().stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (collect.size() != 2) {
                throw new TrantorRuntimeException("conditionValue range value size is more than two");
            }
            StringJoiner joiner = new StringJoiner(",", "[", "]");
            for (Object value : conditionValue.getValues()) {
                joiner.add(value.toString());
            }
            conditionValue.setValues(Collections.singletonList(joiner.toString()));
        }
    }

    private static ConditionOperator toRelationalOperator(Operator operator) {
        if (Objects.isNull(operator)) {
            return null;
        }
        switch (operator) {
            case EQ:
                return ConditionOperator.EQ;
            case NEQ:
                return ConditionOperator.NEQ;
            case GT:
                return ConditionOperator.GT;
            case GTE:
                return ConditionOperator.GTE;
            case LT:
                return ConditionOperator.LT;
            case LTE:
                return ConditionOperator.LTE;
            case START_WITH:
                return ConditionOperator.START_WITH;
            case END_WITH:
                return ConditionOperator.END_WITH;
            case CONTAINS:
                return ConditionOperator.CONTAINS;
            case NOT_CONTAINS:
                return ConditionOperator.NOT_CONTAINS;
            case IN:
                return ConditionOperator.IN;
            case NOT_IN:
                return ConditionOperator.NOT_IN;
            case IS_NULL:
                return ConditionOperator.IS_NULL;
            case IS_NOT_NULL:
                return ConditionOperator.IS_NOT_NULL;
            case BETWEEN_AND:
                return ConditionOperator.BETWEEN_AND;
            default:
                // never happen
                throw new TrantorRuntimeException("Illegal condition Operator「" + operator + "」");
        }
    }

    public static Operator toRelationalOperator(ConditionOperator operator) {
        if (Objects.isNull(operator)) {
            return null;
        }
        switch (operator) {
            case EQ:
                return Operator.EQ;
            case NEQ:
                return Operator.NEQ;
            case GT:
                return Operator.GT;
            case GTE:
                return Operator.GTE;
            case LT:
                return Operator.LT;
            case LTE:
                return Operator.LTE;
            case START_WITH:
                return Operator.START_WITH;
            case END_WITH:
                return Operator.END_WITH;
            case CONTAINS:
                return Operator.CONTAINS;
            case NOT_CONTAINS:
                return Operator.NOT_CONTAINS;
            case IN:
                return Operator.IN;
            case NOT_IN:
                return Operator.NOT_IN;
            case IS_NULL:
                return Operator.IS_NULL;
            case IS_NOT_NULL:
                return Operator.IS_NOT_NULL;
            case BETWEEN_AND:
                return Operator.BETWEEN_AND;
            default:
                // never happen
                throw new TrantorRuntimeException("Illegal ConditionOperator「" + operator + "」");
        }
    }

    public static ConditionValue toConditionValue(FieldType fieldType, Value value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value instanceof VarValue) {
            VarValue varValue = (VarValue) value;
            if (ValueType.CONST.equals(varValue.getValueType())) {
                Object validValue = Objects.isNull(fieldType) ? varValue.getConstValue() : correctValueType(fieldType, varValue.getConstValue());
                if (Objects.isNull(validValue)) {
                    return null;
                }
                return new ConditionValue(ConditionValueType.SPECIFY_VALUE, validValue);
            } else if (ValueType.EXAMPLE.equals(varValue.getValueType())) {
                Object validValue = Objects.isNull(fieldType) ? varValue.getConstValue() : correctValueType(fieldType, varValue.getConstValue());
                if (Objects.isNull(validValue)) {
                    return null;
                }
                return new ConditionValue(ConditionValueType.SAMPLE_VALUE, validValue);
            } else if (ValueType.VAR.equals(varValue.getValueType())) {
                List<VariableValue> variableValues = varValue.getVarValue().stream().map(varStage -> {
                    VariableValue variableValue = new VariableValue();
                    variableValue.setName(varStage.getValueName());
                    variableValue.setValue(varStage.getValueKey());
                    return variableValue;
                }).collect(Collectors.toList());
                return new ConditionValue(ConditionValueType.VARIABLE, variableValues.get(0));
            } else {
                // never happen
                throw new TrantorRuntimeException("Illegal condition ValueType「" + varValue.getValueType() + "」");
            }
        } else if (value instanceof DataControlDimensionValue) {
            DataControlDimensionValue dataControlDimensionValue = (DataControlDimensionValue) value;
            VariableValue variableValue = new VariableValue();
            variableValue.setName(dataControlDimensionValue.getDataControlDimensionName());
            variableValue.setValue(dataControlDimensionValue.getDataControlDimensionKey());
            return new ConditionValue(ConditionValueType.VALUE_RANGE, variableValue);
        }
        throw new IllegalStateException("Never happen, Illegal condition value class: " + value.getClass().getName());
    }

    private static DataControlDimensionValue toDataControlDimensionValue(ConditionValue conditionValue) {
        if (Objects.isNull(conditionValue)) {
            return null;
        }
        DataControlDimensionValue dataControlDimensionValue = new DataControlDimensionValue();
        dataControlDimensionValue.setDataControlDimensionKey(conditionValue.getVariableValue().getValue());
        dataControlDimensionValue.setDataControlDimensionName(conditionValue.getVariableValue().getName());
        return dataControlDimensionValue;
    }

    public static VarValue toVarValue(ConditionValue conditionValue) {
        if (Objects.isNull(conditionValue)) {
            return null;
        }
        VarValue varValue = new VarValue();
        if (ConditionValueType.SPECIFY_VALUE.equals(conditionValue.getValueType())) {
            varValue.setValueType(ValueType.CONST);
            if (Objects.nonNull(conditionValue.getValue())) {
                varValue.setConstValue(String.valueOf(conditionValue.getValue()));
            }
            return varValue;
        } else if (ConditionValueType.SAMPLE_VALUE.equals(conditionValue.getValueType())) {
            varValue.setValueType(ValueType.EXAMPLE);
            if (Objects.nonNull(conditionValue.getValue())) {
                varValue.setConstValue(String.valueOf(conditionValue.getValue()));
            }
            return varValue;
        } else if (ConditionValueType.VARIABLE.equals(conditionValue.getValueType())) {
            varValue.setValueType(ValueType.VAR);
            VariableValue variableValue = conditionValue.getVariableValue();
            if (Objects.isNull(variableValue)) {
                return null;
            }
            VarValue.VarStage varStage = new VarValue.VarStage();
            varStage.setValueName(variableValue.getName());
            varStage.setValueKey(variableValue.getValue());
            varValue.setVarValue(Collections.singletonList(varStage));
            return varValue;
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal ConditionValueType「" + conditionValue.getValueType() + "」");
        }
    }

    public static ConditionLogicalOperator toConditionLogicalOperator(LogicOperator logicOperator) {
        if (Objects.isNull(logicOperator)) {
            return null;
        } else if (LogicOperator.AND.equals(logicOperator)) {
            return ConditionLogicalOperator.AND;
        } else if (LogicOperator.OR.equals(logicOperator)) {
            return ConditionLogicalOperator.OR;
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal LogicOperator「" + logicOperator + "」");
        }
    }

    public static LogicOperator toConditionLogicalOperator(ConditionLogicalOperator conditionLogicalOperator) {
        if (Objects.isNull(conditionLogicalOperator)) {
            return null;
        } else if (ConditionLogicalOperator.AND.equals(conditionLogicalOperator)) {
            return LogicOperator.AND;
        } else if (ConditionLogicalOperator.OR.equals(conditionLogicalOperator)) {
            return LogicOperator.OR;
        } else {
            // never happen
            throw new TrantorRuntimeException("Illegal LogicOperator「" + conditionLogicalOperator + "」");
        }
    }

    /**
     * 纠正值对象类型
     *
     * @param fieldType 字段类型
     * @param value     值对象实例
     * @return 返回正确类型的值对象
     */
    private static Object correctValueType(FieldType fieldType, String value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (FieldType.Number.equals(fieldType) && NumberUtil.isNumber(value)) {
            if (NumberUtil.isInteger(value)) {
                return Integer.valueOf(value);
            } else if (NumberUtil.isLong(value)) {
                return Long.valueOf(value);
            } else if (NumberUtil.isDouble(value)) {
                return Double.valueOf(value);
            }
        } else if (FieldType.Boolean.equals(fieldType)
                && ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value))) {
            return Boolean.valueOf(value);
        }
        return value;
    }
}
