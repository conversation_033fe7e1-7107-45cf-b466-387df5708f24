package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.trantor2.permission.DataConditionMeta;
import io.terminus.trantor2.permission.DataControlDimensionMeta;
import io.terminus.trantor2.permission.api.common.dto.DataConditionWithEffect;
import io.terminus.trantor2.permission.api.common.dto.DataControlDimensionWithEffect;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * 2024/8/27 14:19
 **/
@Mapper
public interface PermissionMetaConverter {

    PermissionMetaConverter INSTANCE = Mappers.getMapper(PermissionMetaConverter.class);

    DataConditionWithEffect convert(DataConditionMeta meta);

    DataControlDimensionWithEffect convert(DataControlDimensionMeta meta);
}
