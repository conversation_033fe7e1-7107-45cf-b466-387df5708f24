package io.terminus.trantor2.permission.impl.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.util.ObjectJsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * 2022/8/26 11:33 上午
 */
public final class ObjectUtils {
    private ObjectUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * mapper
     */
    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    /**
     * 字段复制，跳过null字段
     * @param target
     * @param source
     */
    public static void copyPropertiesSkipNull(Object target, Object source) {
        BeanUtil.copyProperties(source, target, CopyOptions.create().ignoreNullValue().ignoreError());
    }

    public static <T> T deepCopy(T object) {
        try {
            String jsonString = ObjectJsonUtil.MAPPER.writeValueAsString(object);
            return ObjectJsonUtil.MAPPER.readValue(jsonString, (Class<T>) object.getClass());
        } catch (Exception e) {
            throw new TrantorRuntimeException(e);
        }
    }

    /**
     * 类型转换
     * @param source
     * @param targetClass
     * @param <SOURCE>
     * @param <TARGET>
     * @return 转换后的对象
     */
    public static <SOURCE, TARGET> TARGET convert(SOURCE source, Class<TARGET> targetClass) {
        return MAPPER.convertValue(source, targetClass);
    }

    /**
     * to json string
     * @param object
     * @return json
     */
    public static String toJsonString(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new TrantorRuntimeException(e);
        }
    }

    /**
     * json to object
     * @param json
     * @param clazz
     * @param <TARGET>
     * @return object
     */
    public static <TARGET> TARGET parseObject(String json, Class<TARGET> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new TrantorRuntimeException(e);
        }
    }

    /**
     * http:www.trantor.com/ -> www.trantor.com
     * @param url
     * @return 域名
     */
    public static String cleanDomain(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        String target = url;
        if (url.startsWith("http://")) {
            target = url.replaceFirst("http://", "");
        }
        if (url.startsWith("https://")) {
            target = url.replaceFirst("https://", "");
        }
        if (target.contains("/")) {
            target = target.substring(0, target.indexOf("/"));
        }
        if (target.contains(":")) {
            target = target.substring(0, target.indexOf(":"));
        }
        return target;
    }

    /**
     * 检查code格式是否合法
     * @param code
     */
    public static void checkCode(String code) {
        Assert.notNull(code);
        Pattern p = Pattern.compile("^[a-z,A-Z][\\w[.-]]+");
        Matcher m = p.matcher(code);
        if (!m.matches()) {
            throw new TrantorRuntimeException("Code check fail!");
        }
    }
}
