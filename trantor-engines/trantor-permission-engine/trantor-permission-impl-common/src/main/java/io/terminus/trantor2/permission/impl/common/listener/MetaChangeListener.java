package io.terminus.trantor2.permission.impl.common.listener;

import io.terminus.trantor2.meta.event.MetaChangeEvent;
import io.terminus.trantor2.permission.impl.common.listener.handler.MetaChangeHandler;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限元信息变更消息监听处理器，事件触发场景如下：
 * 1. 服务：服务CUD
 * 2. 场景：场景CUD
 * 3. 菜单：菜单CUD
 *
 * <AUTHOR>
 * 2023/7/4 11:38 上午
 **/
@Lazy
@Component
public class MetaChangeListener {

    private final List<MetaChangeHandler> handlers;

    public MetaChangeListener(List<MetaChangeHandler> handlers) {
        // 按声明顺序执行
        this.handlers = handlers.stream().sorted(Comparator.comparing(MetaChangeHandler::getOrder)).collect(Collectors.toList());
    }

    @Order(20)
    @EventListener(MetaChangeEvent.class)
    protected void handleMessage(MetaChangeEvent msg) {
        handlers.forEach(metaChangeHandler -> metaChangeHandler.handle(msg));
    }
}
