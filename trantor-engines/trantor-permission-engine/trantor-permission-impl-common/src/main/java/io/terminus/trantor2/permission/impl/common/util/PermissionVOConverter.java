package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.response.permission.Permission;
import io.terminus.iam.api.response.permission.PermissionDescriptor;
import io.terminus.iam.api.response.permission.PermissionResource;
import io.terminus.iam.api.response.permission.PermissionType;
import io.terminus.trantor2.permission.api.common.dto.FunctionPermissionResourceProps;
import io.terminus.trantor2.permission.api.common.dto.ImportFunctionPermissionResourceProps;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceVO;
import io.terminus.trantor2.permission.api.common.dto.PermissionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/2/1 3:55 PM
 **/
@Mapper(componentModel = "spring")
public interface PermissionVOConverter {

    PermissionVOConverter INSTANCE = Mappers.getMapper(PermissionVOConverter.class);

    default PermissionVO convert(PermissionDescriptor permission) {
        if ( permission == null ) {
            return null;
        }

        PermissionVO permissionVO = new PermissionVO();

        permissionVO.setId( permission.permissionId() );
        permissionVO.setName( permission.permissionName() );
        permissionVO.setKey( permission.permissionKey() );
        permissionVO.setType(PermissionType.valueOf(permission.permissionType()));

        return permissionVO;
    }

    PermissionVO convert(Permission permission);

    PermissionResourceVO convert(PermissionResource permissionResource);

    List<PermissionResourceVO> convertList(List<PermissionResource> permissionResourceList);


    default ImportFunctionPermissionResourceProps convert(FunctionPermissionResourceProps functionPermissionResourceProps) {
        if (functionPermissionResourceProps == null) {
            return null;
        }
        return ImportFunctionPermissionResourceProps.childBuilder()
            .sceneKey(functionPermissionResourceProps.getSceneKey())
            .granted(functionPermissionResourceProps.getGranted())
            .frontendConfig(functionPermissionResourceProps.getFrontendConfig())
            .permission(functionPermissionResourceProps.getPermission()).build();
    }
}
