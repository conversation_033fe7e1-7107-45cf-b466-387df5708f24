package io.terminus.trantor2.permission.impl.common.advice;

import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要重写{@link TrantorContext#getCurrentPortal()}对象的方法
 * <p>
 * 根据指定的{@link #portalCode()}查询到{@link Portal}对象后覆盖{@link TrantorContext#getCurrentPortal()}的值
 *
 * <AUTHOR>
 * 2023/8/24 7:00 下午
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface RewriteCurrentPortal {

    String portalCode();
}
