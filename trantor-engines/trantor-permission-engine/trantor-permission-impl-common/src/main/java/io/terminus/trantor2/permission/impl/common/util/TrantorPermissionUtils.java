package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.response.permission.Permission;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.FunctionPermissionResourceProps;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.permission.impl.common.cache.PropsLoadingConfigContext;
import io.terminus.trantor2.scene.config.SceneType;
import io.terminus.trantor2.scene.config.datamanager.ButtonConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerSceneConfig;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.config.datamanager.ViewResourceConfig;
import io.terminus.trantor2.scene.config.view.IButtonConfig;
import io.terminus.trantor2.scene.constants.SceneConsts;
import io.terminus.trantor2.scene.meta.SceneMeta;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static io.terminus.trantor2.permission.impl.common.util.PermissionUtils.PERMISSION_GROUP_KEY_CONNECT;

/**
 * <AUTHOR>
 * 2023/5/19 10:52 上午
 **/
public final class TrantorPermissionUtils {

    private TrantorPermissionUtils() {
    }

    /**
     * 将菜单资源以及菜单关联的场景资源（视图、按钮）转换成PermissionResourceDTO
     *
     * @param menuMeta          菜单元信息
     * @param sceneMap          当前应用下完整的场景元信息集合
     * @param needResourceTypes 需要的资源类型
     * @return 菜单PermissionResourceDTO
     */
    public static PermissionResourceDTO convertMenuMetaToPermissionResource(@NotNull MenuMeta menuMeta,
                                                                            Map<String, SceneMeta> sceneMap,
                                                                            @NotEmpty List<ACLResourceType> needResourceTypes,
                                                                            @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        if (!needResourceTypes.contains(ACLResourceType.Menu)) {
            return null;
        }

        // 菜单资源
        PermissionResourceDTO menuPermissionResourceDTO = convertToPermissionResource(ACLResourceType.Menu, menuMeta, propsRewriter);
        String sceneKey = menuMeta.getRouteConfig(SceneConsts.SCENE_KEY);
        if (!needResourceTypes.contains(ACLResourceType.View) || StringUtils.isBlank(sceneKey)) {
            // 不需要加载视图节点，或者菜单未绑定场景
            return menuPermissionResourceDTO;
        }

        SceneMeta sceneMeta = sceneMap.get(sceneKey);
        fillInSceneProps(menuPermissionResourceDTO, sceneKey, sceneMeta);
        if (Objects.isNull(sceneMeta)) {
            // 无效场景或场景不存在
            return menuPermissionResourceDTO;
        }

        if (SceneType.DATA.equals(sceneMeta.getType())) {
            if (needResourceTypes.contains(ACLResourceType.View)) {
                DataManagerSceneConfig sceneConfig = (DataManagerSceneConfig) sceneMeta.getSceneConfig();
                List<PermissionResourceDTO> viewPermissionResourceDTOs = convertViewPermissionResource(sceneKey, sceneConfig.getViews(), needResourceTypes, propsRewriter);
                menuPermissionResourceDTO.setChildren(viewPermissionResourceDTOs);
            }
        }

        return menuPermissionResourceDTO;
    }

    private static void fillInSceneProps(@NotNull PermissionResourceDTO menuPermissionResourceDTO, @NotNull String sceneKey, SceneMeta sceneMeta) {
        if (Objects.isNull(menuPermissionResourceDTO.getProps())) {
            menuPermissionResourceDTO.setProps(new FunctionPermissionResourceProps());
        }
        FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) menuPermissionResourceDTO.getProps();
        props.setSceneKey(sceneKey);
        props.setIsSceneValid(Objects.nonNull(sceneMeta));
    }

    public static List<PermissionResourceDTO> convertViewPermissionResource(String sceneKey,
                                                                            List<DataManagerView> dataManagerViews,
                                                                            List<ACLResourceType> needResourceTypes,
                                                                            @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        if (CollectionUtils.isEmpty(dataManagerViews)) {
            return Collections.emptyList();
        }
        return dataManagerViews.stream().map(viewConfig -> {
            // 视图资源
            PermissionResourceDTO viewPermissionResourceDTO = convertToPermissionResource(ACLResourceType.View, viewConfig, propsRewriter);
            if (needResourceTypes.contains(ACLResourceType.Button)) {
                viewPermissionResourceDTO.setProps(FunctionPermissionResourceProps.builder().sceneKey(sceneKey).build());
                // 按钮资源
                if (CollectionUtils.isNotEmpty(viewConfig.getViewResourceConfigs())) {
                    List<ViewResourceConfig> viewResourceConfigs = viewConfig.getViewResourceConfigs().stream()
                        .filter(viewResourceConfig -> ViewResourceConfig.ViewResourceType.Button.equals(viewResourceConfig.getType()))
                        .collect(Collectors.toList());
                    List<PermissionResourceDTO> buttonPermissionResourceDTOs = convertButtonPermissionResource_2(sceneKey, viewPermissionResourceDTO.getResourceKey(), viewResourceConfigs, propsRewriter);
                    viewPermissionResourceDTO.setChildren(buttonPermissionResourceDTOs);
                } else if (CollectionUtils.isNotEmpty(viewConfig.getButtons())) {
                    List<PermissionResourceDTO> buttonPermissionResourceDTOs = convertButtonPermissionResource(sceneKey, viewPermissionResourceDTO.getResourceKey(), viewConfig.getButtons(), propsRewriter);
                    viewPermissionResourceDTO.setChildren(buttonPermissionResourceDTOs);
                }
            }
            return viewPermissionResourceDTO;
        }).collect(Collectors.toList());
    }

    public static List<PermissionResourceDTO> convertButtonPermissionResource_2(String sceneKey,
                                                                                String viewKey,
                                                                                List<ViewResourceConfig> buttonConfigs,
                                                                                @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        return buttonConfigs.stream()
            .map(buttonConfig -> {
                PermissionResourceDTO buttonPermissionResourceDTO = convertToPermissionResource(ACLResourceType.Button, buttonConfig, propsRewriter);
                Map<String, Object> frontedConfigButtonPath = new HashMap<>();
                frontedConfigButtonPath.put("path", buttonConfig.getPath());
                buttonPermissionResourceDTO.setProps(FunctionPermissionResourceProps.builder()
                    .sceneKey(sceneKey)
                    .viewKey(viewKey)
                    .frontendConfig(PropsLoadingConfigContext.isCompleteFrontendConfig() ? frontedConfigButtonPath : null)
                    .build()
                );
                return buttonPermissionResourceDTO;
            })
            .collect(Collectors.toList());
    }

    public static List<PermissionResourceDTO> convertButtonPermissionResource(String sceneKey,
                                                                              String viewKey,
                                                                              List<ButtonConfig> buttonConfigs,
                                                                              @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        return buttonConfigs.stream()
            .map(buttonConfig -> {
                PermissionResourceDTO buttonPermissionResourceDTO = convertToPermissionResource(ACLResourceType.Button, buttonConfig, propsRewriter);
                buttonPermissionResourceDTO.setProps(FunctionPermissionResourceProps.builder()
                    .sceneKey(sceneKey)
                    .viewKey(viewKey)
                    .frontendConfig(PropsLoadingConfigContext.isCompleteFrontendConfig() ? buttonConfig.getFrontendConfig() : null)
                    .build()
                );
                return buttonPermissionResourceDTO;
            })
            .collect(Collectors.toList());
    }

    /**
     * 批量递归方式将菜单树转换为PermissionResourceDTO树对象
     *
     * @param menuMetaList      菜单树列表
     * @param sceneMap          当前应用下完整的场景元信息集合
     * @param needResourceTypes 需要的资源类型
     * @return 菜单PermissionResourceDTO树形对象
     */
    public static List<PermissionResourceDTO> convertMenuMetaToPermissionResourceRecursively(@NotNull Collection<MenuMeta> menuMetaList,
                                                                                             Map<String, SceneMeta> sceneMap,
                                                                                             @NotEmpty List<ACLResourceType> needResourceTypes,
                                                                                             @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        return menuMetaList.stream().map(menuMeta ->
            convertMenuMetaToPermissionResourceRecursively(menuMeta, sceneMap, needResourceTypes, propsRewriter)).collect(Collectors.toList());
    }

    /**
     * 递归方式将菜单树转换为PermissionResourceDTO树对象
     *
     * @param menuMeta          菜单元信息
     * @param sceneMap          当前应用下完整的场景元信息集合
     * @param needResourceTypes 需要的资源类型
     * @return 菜单PermissionResourceDTO树形对象
     */
    public static PermissionResourceDTO convertMenuMetaToPermissionResourceRecursively(@NotNull MenuMeta menuMeta,
                                                                                       Map<String, SceneMeta> sceneMap,
                                                                                       @NotEmpty List<ACLResourceType> needResourceTypes,
                                                                                       @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        PermissionResourceDTO permissionResource = convertMenuMetaToPermissionResource(menuMeta, sceneMap, needResourceTypes, propsRewriter);
        if (Objects.isNull(permissionResource)) {
            return null;
        }

        List<MenuMeta> children = menuMeta.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return permissionResource;
        }
        List<PermissionResourceDTO> childPermissionResourceList = children.stream().map(childMenuMeta ->
            convertMenuMetaToPermissionResourceRecursively(childMenuMeta, sceneMap, needResourceTypes, propsRewriter)).collect(Collectors.toList());
        permissionResource.setChildren(childPermissionResourceList);
        return permissionResource;
    }

    /**
     * 资源对象转换PermissionResourceDTO
     *
     * @param type     资源类型
     * @param resource 资源对象
     * @return PermissionResourceDTO对象
     */
    public static PermissionResourceDTO convertToPermissionResource(@NotNull ACLResourceType type,
                                                                    Object resource,
                                                                    @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter) {
        if (resource == null) {
            return null;
        }
        PermissionResourceDTO permissionResource = new PermissionResourceDTO();
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        permissionResource.setProps(props);
        switch (type) {
            case View:
                DataManagerView viewConfig = (DataManagerView) resource;
                permissionResource.setResourceKey(viewConfig.getKey());
                permissionResource.setResourceName(viewConfig.getTitle());
                permissionResource.setResourceType(ACLResourceType.View);
                if (Objects.nonNull(viewConfig.getPermissionKey())) {
                    props.setOwnPermissionKey(viewConfig.getPermissionKey());
                }
                break;
            case Button:
                if (resource instanceof ViewResourceConfig) {
                    ViewResourceConfig viewResourceConfig = (ViewResourceConfig) resource;
                    permissionResource.setResourceKey(viewResourceConfig.getKey());
                    permissionResource.setResourceName(viewResourceConfig.getLabel());
                    permissionResource.setResourceType(ACLResourceType.Button);
                } else if (resource instanceof IButtonConfig) {
                    // 兼容即将废弃的逻辑
                    IButtonConfig buttonConfig = (IButtonConfig) resource;
                    permissionResource.setResourceKey(buttonConfig.getKey());
                    permissionResource.setResourceName(buttonConfig.getName());
                    permissionResource.setResourceType(ACLResourceType.Button);
                }
                break;
            case Menu:
                MenuMeta menuMeta = (MenuMeta) resource;
                permissionResource.setResourceKey(menuMeta.getKey());
                permissionResource.setResourceName(menuMeta.getLabel());
                permissionResource.setResourceType(ACLResourceType.Menu);
                if (Objects.nonNull(menuMeta.getPermissionKey())) {
                    props.setOwnPermissionKey(menuMeta.getPermissionKey());
                }
                break;
            case Application:
                ModuleMeta module = (ModuleMeta) resource;
                permissionResource.setResourceId(module.getId());
                permissionResource.setResourceKey(module.getKey());
                permissionResource.setResourceName(module.getName());
                permissionResource.setResourceType(ACLResourceType.Application);
                break;
            default:
                throw new TrantorRuntimeException("unknown acl resource type: " + type);
        }
        propsRewriter.write(permissionResource);
        return permissionResource;
    }

    /**
     * 完善资源的功能权限信息和授权状态
     *
     * @param permissionResource 资源对象
     * @param permissionMap      权限集合
     * @param consumer           permissionResource的Consumer操作
     */
    public static void improveFunctionPermissionResourceProps(PermissionResourceDTO permissionResource,
                                                              Map<String, Permission> permissionMap,
                                                              FunctionPermissionPropConsumer consumer) {
        FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) permissionResource.getProps();
        if (Objects.isNull(props)) {
            props = FunctionPermissionResourceProps.builder().build();
            permissionResource.setProps(props);
        }
        String key = getPermissionPropKey(permissionResource);
        Permission permission = permissionMap.get(key);
        consumer.consume(permissionResource, props, permission);
    }

    public static String getPermissionPropKey(PermissionResourceDTO permissionResource) {
        String key = null;
        FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) permissionResource.getProps();
        switch (permissionResource.getResourceType()) {
            case View:
            case Button:
                String sceneKey = props.getSceneKey();
                key = permissionResource.getResourceType()
                    + ":" + sceneKey + PERMISSION_GROUP_KEY_CONNECT + permissionResource.getResourceKey();
                break;
            case Menu:
            case Application:
                key = permissionResource.getResourceType() + ":" + permissionResource.getResourceKey();
                break;
            default:
                break;
        }
        return key;
    }

    /**
     * 递归遍历检查权限授权状态
     *
     * @param permissionMap      权限信息Map
     * @param permissionResource 资源对象
     * @param consumer           permissionResource的Consumer操作
     */
    public static void improveResourcePermissionRecursively(PermissionResourceDTO permissionResource,
                                                            Map<String, Permission> permissionMap,
                                                            FunctionPermissionPropConsumer consumer) {
        if (MapUtils.isEmpty(permissionMap)) {
            return;
        }
        improveFunctionPermissionResourceProps(permissionResource, permissionMap, consumer);
        List<PermissionResourceDTO> children = permissionResource.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        children.forEach(childPermissionResource -> {
            improveResourcePermissionRecursively(childPermissionResource, permissionMap, consumer);
        });
    }


    /**
     * 递归遍历检查权限授权状态
     *
     * @param permissionResourceList 资源对象List
     * @param permissionMap          权限信息Map
     * @param consumer               permissionResource的Consumer操作
     */
    public static void improveResourcePermissionRecursively(Collection<PermissionResourceDTO> permissionResourceList,
                                                            Map<String, Permission> permissionMap,
                                                            FunctionPermissionPropConsumer consumer) {
        if (MapUtils.isEmpty(permissionMap)) {
            return;
        }
        permissionResourceList.forEach(permissionResource -> {
            improveResourcePermissionRecursively(permissionResource, permissionMap, consumer);
        });
    }

    /**
     * 递归过滤掉缺少权限点的场景资源
     *
     * @param permissionResourceList 资源对象列表
     */
    public static void filterOutLackPermissionSceneResourceRecursively(Collection<PermissionResourceDTO> permissionResourceList) {
        Iterator<PermissionResourceDTO> iterator = permissionResourceList.iterator();
        while (iterator.hasNext()) {
            PermissionResourceDTO next = iterator.next();
            if (Objects.isNull(next.getProps())) {
                continue;
            }
            FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) next.getProps();
            // 具有sceneKey，说明是场景资源，判断是否有关联的权限点，如果没有，说明当前资源未注册权限或者后期删除了权限，需要过滤掉
            if (Objects.nonNull(props.getSceneKey()) && Objects.isNull(props.getPermission())) {
                iterator.remove();
                continue;
            }
            List<PermissionResourceDTO> children = next.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }
            filterOutLackPermissionSceneResourceRecursively(children);
        }
    }

    /**
     * 递归过滤出缺少权限点的场景资源
     * 一个列表三个按钮 两个按钮正常有有权限 返回不正常的一个按钮，一个列表
     *
     * @param permissionResourceList 缺少权限点的资源对象列表
     */
    public static List<PermissionResourceDTO> filterOutPermissionSceneResourceToListRecursively(Collection<PermissionResourceDTO> permissionResourceList, List<PermissionResourceDTO> resourceDTOCollection) {
        for (PermissionResourceDTO resourceDTO : permissionResourceList) {
            if (Objects.isNull(resourceDTO.getProps())) {
                continue;
            }
            FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) resourceDTO.getProps();
            // 缺少权限点的场景资源
            if (Objects.isNull(props.getPermission())) {
                resourceDTOCollection.add(resourceDTO);

                List<PermissionResourceDTO> children = resourceDTO.getChildren();
                if (CollectionUtils.isEmpty(children)) {
                    continue;
                }
                filterOutPermissionSceneResourceToListRecursively(children, resourceDTOCollection);
            }
        }
        return resourceDTOCollection;
    }

    /**
     * 组装权限key放入Map中
     * key = {resourceType}:{permissionKey}, value = permission object
     *
     * @param permissionList 权限列表
     * @return 权限key Permission Map
     */
    @org.jetbrains.annotations.NotNull
    public static Map<String, Permission> convertPermissionListToMap(List<Permission> permissionList) {
        Map<String, Permission> permissionMap = new HashMap<>();
        permissionList.forEach(permission -> {
            if (CollectionUtils.isNotEmpty(permission.getPermissionResources())) {
                permission.getPermissionResources().forEach(permissionResource -> {
                    String key = permissionResource.getResourceType() + ":" + permission.getKey();
                    permissionMap.put(key, permission);
                });
            }
        });
        return permissionMap;
    }

    /**
     * 递归获取资源树中的 View 类型的资源标识（viewKey）列表。
     *
     * <p>此方法会遍历所有资源对象树，并将类型为 View 的资源对象的键（viewKey）
     * 添加到传入的集合中。</p>
     *
     * @param permissionResourceDTOList 资源对象树的列表
     * @param allViewKeys               用于存储所有 View 类型资源标识的集合
     */
    public static void recursiveCollectViewKeys(Collection<PermissionResourceDTO> permissionResourceDTOList,
                                                @NotNull Collection<String> allViewKeys) {
        if (CollectionUtils.isEmpty(permissionResourceDTOList)) {
            return;
        }
        for (PermissionResourceDTO permissionResourceDTO : permissionResourceDTOList) {
            if (ACLResourceType.View.equals(permissionResourceDTO.getResourceType())) {
                allViewKeys.add(permissionResourceDTO.getResourceKey());
            }
            if (CollectionUtils.isNotEmpty(permissionResourceDTO.getChildren())) {
                recursiveCollectViewKeys(permissionResourceDTO.getChildren(), allViewKeys);
            }
        }
    }

    public static void recursiveCollectView(Collection<PermissionResourceDTO> permissionResourceDTOList,
                                            @NotNull Collection<PermissionResourceDTO> allViews) {
        if (CollectionUtils.isEmpty(permissionResourceDTOList)) {
            return;
        }
        for (PermissionResourceDTO permissionResourceDTO : permissionResourceDTOList) {
            if (ACLResourceType.View.equals(permissionResourceDTO.getResourceType())) {
                allViews.add(permissionResourceDTO);
            }
            if (CollectionUtils.isNotEmpty(permissionResourceDTO.getChildren())) {
                recursiveCollectView(permissionResourceDTO.getChildren(), allViews);
            }
        }
    }

    @FunctionalInterface
    public interface FunctionPermissionPropConsumer {
        void consume(PermissionResourceDTO permissionResourceDTO, FunctionPermissionResourceProps props, Permission permission);
    }
}
