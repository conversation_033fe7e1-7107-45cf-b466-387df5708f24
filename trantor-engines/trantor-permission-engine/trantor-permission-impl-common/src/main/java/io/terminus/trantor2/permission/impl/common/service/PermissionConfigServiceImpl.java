package io.terminus.trantor2.permission.impl.common.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.iam.api.response.admin.PolicyEnforcementMode;
import io.terminus.iam.api.response.application.ApplicationConfigResult;
import io.terminus.trantor2.common.exception.ErrorType;
import io.terminus.trantor2.common.iam.IamClientFactory;
import io.terminus.trantor2.module.exception.PermissionConfigException;
import io.terminus.trantor2.permission.api.common.dto.PermissionConfig;
import io.terminus.trantor2.permission.api.common.service.PermissionConfigService;
import io.terminus.trantor2.permission.impl.common.util.PermissionConfigConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 2023/12/13 10:54 AM
 **/
@Service
@RequiredArgsConstructor
public class PermissionConfigServiceImpl implements PermissionConfigService {

    // 权限配置增加缓存，设置 ttl = 60s 实现延迟失效
    private static final Cache<Long, PermissionConfig> permissionConfigCache = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).build();

    private final IamClientFactory iamClientFactory;

    @Override
    @NotNull
    public PermissionConfig findPermissionConfig(@NotNull Long endpointId) {
        return permissionConfigCache.get(endpointId, key -> {
            try {
                ApplicationConfigResult applicationConfigResult = iamClientFactory.getIamClient().applicationConfig().findByAppId(endpointId).execute();
                return PermissionConfigConverter.INSTANCE.convert(applicationConfigResult);
            } catch (Exception e) {
                throw new PermissionConfigException("find permission config error", e);
            }
        });
    }

    @Override
    public boolean isFunctionPermissionEnabled(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getFunctionPermissionEnabled())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "functionPermissionEnabled is null");
        }
        return Boolean.TRUE.equals(permissionConfig.getFunctionPermissionEnabled());
    }

    @Override
    public boolean isDataPermissionEnabled(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getDataPermissionEnabled())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "dataPermissionEnabled is null");
        }
        return Boolean.TRUE.equals(permissionConfig.getDataPermissionEnabled());
    }

    @Override
    public boolean isFieldPermissionEnabled(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getFieldPermissionEnabled())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "fieldPermissionEnabled is null");
        }
        return Boolean.TRUE.equals(permissionConfig.getFieldPermissionEnabled());
    }

    @Override
    public PolicyEnforcementMode getFunctionPermissionPolicyEnforcementMode(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getFunctionPermissionMode())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "functionPermissionMode is null");
        }
        return permissionConfig.getFunctionPermissionMode();
    }

    @Override
    public PolicyEnforcementMode getDataPermissionPolicyEnforcementMode(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getDataPermissionMode())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "dataPermissionMode is null");
        }
        return permissionConfig.getDataPermissionMode();
    }

    @Override
    public PolicyEnforcementMode getFieldPermissionPolicyEnforcementMode(@NotNull Long iamAppId) {
        PermissionConfig permissionConfig = findPermissionConfig(iamAppId);
        if (Objects.isNull(permissionConfig.getFieldPermissionMode())) {
            throw new PermissionConfigException(ErrorType.PERMISSION_CONFIG_NOT_FOUND_ERROR, "fieldPermissionMode is null");
        }
        return permissionConfig.getFieldPermissionMode();
    }
}
