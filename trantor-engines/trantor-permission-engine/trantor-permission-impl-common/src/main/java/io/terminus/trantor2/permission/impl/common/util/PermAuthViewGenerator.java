package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.response.permission.PermissionDescriptor;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.module.service.ModuleQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.permission.api.common.dto.ACLResourceType;
import io.terminus.trantor2.permission.api.common.dto.FunctionPermissionResourceProps;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceDTO;
import io.terminus.trantor2.scene.meta.SceneMeta;
import io.terminus.trantor2.scene.repo.SceneRepo;
import io.terminus.trantor2.scene.service.SceneQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.terminus.trantor2.permission.impl.common.util.PermissionUtils.PERMISSION_GROUP_KEY_CONNECT;

/**
 * 权限授权视图生成工具
 *
 * <AUTHOR>
 * 2025/3/28 14:02
 **/
public class PermAuthViewGenerator {

    private PermAuthViewGenerator() {
    }

    public static Collection<PermissionResourceDTO> generateForFuncPerm(@NotNull String portalCode,
                                                                        @NotNull ModuleQueryService moduleQueryService,
                                                                        @NotNull MenuQueryService menuQueryService,
                                                                        @NotNull SceneQueryService sceneQueryService,
                                                                        @NotNull MetaQueryService metaQueryService,
                                                                        @NotNull SceneRepo sceneRepo) {
        ModuleMeta module = moduleQueryService.findByKey(portalCode);
        List<ACLResourceType> needResourceTypes = Arrays.asList(ACLResourceType.Menu, ACLResourceType.View);
        List<PermissionResourceDTO> menuPermissionResourceDTOS = findResourceTreeByPortal(module, needResourceTypes, permissionResourceDTO -> {
        }, menuQueryService, sceneQueryService, metaQueryService, sceneRepo);

        if (CollectionUtils.isNotEmpty(menuPermissionResourceDTOS)) {
            // 递归获取资源树中所有视图和viewKey
            Collection<PermissionResourceDTO> allViews = new ArrayList<>();
            TrantorPermissionUtils.recursiveCollectView(menuPermissionResourceDTOS, allViews);
            List<String> allViewKeys = allViews.stream()
                    .map(PermissionResourceDTO::getResourceKey).distinct()
                    .collect(Collectors.toList());

            // 查询视图权限信息
            List<ViewPermissionDTO> viewPermissions = metaQueryService.findViewPermissions(EditUtil.ctxFromThreadLocal(), portalCode, allViewKeys);

            // 补全视图下按钮资源节点
            appendButtonPermissionResourceDTO(allViews, viewPermissions);

            // 批量查询视图引用权限项：viewKey -> set of view permissionKey
            Map<String, String> viewOwnPermissionKey = new HashMap<>();
            Map<String, Set<String>> viewRefPermissionKeys = new HashMap<>();
            PermissionUtils.getViewRefPermissionKeys(viewPermissions, viewOwnPermissionKey, viewRefPermissionKeys);
            // 批量查询视图所有按钮引用权限项：buttonKey -> set of button permissionKey
            Map<String, Map<String, String>> viewKeyToButtonKeyToOwnPermissionKeyMap = new HashMap<>();
            Map<String, Map<String, Set<String>>> viewKeyToButtonKeyToRefPermissionKeysMap = new HashMap<>();
            PermissionUtils.getButtonRefPermissionKeys(viewPermissions, viewKeyToButtonKeyToOwnPermissionKeyMap, viewKeyToButtonKeyToRefPermissionKeysMap);

            // 补全 PermissionResourceDTO props 参数
            completePermissionResourceProps(menuPermissionResourceDTOS,
                    viewOwnPermissionKey,
                    viewRefPermissionKeys,
                    viewKeyToButtonKeyToOwnPermissionKeyMap,
                    viewKeyToButtonKeyToRefPermissionKeysMap);
        }

        return menuPermissionResourceDTOS;
    }

    private static List<PermissionResourceDTO> findResourceTreeByPortal(@NotNull ModuleMeta moduleMeta,
                                                                        @NotEmpty List<ACLResourceType> needResourceTypes,
                                                                        @NotNull PermissionResourceDTO.PermissionPropsRewriter propsRewriter,
                                                                        @NotNull MenuQueryService menuQueryService,
                                                                        @NotNull SceneQueryService sceneQueryService,
                                                                        @NotNull MetaQueryService metaQueryService,
                                                                        @NotNull SceneRepo sceneRepo) {
        TrantorContext.setTeamId(moduleMeta.getTeamId());
        TrantorContext.setModuleKey(moduleMeta.getKey());
        TrantorContext.setPortalCode(moduleMeta.getKey());
        List<MenuMeta> menuTree = menuQueryService.getMenuTree(moduleMeta.getKey());
        if (CollectionUtils.isEmpty(menuTree)) {
            return Collections.emptyList();
        }
        // 在需要加载视图节点时，再去查询菜单绑定场景信息
        Map<String, SceneMeta> sceneMap = needResourceTypes.contains(ACLResourceType.View)
                ? recursiveGetSceneWithSingleView(menuTree, needResourceTypes.contains(ACLResourceType.Button), sceneQueryService, metaQueryService, sceneRepo)
                : Collections.emptyMap();
        return TrantorPermissionUtils.convertMenuMetaToPermissionResourceRecursively(menuTree, sceneMap, needResourceTypes, propsRewriter);
    }

    private static Map<String, SceneMeta> recursiveGetSceneWithSingleView(@NotNull Collection<MenuMeta> menuMetaList,
                                                                          boolean containButton,
                                                                          @NotNull SceneQueryService sceneQueryService,
                                                                          @NotNull MetaQueryService metaQueryService,
                                                                          @NotNull SceneRepo sceneRepo) {
        // 由于存在多层级菜单，需要拉平菜单列表后查询菜单关联场景
        List<MenuMeta> flatMenuMetaList = new ArrayList<>();
        MenuUtils.flatMenuMetaListRecursively(menuMetaList, flatMenuMetaList);
        Set<String> sceneKeys = new HashSet<>(MenuUtils.getMenuToSceneMap(flatMenuMetaList).values());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(sceneKeys)) {
            return Collections.emptyMap();
        }
        if (containButton) {
            // deprecated 暂时保留分支代码以作兼容
            return MenuUtils.getRefScenes(sceneQueryService, flatMenuMetaList);
        } else {
            // 如果不需要包含按钮，采用性能更优的查询方式
            return MenuUtils.recursiveGetSceneWithSingleView(metaQueryService, sceneRepo, flatMenuMetaList);
        }
    }

    private static void appendButtonPermissionResourceDTO(Collection<PermissionResourceDTO> views,
                                                          Collection<ViewPermissionDTO> viewPermissions) {
        if (CollectionUtils.isEmpty(views) || CollectionUtils.isEmpty(viewPermissions)) {
            return;
        }
        Map<String, ViewPermissionDTO> viewKeyToViewPermission = viewPermissions.stream()
                .collect(Collectors.toMap(ViewPermissionDTO::getViewKey, Function.identity()));
        for (PermissionResourceDTO view : views) {
            ViewPermissionDTO viewPermissionDTO = viewKeyToViewPermission.get(view.getResourceKey());
            if (Objects.isNull(viewPermissionDTO) || CollectionUtils.isEmpty(viewPermissionDTO.getComponents())) {
                continue;
            }
            List<PermissionResourceDTO> buttons = new ArrayList<>();
            for (ViewPermissionDTO.ComponentPermissionDTO component : viewPermissionDTO.getComponents()) {
                if (ACLResourceType.Button.name().equals(component.getResourceType()) && component.isPermissionEnabled()) {
                    buttons.add(convertButton(view.getResourceKey(), component));
                }
                List<ViewPermissionDTO.VirtualComponentPermissionDTO> virtualComponents = component.getVirtualComponents();
                if (CollectionUtils.isNotEmpty(virtualComponents)) {
                    for (ViewPermissionDTO.VirtualComponentPermissionDTO virtualComponent : virtualComponents) {
                        if (ACLResourceType.Button.name().equals(virtualComponent.getResourceType())) {
                            buttons.add(convertButton(view.getResourceKey(), virtualComponent));
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(buttons)) {
                sortButtons(buttons);
                view.setChildren(buttons);
            }
        }
    }

    private static PermissionResourceDTO convertButton(String viewKey,
                                                       @NotNull ViewPermissionDTO.ComponentPermissionDTO component) {
        PermissionResourceDTO dto = new PermissionResourceDTO();
        dto.setResourceKey(component.getCompKey());
        dto.setResourceName(component.getCompTitle());
        dto.setResourceType(ACLResourceType.valueOf(component.getResourceType()));
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        props.setViewKey(viewKey);
        props.setResourcePath(component.getResourcePath());
        dto.setProps(props);
        return dto;
    }

    private static PermissionResourceDTO convertButton(String viewKey,
                                                       @NotNull ViewPermissionDTO.VirtualComponentPermissionDTO virtualComponent) {
        PermissionResourceDTO dto = new PermissionResourceDTO();
        dto.setResourceKey(virtualComponent.getVirtualCompFullKey());
        dto.setResourceName(virtualComponent.getVirtualCompTitle());
        dto.setResourceType(ACLResourceType.valueOf(virtualComponent.getResourceType()));
        FunctionPermissionResourceProps props = new FunctionPermissionResourceProps();
        props.setViewKey(viewKey);
        props.setResourcePath(virtualComponent.getResourcePath());
        dto.setProps(props);
        return dto;
    }

    private static void sortButtons(@NotEmpty List<PermissionResourceDTO> buttons) {
        buttons.sort(Comparator.comparing(e -> {
            Collection<String> path = ((FunctionPermissionResourceProps) e.getProps()).getResourcePath();
            return StringUtils.join(path, ',');
        }));
    }

    /**
     * 补全授权资源节点中的props属性参数
     *
     * @param permissionResourceDTOList                授权资源树列表
     * @param viewOwnPermissionKey                     视图自身的权限项映射
     * @param viewRefPermissionKeys                    视图引用服务的权限项映射：viewKey -> set of view permissionKey
     * @param viewKeyToButtonKeyToOwnPermissionKeyMap  视图关联按钮自身的权限项映射
     * @param viewKeyToButtonKeyToRefPermissionKeysMap 视图关联按钮引用权限项映射：buttonKey -> set of button permissionKey
     */
    private static void completePermissionResourceProps(@NotEmpty List<PermissionResourceDTO> permissionResourceDTOList,
                                                        Map<String, String> viewOwnPermissionKey,
                                                        Map<String, Set<String>> viewRefPermissionKeys,
                                                        Map<String, Map<String, String>> viewKeyToButtonKeyToOwnPermissionKeyMap,
                                                        Map<String, Map<String, Set<String>>> viewKeyToButtonKeyToRefPermissionKeysMap) {
        improveResourcePermissionRecursively(permissionResourceDTOList, (permissionResourceDTO, permissionResourceProps, permission) -> {
            if (Objects.isNull(permissionResourceDTO.getProps())) {
                permissionResourceDTO.setProps(FunctionPermissionResourceProps.builder().build());
            }
            FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) permissionResourceDTO.getProps();
            if (ACLResourceType.View.equals(permissionResourceDTO.getResourceType())) {
                String ownPermissionKey = viewOwnPermissionKey.get(permissionResourceDTO.getResourceKey());
                props.setOwnPermissionKey(ownPermissionKey);
                Set<String> refPermissionKeys = viewRefPermissionKeys.get(permissionResourceDTO.getResourceKey());
                if (CollectionUtils.isNotEmpty(refPermissionKeys)) {
                    props.setResolvedPermissionKeys(refPermissionKeys);
                }
            } else if (ACLResourceType.Button.equals(permissionResourceDTO.getResourceType())) {
                String viewKey = ((FunctionPermissionResourceProps) permissionResourceDTO.getProps()).getViewKey();
                String ownPermissionKey = viewKeyToButtonKeyToOwnPermissionKeyMap.getOrDefault(viewKey, Collections.emptyMap())
                        .get(permissionResourceDTO.getResourceKey());
                props.setOwnPermissionKey(ownPermissionKey);
                Set<String> refPermissionKeys = viewKeyToButtonKeyToRefPermissionKeysMap.getOrDefault(viewKey, Collections.emptyMap())
                        .get(permissionResourceDTO.getResourceKey());
                if (CollectionUtils.isNotEmpty(refPermissionKeys)) {
                    props.setResolvedPermissionKeys(refPermissionKeys);
                }
            }
        });
    }

    public static void improveResourcePermissionRecursively(Collection<PermissionResourceDTO> permissionResourceList,
                                                            PermissionResourceDTO.PermissionPropsConsumer consumer) {
        improveResourcePermissionRecursively(permissionResourceList, Collections.emptyMap(), consumer);
    }

    /**
     * 递归遍历检查权限授权状态
     *
     * @param permissionResourceList 资源对象List
     * @param permissionMap          权限信息Map
     * @param consumer               permissionResource的Consumer操作
     */
    public static void improveResourcePermissionRecursively(Collection<PermissionResourceDTO> permissionResourceList,
                                                            Map<String, ? extends PermissionDescriptor> permissionMap,
                                                            PermissionResourceDTO.PermissionPropsConsumer consumer) {
        permissionResourceList.forEach(permissionResource ->
                improveResourcePermissionRecursively(permissionResource, permissionMap, consumer));
    }

    /**
     * 递归遍历检查权限授权状态
     *
     * @param permissionMap      权限信息Map
     * @param permissionResource 资源对象
     * @param consumer           permissionResource的Consumer操作
     */
    private static void improveResourcePermissionRecursively(PermissionResourceDTO permissionResource,
                                                             Map<String, ? extends PermissionDescriptor> permissionMap,
                                                             PermissionResourceDTO.PermissionPropsConsumer consumer) {
        if (CollectionUtils.isNotEmpty(permissionResource.getChildren())) {
            improveResourcePermissionRecursively(permissionResource.getChildren(), permissionMap, consumer);
        }
        improveFunctionPermissionResourceProps(permissionResource, permissionMap, consumer);
    }

    /**
     * 完善资源的功能权限信息和授权状态
     *
     * @param permissionResource 资源对象
     * @param keyToPermissionMap      权限集合
     * @param consumer           permissionResource的Consumer操作
     */
    private static void improveFunctionPermissionResourceProps(PermissionResourceDTO permissionResource,
                                                               Map<String, ? extends PermissionDescriptor> keyToPermissionMap,
                                                               PermissionResourceDTO.PermissionPropsConsumer consumer) {
        consumer.consume(permissionResource, permissionResource.getProps(), keyToPermissionMap.get(getPermissionPropKey(permissionResource)));
    }

    private static String getPermissionPropKey(PermissionResourceDTO permissionResource) {
        if (permissionResource.getProps() instanceof FunctionPermissionResourceProps) {
            String key = null;
            FunctionPermissionResourceProps props = (FunctionPermissionResourceProps) permissionResource.getProps();
            switch (permissionResource.getResourceType()) {
                case View:
                case Button:
                    String sceneKey = props.getSceneKey();
                    key = permissionResource.getResourceType()
                            + ":" + sceneKey + PERMISSION_GROUP_KEY_CONNECT + permissionResource.getResourceKey();
                    break;
                case Menu:
                case Application:
                    key = permissionResource.getResourceType() + ":" + permissionResource.getResourceKey();
                    break;
                default:
                    break;
            }
            return key;
        }
        return null;
    }
}
