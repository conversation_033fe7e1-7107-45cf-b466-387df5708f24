package io.terminus.trantor2.permission.impl.common.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 2024/2/5 9:08 PM
 **/
@Component
@RequiredArgsConstructor
public class PortalToEndpointUtil implements PortalToIamAppConverter {

    private final ModuleRepo moduleRepo;
    private final ConfigurationService configurationService;

    private static final Cache<Long, Long> PORTAL_ID_TO_IAM_APP_ID = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();
    private static final Cache<String, Long> PORTAL_CODE_TO_IAM_APP_ID = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    @Override
    public Long getIamAppIdByPortalId(@NotNull Long portalId) {
        return PORTAL_ID_TO_IAM_APP_ID.get(portalId, aLong -> {
            ModuleMeta moduleMeta = moduleRepo.findOneById(portalId)
                    .orElseThrow(() -> new TrantorRuntimeException("module meta can not found, id = " + portalId));
            return configurationService.getIamEndpointId(moduleMeta.getTeamId(), moduleMeta.getKey());
        });
    }

    @Override
    public Long getIamAppIdByPortalCode(@NotNull String portalCode) {
        return PORTAL_CODE_TO_IAM_APP_ID.get(generatePortalCodeCacheKey(portalCode), aLong -> {
            ModuleMeta moduleMeta = moduleRepo.findOneByKey(portalCode, ResourceContext.ctxFromThreadLocal())
                    .orElseThrow(() -> new TrantorRuntimeException("module meta can not found, key = " + portalCode));
            return configurationService.getIamEndpointId(moduleMeta.getTeamId(), moduleMeta.getKey());
        });
    }

    @Override
    public Long getIamAppIdFromContext() {
        Long iamAppId = TrantorContext.getCurrentPortalOptional().map(Portal::getIamEndpointId).orElse(null);
        if (Objects.nonNull(iamAppId)) {
            return iamAppId;
        }
        String portalCode = TrantorContext.getCurrentPortalOptional()
                .map(Portal::getCode)
                .orElseGet(TrantorContext::getPortalCode);
        if (Objects.isNull(portalCode)) {
            throw new TrantorRuntimeException("Not found portalCode in TrantorContext");
        }
        return getIamAppIdByPortalCode(portalCode);
    }

    /**
     * 生成门户code缓存key
     */
    private String generatePortalCodeCacheKey(@NotNull String portalCode) {
        return TrantorContext.getTeamCode() + "_" + portalCode;
    }
}
