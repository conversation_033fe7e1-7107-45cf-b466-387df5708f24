package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.response.application.ApplicationConfigResult;
import io.terminus.trantor2.iam.dto.ApplicationConfig;
import io.terminus.trantor2.permission.api.common.dto.PermissionConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * 2023/12/13 10:57 AM
 **/
@Mapper
public interface PermissionConfigConverter {

    PermissionConfigConverter INSTANCE = Mappers.getMapper(PermissionConfigConverter.class);

    PermissionConfig convert(ApplicationConfigResult applicationConfigResult);

    PermissionConfig convert(ApplicationConfig applicationConfig);
}
