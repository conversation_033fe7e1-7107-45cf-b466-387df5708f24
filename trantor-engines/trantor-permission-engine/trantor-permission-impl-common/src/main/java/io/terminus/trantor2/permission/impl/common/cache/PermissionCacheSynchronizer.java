package io.terminus.trantor2.permission.impl.common.cache;

import io.terminus.trantor2.iam.cache.PermissionCacheManager;
import io.terminus.trantor2.module.adapter.PortalPermissionCache;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheCleanser;
import io.terminus.trantor2.permission.api.common.cache.PermissionCacheEvent;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.api.common.cache.ResourcePermissionCache;
import io.terminus.trantor2.permission.api.common.consts.PermissionConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2023/7/6 7:25 下午
 **/
@Slf4j
@Component
public class PermissionCacheSynchronizer implements PermissionCacheCleanser {

    private static final String LOG_INFO_TEMPLATE = "publish a message [{}] to redis topic [{}]";

    private final ResourcePermissionCache resourcePermissionCache;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final RTopic expireTopic;
    private final PermissionCacheManager permissionCacheManager;

    public PermissionCacheSynchronizer(ResourcePermissionCache resourcePermissionCache,
                                       PortalToIamAppConverter portalToIamAppConverter,
                                       RedissonClient redissonClient,
                                       PermissionCacheSyncListener permissionCacheSyncListener,
                                       PermissionCacheManager permissionCacheManager) {
        this.resourcePermissionCache = resourcePermissionCache;
        this.expireTopic = redissonClient.getTopic(PermissionConstants.PERMISSION_CACHE_TOPIC);
        this.expireTopic.addListener(PermissionCacheEvent.class, permissionCacheSyncListener);
        this.portalToIamAppConverter = portalToIamAppConverter;
        this.permissionCacheManager = permissionCacheManager;
    }

    public void invalidateAll() {
        resourcePermissionCache.invalidateAll();
        PortalPermissionCache.invalidateAllPortalPermissionCache();
        permissionCacheManager.invalidateAll();

        PermissionCacheEvent event = new PermissionCacheEvent().setClearAll(Boolean.TRUE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidateUserPortalCache(@NotNull Long userId, @NotNull Long portalId) {
        resourcePermissionCache.invalidateByUserAndPortal(userId, portalId);
        PortalPermissionCache.invalidatePortalPermissionCacheByEndpoint(portalToIamAppConverter.getIamAppIdByPortalId(portalId));
        permissionCacheManager.invalidateAll();

        PermissionCacheEvent event = new PermissionCacheEvent().setUserId(userId).setPortalId(portalId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidateUserCache(@NotNull Long userId) {
        resourcePermissionCache.invalidateUserCache(userId);
        permissionCacheManager.invalidateAll();

        PermissionCacheEvent event = new PermissionCacheEvent().setUserId(userId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }

    @Override
    public void invalidatePortalCache(@NotNull Long portalId) {
        resourcePermissionCache.invalidatePortalCache(portalId);
        PortalPermissionCache.invalidatePortalPermissionCacheByEndpoint(portalToIamAppConverter.getIamAppIdByPortalId(portalId));
        permissionCacheManager.invalidateAll();

        PermissionCacheEvent event = new PermissionCacheEvent().setPortalId(portalId).setClearAll(Boolean.FALSE);
        expireTopic.publish(event);
        log.info(LOG_INFO_TEMPLATE, event, PermissionConstants.PERMISSION_CACHE_TOPIC);
    }
}
