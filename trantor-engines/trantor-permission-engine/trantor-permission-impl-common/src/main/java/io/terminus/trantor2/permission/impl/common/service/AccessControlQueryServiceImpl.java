package io.terminus.trantor2.permission.impl.common.service;

import cn.hutool.core.collection.CollUtil;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.ide.repository.ApiMetaRepo;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.dto.criteria.Cond;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.dto.page.Order;
import io.terminus.trantor2.meta.api.dto.page.PageReq;
import io.terminus.trantor2.meta.api.model.MetaType;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.permission.ApiMeta;
import io.terminus.trantor2.permission.api.common.dto.ResourceAccessControlFindRequest;
import io.terminus.trantor2.permission.api.common.dto.ResourceAccessControlPageRequest;
import io.terminus.trantor2.permission.api.common.service.AccessControlQueryService;
import io.terminus.trantor2.permission.props.AIAgentProps;
import io.terminus.trantor2.permission.props.ApiMetaProps;
import io.terminus.trantor2.permission.props.ServiceProps;
import io.terminus.trantor2.permission.props.SystemServiceProps;
import io.terminus.trantor2.permission.props.WebApiProps;
import io.terminus.trantor2.permission.props.access.control.ApiType;
import io.terminus.trantor2.permission.props.access.control.ResourceAccessControl;
import io.terminus.trantor2.utils.ApiMetaConverter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AccessControlQueryServiceImpl implements AccessControlQueryService {

    private final ApiMetaRepo apiMetaRepo;
    private final MetaQueryService metaQueryService;

    @Override
    public List<ResourceAccessControl> findPortalAccessControlResources(@NotNull ResourceAccessControlFindRequest findRequest) {
        Collection<ApiMeta> apiMetaList = findApiMetas(findRequest);
        return apiMetaList.stream().map(ApiMetaConverter.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public Paging<ResourceAccessControl> pagingPortalAccessControlResources(@NotNull ResourceAccessControlPageRequest pageRequest) {
        Paging<ApiMeta> apiMetaPaging = pagingApiMeta(pageRequest);
        if (CollectionUtils.isEmpty(apiMetaPaging.getData())) {
            return Paging.empty();
        }
        List<ResourceAccessControl> resourceAccessControlList = apiMetaPaging.getData()
                .stream().map(ApiMetaConverter.INSTANCE::convert).collect(Collectors.toList());
        return new Paging<>(apiMetaPaging.getTotal(), resourceAccessControlList);
    }

    private Cond convertToQueryCond(@NotNull ResourceAccessControlFindRequest request) {
        return convertToQueryCond(request.getPortalCode(),
                request.getOpenApi(),
                request.getResourceTypes(),
                request.getResourceKey(),
                request.getAuthenticationEnabled(),
                request.getAuthorizationEvaluateEnabled(),
                request.getKeyword());
    }

    private Cond convertToQueryCond(@NotNull ResourceAccessControlPageRequest request) {
        return convertToQueryCond(request.getPortalCode(),
                request.getOpenApi(),
                request.getResourceTypes(),
                request.getResourceKey(),
                request.getUserAuthenticationEnabled(),
                request.getUserAuthorizationEvaluateEnabled(),
                request.getKeyword());
    }

    /**
     * 将请求参数条件转换为查询条件对象
     *
     * @param portalCode                   门户编号
     * @param isOpenApi                    是否是OpenAPI
     * @param resourceTypes                资源类型
     * @param resourceKey                  资源标识
     * @param authenticationEnabled        是否开启身份认证
     * @param authorizationEvaluateEnabled 是否开启鉴权校验
     * @param keyword                      模糊检索关键字
     * @return 查询条件对象
     */
    private Cond convertToQueryCond(@NotNull String portalCode,
                                    Boolean isOpenApi,
                                    Collection<ApiType> resourceTypes,
                                    String resourceKey,
                                    Boolean authenticationEnabled,
                                    Boolean authorizationEvaluateEnabled,
                                    String keyword) {
        List<Cond> condList = new ArrayList<>();
        condList.add(Field.type().equal(MetaType.Api.name()));
        condList.add(Field.parentKey().equal(portalCode));
        if (Boolean.TRUE.equals(isOpenApi)) {
            condList.add(Field.props(Boolean.class, ApiMetaProps.Fields.openApi).equal(Boolean.TRUE));
            List<String> apiTypes = CollUtil.isNotEmpty(resourceTypes)
                    ? resourceTypes.stream().map(Enum::name).collect(Collectors.toList())
                    : Arrays.asList(ApiType.OpenService.name(), ApiType.OpenApi.name(), ApiType.OpenAIAgent.name());
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).in(apiTypes));
            if (StringUtils.isNotBlank(resourceKey)) {
                condList.add(convertQueryCondByResourceKey(resourceKey, true));
            }
            if (StringUtils.isNotBlank(keyword)) {
                condList.add(convertQueryCondByFuzzyValue(keyword, true));
            }
        } else {
            condList.add(Field.props(Boolean.class, ApiMetaProps.Fields.openApi).equal(Boolean.FALSE));
            List<String> apiTypes = CollUtil.isNotEmpty(resourceTypes)
                    ? resourceTypes.stream().map(Enum::name).collect(Collectors.toList())
                    : Arrays.asList(ApiType.SystemService.name(), ApiType.Service.name(), ApiType.Api.name(), ApiType.AIAgent.name());
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).in(apiTypes));
            if (StringUtils.isNotBlank(resourceKey)) {
                condList.add(convertQueryCondByResourceKey(resourceKey, false));
            }
            if (Objects.nonNull(authenticationEnabled)) {
                condList.add(Field.props(Boolean.class, ApiMetaProps.Fields.authenticationEnabled).equal(authenticationEnabled));
            }
            if (Objects.nonNull(authorizationEvaluateEnabled)) {
                condList.add(Field.props(Boolean.class, ApiMetaProps.Fields.authorizationEvaluateEnabled).equal(authorizationEvaluateEnabled));
            }
            if (StringUtils.isNotBlank(keyword)) {
                condList.add(convertQueryCondByFuzzyValue(keyword, false));
            }
        }
        return Cond.and(condList.toArray(new Cond[0]));
    }

    /**
     * 将请求参数条件转换为查询条件对象
     *
     * @param portalCode   门户编号
     * @param resourceType 资源类型
     * @param serviceKey   服务key
     * @param modelKey     模型key
     * @return 查询条件对象
     */
    private Cond convertToQueryCond(@NotNull String portalCode,
                                    ApiType resourceType,
                                    String serviceKey,
                                    String modelKey,
                                    String apiPath,
                                    String apiMethod) {
        List<Cond> condList = new ArrayList<>();
        condList.add(Field.type().equal(MetaType.Api.name()));
        if (Objects.nonNull(portalCode)) {
            condList.add(Field.parentKey().equal(portalCode));
        }
        condList.add(Field.props(Boolean.class, ApiMetaProps.Fields.openApi).equal(Boolean.FALSE));
        condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).equal(resourceType.name()));
        if (resourceType == ApiType.SystemService) {
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.SystemService.name())
                    .and(Field.props(String.class, SystemServiceProps.Fields.serviceKey).equal(serviceKey))
                    .and(Field.props(String.class, SystemServiceProps.Fields.modelKey).equal(modelKey))
            );
        } else if (resourceType == ApiType.Service) {
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Service.name())
                    .and(Field.props(String.class, ServiceProps.Fields.serviceKey).equal(serviceKey))
            );
        } else if (resourceType == ApiType.Api) {
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Api.name())
                    .and(Field.props(String.class, WebApiProps.Fields.httpPath).equal(apiPath))
                    .and(Field.props(String.class, WebApiProps.Fields.httpMethod).equal(apiMethod))
            );
        } else if (resourceType == ApiType.AIAgent) {
            condList.add(Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.AIAgent.name())
                    .and(Field.props(String.class, AIAgentProps.Fields.agentKey).equal(serviceKey))
            );
        }
        return Cond.and(condList.toArray(new Cond[0]));
    }

    private Cond convertQueryCondByResourceKey(@NotBlank String resourceKey, boolean isOpenApi) {
        if (isOpenApi) {
            return Cond.or(
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenService.name())
                            .and(Field.props(String.class, ServiceProps.Fields.serviceKey).equal(resourceKey)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenApi.name())
                            .and(Field.props(String.class, WebApiProps.Fields.httpPath).equal(resourceKey)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenAIAgent.name())
                            .and(Field.props(String.class, AIAgentProps.Fields.agentKey).equal(resourceKey))
            );
        } else {
            return Cond.or(
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.SystemService.name())
                            .and(Cond.or(
                                    Field.props(String.class, SystemServiceProps.Fields.serviceKey).equal(resourceKey),
                                    Field.props(String.class, SystemServiceProps.Fields.modelKey).equal(resourceKey)
                            )),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Service.name())
                            .and(Field.props(String.class, ServiceProps.Fields.serviceKey).equal(resourceKey)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Api.name())
                            .and(Field.props(String.class, WebApiProps.Fields.httpPath).equal(resourceKey)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.AIAgent.name())
                            .and(Field.props(String.class, AIAgentProps.Fields.agentKey).equal(resourceKey))
            );
        }
    }

    private Cond convertQueryCondByFuzzyValue(@NotBlank String keyword, boolean isOpenApi) {
        String fuzzyValue = "%" + keyword + "%";
        Cond fuzzyValueCond;
        if (isOpenApi) {
            fuzzyValueCond = Cond.or(
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenService.name())
                            .and(Field.props(String.class, ServiceProps.Fields.serviceKey).like(fuzzyValue)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenApi.name())
                            .and(Field.props(String.class, WebApiProps.Fields.httpPath).like(fuzzyValue)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.OpenAIAgent.name())
                            .and(Field.props(String.class, AIAgentProps.Fields.agentKey).like(fuzzyValue))
            );
        } else {
            fuzzyValueCond = Cond.or(
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.SystemService.name())
                            .and(Cond.or(
                                    Field.props(String.class, SystemServiceProps.Fields.serviceKey).like(fuzzyValue),
                                    Field.props(String.class, SystemServiceProps.Fields.modelKey).like(fuzzyValue)
                            )),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Service.name())
                            .and(Field.props(String.class, ServiceProps.Fields.serviceKey).like(fuzzyValue)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.Api.name())
                            .and(Field.props(String.class, WebApiProps.Fields.httpPath).like(fuzzyValue)),
                    Field.props(String.class, ApiMetaProps.Fields.apiType).equal(ApiType.AIAgent.name())
                            .and(Field.props(String.class, AIAgentProps.Fields.agentKey).like(fuzzyValue))
            );
        }
        return Cond.or(
                Field.key().like(fuzzyValue),
                Field.name().like(fuzzyValue),
                fuzzyValueCond
        );
    }

    @Override
    public Map<String, String> getApiMetaKeyToPermissionKeyMap(@NotEmpty Collection<ApiMeta> apiMetas) {
        Map<String, String> metaKeyToPermissionKeyMap = new HashMap<>();
        Map<ApiType, List<ApiMeta>> apiTypeToApiMetasMap = apiMetas.stream().collect(Collectors.groupingBy(apiMeta -> apiMeta.getResourceProps().getApiType()));
        apiTypeToApiMetasMap.forEach((apiType, apiMetaGroup) -> {
            if (ApiType.SystemService.equals(apiType)) {
                // 查询系统服务到权限项映射
                Map<String, String> sysServiceKeyToPermissionKeyMap = getSysServiceKeyToPermissionKeyMap(apiMetaGroup);
                apiMetaGroup.forEach(apiMeta -> {
                    SystemServiceProps props = (SystemServiceProps) apiMeta.getResourceProps();
                    metaKeyToPermissionKeyMap.put(apiMeta.getKey(),
                            sysServiceKeyToPermissionKeyMap.get(KeyUtil.assembleSysService(props.getServiceKey(), props.getModelKey())));
                });
            }
            if (ApiType.Service.equals(apiType) || ApiType.OpenService.equals(apiType)) {
                // 查询服务到权限项映射
                Map<String, String> serviceKeyToPermissionKeyMap = getServiceKeyToPermissionKeyMap(apiMetaGroup);
                apiMetaGroup.forEach(apiMeta -> {
                    ServiceProps props = (ServiceProps) apiMeta.getResourceProps();
                    metaKeyToPermissionKeyMap.put(apiMeta.getKey(), serviceKeyToPermissionKeyMap.get(props.getServiceKey()));
                });
            }
            if (ApiType.Api.equals(apiType) || ApiType.OpenApi.equals(apiType)) {
                apiMetaGroup.forEach(apiMeta -> {
                    String permissionKey = ((WebApiProps) apiMeta.getResourceProps()).getPermissionKey();
                    if (Objects.nonNull(permissionKey)) {
                        metaKeyToPermissionKeyMap.put(apiMeta.getKey(), permissionKey);
                    }
                });
            }
            if (ApiType.AIAgent.equals(apiType) || ApiType.OpenAIAgent.equals(apiType)) {
                // 查询智能体到权限项映射
                Map<String, String> aiAgentKeyToPermissionKeyMap = getAIAgentKeyToPermissionKeyMap(apiMetaGroup);
                apiMetaGroup.forEach(apiMeta -> {
                    AIAgentProps props = (AIAgentProps) apiMeta.getResourceProps();
                    metaKeyToPermissionKeyMap.put(apiMeta.getKey(), aiAgentKeyToPermissionKeyMap.get(props.getAgentKey()));
                });
            }
        });

        return metaKeyToPermissionKeyMap;
    }

    @Override
    public Paging<ApiMeta> pagingApiMeta(ResourceAccessControlPageRequest pageRequest) {
        Cond cond = convertToQueryCond(pageRequest);
        PageReq pageReq = PageReq.of(Math.max(pageRequest.getPageNo() - 1, 0), pageRequest.getPageSize(), Order.byModifiedAt().desc());
        return apiMetaRepo.findAll(cond, pageReq, ResourceContext.ctxFromThreadLocal());
    }

    @Override
    public Collection<ApiMeta> findApiMetas(ResourceAccessControlFindRequest findRequest) {
        Cond cond = convertToQueryCond(findRequest);
        return apiMetaRepo.findAll(cond, ResourceContext.ctxFromThreadLocal());
    }

    @Override
    public Collection<ApiMeta> findApiMetasByService(String portalCode, ApiType resourceType, String serviceKey, String modelKey, String apiPath, String apiMethod) {
        Cond cond = convertToQueryCond(portalCode, resourceType, serviceKey, modelKey, apiPath, apiMethod);
        return apiMetaRepo.findAll(cond, ResourceContext.ctxFromThreadLocal());
    }

    private Map<String, String> getSysServiceKeyToPermissionKeyMap(@NotEmpty Collection<ApiMeta> apiMetas) {
        Map<String, String> sysServiceKeyToPermissionKeyMap = new HashMap<>();
        List<ViewPermissionDTO.BindSysService> sysServices = apiMetas.stream().map(apiMeta -> {
            SystemServiceProps props = (SystemServiceProps) apiMeta.getResourceProps();
            ViewPermissionDTO.BindSysService sysService = new ViewPermissionDTO.BindSysService();
            sysService.setServiceKey(props.getServiceKey());
            sysService.setModelKey(props.getModelKey());
            return sysService;
        }).collect(Collectors.toList());
        List<ViewPermissionDTO.BindSysServicePermissionDTO> permissionBySysService = metaQueryService.findPermissionBySysService(EditUtil.ctxFromThreadLocal(), sysServices);
        permissionBySysService.forEach(bindSysServicePermissionDTO -> {
            if (Objects.nonNull(bindSysServicePermissionDTO.getPermissionKey())) {
                String assembleSysService = KeyUtil.assembleSysService(bindSysServicePermissionDTO.getServiceKey(), bindSysServicePermissionDTO.getModelKey());
                sysServiceKeyToPermissionKeyMap.put(assembleSysService, bindSysServicePermissionDTO.getPermissionKey());
            }
        });
        return sysServiceKeyToPermissionKeyMap;
    }

    private Map<String, String> getServiceKeyToPermissionKeyMap(@NotEmpty Collection<ApiMeta> apiMetas) {
        Map<String, String> serviceKeyToPermissionKeyMap = new HashMap<>();
        List<ViewPermissionDTO.BindService> services = apiMetas.stream().map(apiMeta -> {
            ServiceProps props = (ServiceProps) apiMeta.getResourceProps();
            ViewPermissionDTO.BindService service = new ViewPermissionDTO.BindService();
            service.setServiceKey(props.getServiceKey());
            return service;
        }).collect(Collectors.toList());
        List<ViewPermissionDTO.BindServicePermissionDTO> permissionByServiceKey = metaQueryService.findPermissionByServiceKey(EditUtil.ctxFromThreadLocal(), services);
        permissionByServiceKey.forEach(bindServicePermissionDTO -> {
            if (Objects.nonNull(bindServicePermissionDTO.getPermissionKey())) {
                serviceKeyToPermissionKeyMap.put(bindServicePermissionDTO.getServiceKey(), bindServicePermissionDTO.getPermissionKey());
            }
        });
        return serviceKeyToPermissionKeyMap;
    }

    private Map<String, String> getAIAgentKeyToPermissionKeyMap(@NotEmpty Collection<ApiMeta> apiMetas) {
        Map<String, String> aiAgentKeyToPermissionKeyMap = new HashMap<>();
        Collection<String> agentKeys = apiMetas.stream()
                .filter(apiMeta -> Arrays.asList(ApiType.AIAgent, ApiType.OpenAIAgent).contains(apiMeta.getResourceProps().getApiType()))
                .map(apiMeta -> ((AIAgentProps) apiMeta.getResourceProps()).getAgentKey())
                .collect(Collectors.toSet());
        List<ViewPermissionDTO.BindAIAgent> bindAIAgents = agentKeys.stream().map(agentKey -> {
            ViewPermissionDTO.BindAIAgent bindAIAgent = new ViewPermissionDTO.BindAIAgent();
            bindAIAgent.setAiAgentKey(agentKey);
            return bindAIAgent;
        }).collect(Collectors.toList());
        List<ViewPermissionDTO.BindAIAgentPermissionDTO> permissionByAIAgentKey
                = metaQueryService.findPermissionByAIAgent(EditUtil.ctxFromThreadLocal(), null, bindAIAgents);
        permissionByAIAgentKey.forEach(bindServicePermissionDTO -> {
            if (Objects.nonNull(bindServicePermissionDTO.getPermissionKey())) {
                aiAgentKeyToPermissionKeyMap.put(bindServicePermissionDTO.getAiAgentKey(), bindServicePermissionDTO.getPermissionKey());
            }
        });
        return aiAgentKeyToPermissionKeyMap;
    }
}
