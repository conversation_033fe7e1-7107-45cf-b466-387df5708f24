package io.terminus.trantor2.permission.impl.common.util;

import io.terminus.iam.api.request.role.DefaultRoleParams;
import io.terminus.iam.api.request.role.RoleCopyParams;
import io.terminus.iam.api.request.role.RoleCreateParams;
import io.terminus.iam.api.request.role.RoleFindParams;
import io.terminus.iam.api.request.role.RolePageParams;
import io.terminus.iam.api.request.role.RoleUpdateParams;
import io.terminus.trantor2.iam.dto.user.AssignUserRequest;
import io.terminus.trantor2.module.model.dto.role.DefaultRoleRequest;
import io.terminus.trantor2.module.model.dto.role.RoleUserRelationIncrementalRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleCopyRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleCreateRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleFindRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RolePageRequest;
import io.terminus.trantor2.permission.api.common.dto.role.RoleUpdateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * 2024/1/5 2:25 PM
 **/
@Mapper(componentModel = "spring")
public interface RoleMapper {

    RoleMapper INSTANCE = Mappers.getMapper(RoleMapper.class);

    @Mapping(target = "sourceApplicationId", source = "endpointId")
    RoleCreateParams convert(RoleCreateRequest request);

    RoleUpdateParams convert(RoleUpdateRequest request);

    @Mapping(target = "sourceApplicationIds",
            expression = "java(request.getEndpointId() != null ? java.util.Collections.singletonList(request.getEndpointId()) : null)")
    @Mapping(target = "keys",
            expression = "java(request.getKey() != null ? java.util.Collections.singletonList(request.getKey()) : null)")
    @Mapping(target = "roleTypeKeys",
            expression = "java(request.getRoleTypeKey() != null ? java.util.Collections.singletonList(request.getRoleTypeKey()) : null)")
    RoleFindParams convert(RoleFindRequest request);

    @Mapping(target = "sourceApplicationId", source = "endpointId")
    RolePageParams convert(RolePageRequest request);

    @Mapping(target = "sourceApplicationId", source = "endpointId")
    RoleCopyParams convert(RoleCopyRequest request);

    DefaultRoleParams convert(DefaultRoleRequest request);

    @Mapping(source = "deletedUserIds", target = "removeUserIds")
    @Mapping(source = "createdUserIds", target = "assignUserIds")
    AssignUserRequest convert(RoleUserRelationIncrementalRequest request);
}
