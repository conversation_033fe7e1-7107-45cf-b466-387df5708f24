package io.terminus.trantor2.permission.impl.common.ai.mcp.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.exception.TrantorRuntimeException;
import io.terminus.trantor2.common.utils.StandardPermissionKeyHelper;
import io.terminus.trantor2.iam.service.TrantorIAMPermissionAssignService;
import io.terminus.trantor2.iam.utils.IAMNamespaceGenerator;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.meta.api.dto.ViewPermissionDTO;
import io.terminus.trantor2.meta.api.dto.criteria.Field;
import io.terminus.trantor2.meta.api.service.MetaQueryService;
import io.terminus.trantor2.meta.index.MetaIndexAsset;
import io.terminus.trantor2.meta.index.MetaIndexAssetRepo;
import io.terminus.trantor2.meta.util.EditUtil;
import io.terminus.trantor2.meta.util.KeyUtil;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import io.terminus.trantor2.permission.api.common.cache.PortalToIamAppConverter;
import io.terminus.trantor2.permission.impl.common.util.PermissionUtils;
import io.terminus.trantor2.permission.props.FunctionItemPermissionProps;
import io.terminus.trantor2.scene.config.datamanager.DataManagerView;
import io.terminus.trantor2.scene.repo.ViewRepo;
import io.terminus.trantor2.service.dsl.properties.ServiceProperties;
import io.terminus.trantor2.service.management.repo.ServiceRepo;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/6/19 16:41
 **/
@RequiredArgsConstructor
public class PermissionMcpTools {

    private final MetaIndexAssetRepo metaIndexAssetRepo;
    private final ServiceRepo serviceRepo;
    private final ViewRepo viewRepo;
    private final MenuQueryService menuQueryService;
    private final MetaQueryService metaQueryService;
    private final PortalToIamAppConverter portalToIamAppConverter;
    private final TrantorIAMPermissionAssignService iamPermissionAssignService;

    @Tool(description = "获取标准操作列表")
    public Collection<String> getStandardOperations() {
        return Arrays.stream(StandardPermissionKeyHelper.Operate.values()).map(Enum::name).collect(Collectors.toList());
    }

    @Tool(description = "获取系统服务列表")
    public Collection<String> getSystemServiceKeys() {
        return Arrays.stream(StandardPermissionKeyHelper.SystemServiceKey.values()).map(Enum::name).collect(Collectors.toList());
    }

    @Tool(description = "根据模型和操作生成权限标识")
    public String generatePermissionKeyByModelAndOperate(@ToolParam(description = "模型标识/modelKey/modelAlias") String modelKey,
                                                         @ToolParam(description = "操作类型/operate，可选值范围通过 getStandardOperations tool 获取") StandardPermissionKeyHelper.Operate operate) {
        return operate.getPermissionKey(modelKey);
    }

    @Tool(description = "生成服务的权限标识，如果返回结果为null，则需要查询serviceKey元数据定义，分析后生成合适的操作权限标识")
    public String generateServicePermissionKey(@ToolParam(description = "服务标识/serviceKey") String serviceKey,
                                               @ToolParam(description = "模型标识/modelKey/modelAlias，如果serviceKey是系统服务，则必填", required = false) String modelKey) {
        if (KeyUtil.isSysShortKey(serviceKey)) {
            return StandardPermissionKeyHelper.SystemServiceKey.getPermissionKey(serviceKey, modelKey);
        } else {
            if (StandardPermissionKeyHelper.ServiceKey.isStandard(serviceKey)) {
                return StandardPermissionKeyHelper.ServiceKey.getPermissionKey(serviceKey);
            } else {
                return null;
            }
        }
    }

    @Tool(description = "查询所有标准化模型操作权限，以KV结构返回查询结果，key为模型标识，value为与模型强相关的标准化操作权限列表")
    public Map<String, Set<String>> findAllStandardPermissions(ToolContext context,
                                                               @ToolParam(description = "模型标识/modelKey/modelAlias，如果未指定具体模型，则标识查询所有模型的标准化操作权限", required = false) String modelKey) {
        List<MetaIndexAsset.Key> keys;
        if (StringUtils.isNotBlank(modelKey)) {
            keys = metaIndexAssetRepo.find(
                    TrantorContext.getTeamId(),
                    MetaIndexAsset.Key.class,
                    Field.props(String.class, FunctionItemPermissionProps.Fields.sourceModelKey).equal(modelKey)
            );
        } else {
            keys = metaIndexAssetRepo.find(
                    TrantorContext.getTeamId(),
                    MetaIndexAsset.Key.class,
                    Field.props(String.class, FunctionItemPermissionProps.Fields.sourceModelKey).isNotNull()
            );
        }
        return keys.stream().collect(Collectors.groupingBy(
                key -> key.getKey().split(":")[0],
                Collectors.mapping(MetaIndexAsset.Key::getKey, Collectors.toSet())
        ));
    }

    @Tool(description = "变更服务权限信息")
    public void replaceServicePermission(@ToolParam(description = "服务标识") String serviceKey,
                                         @ToolParam(description = "旧权限标识") String oldPermissionKey,
                                         @ToolParam(description = "新权限标识") String newPermissionKey) {
        serviceRepo.findOneByKey(serviceKey, ResourceContext.ctxFromThreadLocal()).ifPresent(serviceMeta -> {
            ServiceProperties props = serviceMeta.getResourceProps().getServiceDslJson().getProps();
            if (!Objects.equals(props.getPermissionKey(), oldPermissionKey)) {
                throw new TrantorRuntimeException(String.format("The current service permissionKey is '%s', not expected '%s', so cannot be modified to '%s'",
                        oldPermissionKey, props.getPermissionKey(), newPermissionKey));
            }
            props.setPermissionKey(newPermissionKey);
            serviceRepo.update(serviceMeta, ResourceContext.ctxFromThreadLocal());
        });
    }

    @Tool(description = "变更视图权限信息")
    public void replaceViewPermissions(@ToolParam(description = "视图标识") String viewKey,
                                       @ToolParam(description = "新旧权限数据结构Map, key为视图或按钮当前permissionKey，value为视图或按钮的推荐permissionKey") Map<String, String> permissionKeyMap) {
        if (MapUtils.isEmpty(permissionKeyMap)) {
            return;
        }
        ResourceContext ctx = ResourceContext.ctxFromThreadLocal();
        viewRepo.findOneByKey(viewKey, ctx).ifPresent(dataManagerViewMeta -> {
            DataManagerView dataManagerView = dataManagerViewMeta.getResourceProps();
            if (permissionKeyMap.containsKey(viewKey)) {
                dataManagerView.setPermissionKey(permissionKeyMap.get(dataManagerView.getPermissionKey()));
            }
            ObjectNode viewContent = dataManagerView.getContent();
            List<JsonNode> parents = viewContent.findParents("permissionKey");
            for (JsonNode parent : parents) {
                if (!parent.isMissingNode() && parent.isObject()) {
                    String oldPermKey = parent.get("permissionKey").textValue();
                    if (permissionKeyMap.containsKey(oldPermKey)) {
                        ((ObjectNode) parent).put("permissionKey", permissionKeyMap.get(oldPermKey));
                    }
                }
            }
            viewRepo.update(dataManagerViewMeta, ctx);
        });
    }

    @Tool(description = "给指定角色分配业务功能权限，业务功能权限通常指以菜单为粒度的整套业务的功能使用权限，包含菜单，菜单关联页面，页面按钮等用于完成某项完整业务所需的功能权限集合")
    public void assignMenuFunctionPermission(@ToolParam(description = "角色ID") Long roleId,
                                             @ToolParam(description = "门户key或门户code") String portalKey,
                                             @ToolParam(description = "菜单key列表") Collection<String> menuKeys) {
        List<MenuMeta> menuMetas = menuQueryService.findMenusByKeys(menuKeys)
                .stream().filter(menuMeta -> !MenuMeta.RouteType.None.equals(menuMeta.getRouteType())).toList();
        if (CollectionUtils.isEmpty(menuMetas)) {
            return;
        }
        assignMenuFunctionPermission(roleId, portalKey, menuMetas);
    }

    @Tool(description = "给指定角色分配所有菜单功能权限")
    public void assignAllMenuFunctionPermission(@ToolParam(description = "角色ID") Long roleId,
                                                @ToolParam(description = "门户key或门户code") String portalKey) {
        List<MenuMeta> menuMetas = menuQueryService.getMenuTree(portalKey)
                .stream().filter(menuMeta -> !MenuMeta.RouteType.None.equals(menuMeta.getRouteType())).toList();
        if (CollectionUtils.isEmpty(menuMetas)) {
            return;
        }
        assignMenuFunctionPermission(roleId, portalKey, menuMetas);
    }

    private void assignMenuFunctionPermission(@NotNull Long roleId,
                                              @NotNull String portalKey,
                                              @NotEmpty List<MenuMeta> menuMetas) {
        Map<String, Collection<String>> menuKeyToViewKeysMap = MenuUtils.recursiveGetMenuKeyToViewKeysMap(EditUtil.ctxFromThreadLocal(), metaQueryService, menuMetas);
        Map<String, String> menuKeyToPermKey = menuMetas.stream().collect(Collectors.toMap(MenuMeta::getKey, MenuMeta::getPermissionKey));
        Set<String> allPermKeys = new LinkedHashSet<>(menuKeyToPermKey.values());
        for (MenuMeta menuMeta : menuMetas) {
            if (!MenuMeta.RouteType.Scene.equals(menuMeta.getRouteType())) {
                continue;
            }
            allPermKeys.add(menuMeta.getPermissionKey());
            Collection<String> viewKeys = menuKeyToViewKeysMap.get(menuMeta.getKey());
            if (CollectionUtils.isEmpty(viewKeys)) {
                continue;
            }
            List<ViewPermissionDTO> viewPermissions = metaQueryService.findViewPermissions(EditUtil.ctxFromThreadLocal(), portalKey, new ArrayList<>(viewKeys));
            // 批量查询视图引用权限项：viewKey -> set of view permissionKey
            Map<String, String> viewOwnPermissionKey = new HashMap<>();
            Map<String, Set<String>> viewRefPermissionKeys = new HashMap<>();
            PermissionUtils.getViewRefPermissionKeys(viewPermissions, viewOwnPermissionKey, viewRefPermissionKeys);
            allPermKeys.addAll(viewOwnPermissionKey.values());
            allPermKeys.addAll(viewRefPermissionKeys.values().stream().flatMap(Collection::stream).toList());
            // 批量查询视图所有按钮引用权限项：buttonKey -> set of button permissionKey
            Map<String, Map<String, String>> viewKeyToButtonKeyToOwnPermissionKeyMap = new HashMap<>();
            Map<String, Map<String, Set<String>>> viewKeyToButtonKeyToRefPermissionKeysMap = new HashMap<>();
            PermissionUtils.getButtonRefPermissionKeys(viewPermissions, viewKeyToButtonKeyToOwnPermissionKeyMap, viewKeyToButtonKeyToRefPermissionKeysMap);
            allPermKeys.addAll(
                    viewKeyToButtonKeyToOwnPermissionKeyMap.values().stream().map(Map::values).flatMap(Collection::stream).toList()
            );
            allPermKeys.addAll(
                    viewKeyToButtonKeyToRefPermissionKeysMap.values().stream().map(Map::values).flatMap(Collection::stream).flatMap(Collection::stream).toList()
            );
        }

        allPermKeys.remove(null);
        if (CollectionUtils.isNotEmpty(allPermKeys)) {
            Long iamAppId = portalToIamAppConverter.getIamAppIdByPortalCode(portalKey);
            String permissionNamespace = IAMNamespaceGenerator.generatePermissionNamespace(TrantorContext.getTeamCode());
            iamPermissionAssignService.assignPermissionsToRole(roleId, iamAppId, permissionNamespace, allPermKeys);
        }
    }
}
