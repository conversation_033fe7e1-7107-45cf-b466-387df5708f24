package io.terminus.trantor2.permission.impl.common.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.iam.api.response.permission.Permission;
import io.terminus.iam.api.response.permission.PermissionDescriptor;
import io.terminus.iam.api.response.permission.PermissionResource;
import io.terminus.trantor2.permission.api.common.dto.PermissionResourceVO;
import io.terminus.trantor2.permission.api.common.dto.PermissionVO;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 2024/2/4 7:04 PM
 **/
public final class PermissionVOFactory {

    // id to PermissionVO
    public static final Cache<Long, PermissionVO> PERMISSION_VO_CACHE = Caffeine.newBuilder().expireAfterWrite(2, TimeUnit.HOURS).build();
    // // id to PermissionResourceVO
    public static final Cache<Long, PermissionResourceVO> PERMISSION_RESOURCE_VO_CACHE = Caffeine.newBuilder().expireAfterWrite(2, TimeUnit.HOURS).build();

    private PermissionVOFactory() {
    }

    public static PermissionVO toVo(PermissionDescriptor permissionDescriptor) {
        if (Objects.isNull(permissionDescriptor)) {
            return null;
        }
        if (Objects.isNull(permissionDescriptor.permissionId())) {
            return PermissionVOConverter.INSTANCE.convert(permissionDescriptor);
        }
        return PERMISSION_VO_CACHE.get(permissionDescriptor.permissionId(), pid -> PermissionVOConverter.INSTANCE.convert(permissionDescriptor));
    }

    public static PermissionVO toVo(Permission permission) {
        if (Objects.isNull(permission)) {
            return null;
        }
        if (Objects.isNull(permission.getId())) {
            return PermissionVOConverter.INSTANCE.convert(permission);
        }
        return PERMISSION_VO_CACHE.get(permission.getId(), pid -> PermissionVOConverter.INSTANCE.convert(permission));
    }

    public static PermissionResourceVO toVo(PermissionResource permissionResource) {
        if (Objects.isNull(permissionResource)) {
            return null;
        }
        if (Objects.isNull(permissionResource.getId())) {
            return PermissionVOConverter.INSTANCE.convert(permissionResource);
        }
        return PERMISSION_RESOURCE_VO_CACHE.get(permissionResource.getId(), prid -> PermissionVOConverter.INSTANCE.convert(permissionResource));
    }

}
