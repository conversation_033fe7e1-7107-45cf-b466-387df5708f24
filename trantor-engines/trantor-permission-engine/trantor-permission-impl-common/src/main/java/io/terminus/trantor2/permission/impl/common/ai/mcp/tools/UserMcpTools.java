package io.terminus.trantor2.permission.impl.common.ai.mcp.tools;

import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.iam.service.TrantorIAMUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.Collection;

/**
 * 用户相关MCP工具
 *
 * <AUTHOR>
 * 2025/6/25 20:53
 **/
@RequiredArgsConstructor
public class UserMcpTools {

    private final TrantorIAMUserService iamUserService;

    @Tool(description = "根据用户属性查询用户列表")
    public Collection<User> findAllUsers(@ToolParam(description = "用户查询参数对象", required = false) UserPagingParams params) {
        return iamUserService.findAll(params);
    }
}
