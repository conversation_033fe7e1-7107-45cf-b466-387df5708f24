package io.terminus.trantor2.permission.impl.common.cache;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * 需要一个配置参数决定加载资源权限树时是否填充权限对象信息和frontendConfig信息，为了实现最小改动，通过一个线程本地变量控制，减小对原有代码的改动，这种方式不是非常优雅
 *
 * <AUTHOR>
 * 2024/2/5 5:06 PM
 **/
public final class PropsLoadingConfigContext {

    private static final ThreadLocal<PropsLoadingConfig> THREAD_LOCAL = ThreadLocal.withInitial(() -> new PropsLoadingConfig(true, false));

    private PropsLoadingConfigContext() {
    }

    public static void enableCompletePermission() {
        THREAD_LOCAL.get().setCompletePermission(true);
    }

    public static void disableCompletePermission() {
        THREAD_LOCAL.get().setCompletePermission(false);
    }

    public static void enableCompleteFrontendConfig() {
        THREAD_LOCAL.get().setCompleteFrontendConfig(true);
    }

    public static void disableCompleteFrontendConfig() {
        THREAD_LOCAL.get().setCompleteFrontendConfig(false);
    }

    public static boolean isCompletePermission() {
        return Boolean.TRUE.equals(THREAD_LOCAL.get().getCompletePermission());
    }

    public static boolean isCompleteFrontendConfig() {
        return Boolean.TRUE.equals(THREAD_LOCAL.get().getCompleteFrontendConfig());
    }

    public static void clear() {
        if (Objects.nonNull(THREAD_LOCAL.get())) {
            THREAD_LOCAL.remove();
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class PropsLoadingConfig {

        // 是否补全权限信息
        private Boolean completePermission;

        // 是否补全frontendConfig信息
        private Boolean completeFrontendConfig;

        public PropsLoadingConfig(boolean completePermission, boolean completeFrontendConfig) {
            this.completePermission = completePermission;
            this.completeFrontendConfig = completeFrontendConfig;
        }
    }
}
