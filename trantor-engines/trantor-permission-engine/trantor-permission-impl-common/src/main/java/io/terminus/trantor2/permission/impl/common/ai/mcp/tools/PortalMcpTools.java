package io.terminus.trantor2.permission.impl.common.ai.mcp.tools;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.meta.api.dto.ResourceContext;
import io.terminus.trantor2.module.meta.MenuMeta;
import io.terminus.trantor2.module.meta.ModuleIamConfig;
import io.terminus.trantor2.module.meta.ModuleMeta;
import io.terminus.trantor2.module.repository.ModuleRepo;
import io.terminus.trantor2.module.service.ConfigurationService;
import io.terminus.trantor2.module.service.MenuQueryService;
import io.terminus.trantor2.module.util.MenuUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门户相关MCP工具
 *
 * <AUTHOR>
 * 2025/6/25 23:25
 **/
@RequiredArgsConstructor
public class PortalMcpTools {

    private final ModuleRepo moduleRepo;
    private final ConfigurationService configurationService;
    private final MenuQueryService menuQueryService;

    @Tool(description = "获取当前门户信息")
    public Portal getCurrentPortal() {
        return TrantorContext.getCurrentPortal();
    }

    @Tool(description = "获取所有门户信息")
    public Collection<Portal> findAllPortal() {
        List<ModuleMeta> portals = moduleRepo.findAllPortalByTeam(ResourceContext.ctxFromThreadLocal());
        if (CollectionUtils.isEmpty(portals)) {
            return Collections.emptyList();
        }
        Map<String, ModuleIamConfig> iamConfigMap = configurationService.queryAllModuleIamConfig(TrantorContext.getTeamId());
        return portals.stream().map(portal -> {
            ModuleIamConfig moduleIamConfig = iamConfigMap.getOrDefault(portal.getKey(), new ModuleIamConfig());
            return Portal.builder()
                    .id(portal.getId())
                    .teamId(portal.getTeamId())
                    .teamCode(TrantorContext.getTeamCode())
                    .code(portal.getKey())
                    .name(portal.getName())
                    .loginUrl(moduleIamConfig.getLoginUrl())
                    .loginCallbackUrl(moduleIamConfig.getLoginCallbackUrl())
                    .build();
        }).collect(Collectors.toList());
    }

    @Tool(description = "获取指定门户的菜单列表")
    public List<BaseMenu> findSimplePortalMenus(@ToolParam(description = "门户key或门户code") String portalKey) {
        List<MenuMeta> menuMetas = menuQueryService.getMenuTree(portalKey);
        if (CollectionUtils.isEmpty(menuMetas)) {
            return Collections.emptyList();
        }
        List<MenuMeta> flatMenuMetas = new ArrayList<>();
        MenuUtils.flatMenuMetaListRecursively(menuMetas, flatMenuMetas);
        List<MenuMeta> routeMenus = flatMenuMetas.stream().filter(menuMeta -> !MenuMeta.RouteType.None.equals(menuMeta.getRouteType())).toList();
        return BaseMenu.of(routeMenus);
    }

    @Tool(description = "分页获取指定门户的菜单列表")
    public Paging<BaseMenu> pagingSimplePortalMenus(@ToolParam(description = "门户key或门户code") String portalKey,
                                                    @ToolParam(required = false, description = "当前页码，从1开始") Integer pageNumber,
                                                    @ToolParam(required = false, description = "每页查询记录数，不指定将获取所有记录") Integer pageSize) {
        List<BaseMenu> menus = findSimplePortalMenus(portalKey);
        if (CollectionUtils.isEmpty(menus)) {
            return Paging.empty();
        }
        int fromIndex = Objects.isNull(pageNumber) || pageNumber <= 0 ? 0 : (pageNumber - 1) * pageSize;
        int toIndex = Objects.isNull(pageSize) || pageSize >= menus.size() ? menus.size() : pageNumber * pageSize;
        return new Paging<>(menus.size(), menus.subList(fromIndex, toIndex));
    }

    @Tool(description = "分页获取指定门户的菜单列表")
    public Paging<Menu> pagingPortalMenus(@ToolParam(description = "门户key或门户code") String portalKey,
                                          @ToolParam(required = false, description = "当前页码，从1开始") Integer pageNumber,
                                          @ToolParam(required = false, description = "每页查询记录数，不指定将获取所有记录") Integer pageSize) {
        List<MenuMeta> menuMetas = menuQueryService.getMenuTree(portalKey);
        List<Menu> menus = Menu.of(menuMetas);
        if (CollectionUtils.isEmpty(menus)) {
            return Paging.empty();
        }
        int fromIndex = Objects.isNull(pageNumber) || pageNumber <= 0 ? 0 : (pageNumber - 1) * pageSize;
        int toIndex = Objects.isNull(pageSize) || pageSize >= menus.size() ? menus.size() : pageNumber * pageSize;
        return new Paging<>(menus.size(), menus.subList(fromIndex, toIndex));
    }

    @Data
    public static class BaseMenu implements Serializable {
        @Schema(description = "标识")
        private String key;
        @Schema(description = "名称")
        private String label;

        public static BaseMenu of(MenuMeta menuMeta) {
            if (Objects.isNull(menuMeta)) {
                return null;
            }
            BaseMenu menu = new BaseMenu();
            menu.setKey(menuMeta.getKey());
            menu.setLabel(menuMeta.getLabel());
            return menu;
        }

        public static List<BaseMenu> of(Collection<MenuMeta> menuMetas) {
            if (Objects.isNull(menuMetas)) {
                return null;
            }
            return menuMetas.stream().map(BaseMenu::of).collect(Collectors.toList());
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Menu implements Serializable {
        @Schema(description = "标识")
        private String key;
        @Schema(description = "名称")
        private String label;
        @Schema(description = "菜单描述")
        private String description;
        @Schema(description = "权限项key")
        private String permissionKey;
        @Schema(description = "子节点")
        private List<Menu> children;

        public static Menu of(MenuMeta menuMeta) {
            if (Objects.isNull(menuMeta)) {
                return null;
            }
            Menu menu = new Menu();
            menu.setKey(menuMeta.getKey());
            menu.setLabel(menuMeta.getLabel());
            menu.setDescription(menuMeta.getDescription());
            menu.setPermissionKey(menuMeta.getPermissionKey());
            menu.setChildren(of(menuMeta.getChildren()));
            return menu;
        }

        public static List<Menu> of(Collection<MenuMeta> menuMetas) {
            if (Objects.isNull(menuMetas)) {
                return null;
            }
            return menuMetas.stream().map(Menu::of).collect(Collectors.toList());
        }
    }
}
