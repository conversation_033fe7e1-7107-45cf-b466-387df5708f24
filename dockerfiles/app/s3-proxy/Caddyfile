:80 {
    reverse_proxy {env.UPSTREAM}

    @options method OPTIONS
    handle @options {
        header Access-Control-Allow-Origin "*"
        header Access-Control-Allow-Methods "GET, POST, PUT, OPTIONS"
        header Access-Control-Allow-Headers "Origin, Authorization, Accept, Content-Type, X-Requested-With, Content-Disposition"
        respond "" 204
    }

    header Access-Control-Allow-Origin "*"
    header Access-Control-Allow-Methods "GET, POST, PUT, OPTIONS"
    header Access-Control-Allow-Headers "Origin, Authorization, Accept, Content-Type, X-Requested-With, Content-Disposition"
}
