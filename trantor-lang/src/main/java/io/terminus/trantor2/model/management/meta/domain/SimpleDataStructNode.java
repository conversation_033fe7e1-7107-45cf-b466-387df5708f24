package io.terminus.trantor2.model.management.meta.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.model.management.meta.consts.DataStructType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SimpleDataStructNode implements Serializable {
    private String key;
    private String name;
    private String mainField;

    private List<SimpleDataStructFieldNode> fields = new ArrayList<>();
    private List<SimpleDataStructNode> relationModels = new ArrayList<>();

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class SimpleDataStructFieldNode {
        private String key;
        private String name;
        private String alias;
        private String type;
        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private boolean required;
        private DataDictProperties dictProps;
        private String relKey;
        private String relName;
    }
}