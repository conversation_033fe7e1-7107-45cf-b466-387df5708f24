package io.terminus.trantor2.model.management.meta.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.terminus.trantor2.model.management.meta.consts.FieldType;
import io.terminus.trantor2.model.management.meta.domain.search.SearchModelFieldConfigMeta;
import io.terminus.trantor2.model.management.meta.domain.statistic.field.StatisticFieldConfig;
import io.terminus.trantor2.model.management.meta.enums.ModelRelationTypeEnum;
import io.terminus.trantor2.model.management.meta.util.DateUtils;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/2/22
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldNameConstants
public class DataStructFieldProperties extends BaseProperties {

    private static final long serialVersionUID = -1322290793291857651L;
    /**
     * 字段类型
     */
    private FieldType fieldType;

    /**
     * 是否必填
     */
    private boolean required;

    /**
     * 是否唯一
     */
    private boolean unique;

    /**
     * 主键
     */
    private boolean compositeKey;

    /**
     * 是否系统字段
     */
    private Boolean isSystemField = false;

    /**
     * 是否自动生成
     */
    private boolean autoGenerated;

    /**
     * 默认值
     */
    private Object defaultValue;

    /**
     * 注释
     */
    private String comment;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 长度
     */
    private Integer length;

    /**
     * 整数位
     */
    private Integer intLength;

    /**
     * 数字类型字段的显示类型：货币/数量
     */
    private String numberDisplayType;

    /**
     * 精度
     */
    private Integer scale;

    /**
     * 关联关系
     */
    private RelationMeta relationMeta;

    /**
     * 枚举属性
     */
    private DataDictProperties dictPros;

    /**
     * 多选属性
     */
    private AttachmentProperties attachmentProps;

    /**
     * 是否加密
     */
    private boolean encrypted;

    /**
     * 脱敏规则
     */
    private String desensitizedRule;

    /**
     * 标识该字段是否为关系表内用于关联两个模型的字段，当模型为关系模型时，默认会生成两个字段串联两个业务模型。
     * 前端需要根据此标识来特殊处理展示(主模型字段关联关系模型，最后显示时要显示关系模型对应的子模型数据)，关系模型内用户可额外添加其他字段。
     */
    private boolean relationField;

    /**
     * 字段级别搜索引擎相关配置
     */
    private SearchModelFieldConfigMeta searchModelFieldConfigMeta;

    /**
     * 统计模型字段级别配置
     */
    private StatisticFieldConfig statisticFieldConfig;

    /**
     * 模型字段是否开启业务多语言开关
     */
    private boolean i18nEnabled;

    @JsonIgnore
    public boolean isLink() {
        return fieldType == FieldType.OBJECT
                && relationMeta != null
                && relationMeta.getRelationType() == ModelRelationTypeEnum.LINK;
    }

    @JsonIgnore
    public boolean isToMany() {
        return fieldType == FieldType.OBJECT
                && relationMeta != null
                && relationMeta.getRelationType() == ModelRelationTypeEnum.PARENT_CHILD;
    }


    public Object fetchDefaultValue() {
        if (defaultValue == null) {
            return null;
        }
        if (defaultValue instanceof Date) {
            return "'" + DateUtils.formatDate((Date) defaultValue) + "'";
        } else if (defaultValue instanceof String) {
            return "'" + defaultValue + "'";
        } else if (defaultValue instanceof Boolean) {
            // 由于存储用的tinyint，所以ddl时直接设置为1或者0
            return defaultValue == Boolean.TRUE ? 1 : 0;
        } else if (defaultValue instanceof Long && fieldType == FieldType.DATE) {
            return "'" + DateUtils.parseLongToDate((Long) defaultValue) + "'";
        }
        return defaultValue;
    }

    public Object fetchOriginDefaultValue() {
        if (defaultValue == null) {
            return null;
        }
        if (defaultValue instanceof Date) {
            return DateUtils.formatDate((Date) defaultValue);
        } else if (defaultValue instanceof String) {
            return defaultValue;
        } else if (defaultValue instanceof Boolean) {
            // 由于存储用的tinyint，所以ddl时直接设置为1或者0
            return defaultValue == Boolean.TRUE ? 1 : 0;
        } else if (defaultValue instanceof Long && fieldType == FieldType.DATE) {
            return DateUtils.parseLongToDate((Long) defaultValue);
        }
        return defaultValue;
    }

}
