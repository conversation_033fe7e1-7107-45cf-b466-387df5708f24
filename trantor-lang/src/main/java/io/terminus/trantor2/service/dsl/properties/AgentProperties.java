package io.terminus.trantor2.service.dsl.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import io.terminus.trantor2.service.dsl.properties.knowledgebase.AgentKnowledgeBase;
import io.terminus.trantor2.service.dsl.properties.trigger.AgentTrigger;
import io.terminus.trantor2.service.dsl.properties.validation.Validator;
import io.terminus.trantor2.service.dsl.properties.validation.ValidatorContext;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * AgentProperties
 *
 * <AUTHOR> Created on 2025/3/15 23:59
 */
@Setter
@Getter
@Schema(title = "Agent属性", description = "Agent属性")
public class AgentProperties extends ServiceProperties implements Validator, Serializable {
    private static final long serialVersionUID = -9171600114857814812L;

    /**
     * agent头像
     */
    private String avatar;

    /**
     * 大模型
     */
    private LlmModel model;

    /**
     * 根据用户语言回复
     */
    private Boolean replyWithUserLanguage;

    /**
     * 欢迎词
     */
    private String greetings;

    /**
     * 欢迎词关联工具
     */
    private List<SkillTool> greetingsRelatedTools;

    /**
     * 开场白预置问题
     */
    private List<String> userQuestions;

    /**
     * 快捷问题
     */
    private List<QuickQuestion> quickQuestions;

    /**
     * 用户问题建议开关，如果开，则每次都返回3个用户建议问题
     */
    private boolean userQuestionsSuggest;

    /**
     * 是否用户自定义提示词
     */
    private boolean userQuestionsCustom;

    /**
     * 用户问题建议提示词
     */
    private String userQuestionsSuggestionPrompt;

    /**
     * Agent提示词
     */
    private String systemPrompt;

    /**
     * 技能列表
     */
    private List<Skill> skills;

    /**
     * 技能工具
     *
     * @deprecated 放入到Skill中
     */
    private List<SkillTool> skillTools;

    /**
     * 触发器
     *
     * @deprecated 放入到Skill中
     */
    private List<AgentTrigger> triggers;

    /**
     * 入参结构
     */
    private List<Field> input;

    /**
     * 关联模型列表
     */
    private List<RelatedModel> relatedModels;

    /**
     * 推理框架
     */
    private String reasoningFramework;

    /**
     * 是否需要验证结果
     */
    private Boolean checkResult;

    /**
     * 权限项key
     */
    private String permissionKey;

    /**
     * 数据访问权限范围
     *
     * @deprecated 放入到skills中
     */
    private List<AgentDataScope> dataScopes;

    /**
     * 知识库
     *
     * @deprecated 放入到Skill中
     */
    private AgentKnowledgeBase knowledgeBase;

    /**
     * 术语映射预览结果(结构化的术语映射最后转化为文本，LLM 感知的内容是最终映射预览结果)
     */
    private String termMappings;

    /**
     * 创建会话时的钩子
     */
    private AgentHook hookForSessionCreated;
    /**
     * 发送消息前的钩子
     */
    private AgentHook hookForBeforeSendMessage;
    /**
     * 发送消息后的钩子
     */
    private AgentHook hookForAfterSendMessage;

    /**
     * 长期记忆配置
     */
    private AgentLongTermMemoryConfig longTermMemoryConfig = new AgentLongTermMemoryConfig();

    @Override
    public void validate(ValidatorContext errorContext) {
        if (model == null) {
            errorContext.addErrorInfo("大模型未配置");
        } else {
            if (model.getModelPublisher() == null) {
                errorContext.addErrorInfo("大模型开发商未配置");
            }
            if (model.getName() == null) {
                errorContext.addErrorInfo("大模型未配置");
            }
        }

        if (systemPrompt == null) {
            errorContext.addErrorInfo("提示词未配置");
        }

        if (input != null) {
            errorContext.addTag("变量");
            input.forEach(i -> i.validate(errorContext));
            errorContext.removeTag();
        }

        if (userQuestionsSuggest) {
            if (userQuestionsSuggestionPrompt != null) {
                errorContext.addErrorInfo("用户问题建议提示词未配置");
            }
        }

        longTermMemoryConfig.getMemoryVariables().forEach(i -> {
            if (i.getName() != null && i.getName().length() > 64) {
                errorContext.addErrorInfo("长期记忆变量名称长度不能超过64：" + i.getName());
            }
            if (i.getDescription() != null && i.getDescription().length() > 128) {
                errorContext.addErrorInfo("长期记忆变量描述长度不能超过128：" + i.getDescription());
            }
        });
    }
}
