package io.terminus.trantor2.console.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@Schema(description = "Console")
public class Console {
    @Schema(description = "是否开启中心市场开发模式，开启后可以发布本地应用至中心市场")
    private Boolean publicMarketDevMode;

    @Schema(description = "是否开启本地市场开发模式，开启后可以发布本地应用至本地市场应用")
    private Boolean localMarketDevMode;
}
